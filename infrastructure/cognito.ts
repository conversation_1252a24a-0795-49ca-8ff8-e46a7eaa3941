import * as aws from '@pulumi/aws';
import * as pulumi from '@pulumi/pulumi';
import { stageName } from './config';

export function createCognitoUserPool(name: string, postConfirmationLambda: aws.lambda.Function) {
  // Get Google OAuth credentials from config
  const config = new pulumi.Config();
  const googleClientId = config.require('googleClientId');
  const googleClientSecret = config.requireSecret('googleClientSecret');

  // Create Cognito User Pool
  const userPool = new aws.cognito.UserPool(`${stageName}-${name}`, {
    usernameAttributes: ['email'],
    autoVerifiedAttributes: ['email'],
    accountRecoverySetting: {
      recoveryMechanisms: [
        {
          name: 'verified_email',
          priority: 1,
        },
      ],
    },
    passwordPolicy: {
      minimumLength: 8,
      requireLowercase: true,
      requireNumbers: true,
      requireSymbols: false,
      requireUppercase: false,
    },
    schemas: [
      {
        attributeDataType: 'String',
        name: 'email',
        required: true,
        mutable: true,
        stringAttributeConstraints: {
          minLength: '0',
          maxLength: '2048',
        },
      },
    ],
    lambdaConfig: {
      postConfirmation: postConfirmationLambda.arn,
    },
  });

  // Add Lambda permission for Cognito
  new aws.lambda.Permission(`${stageName}-postConfirmationPermission`, {
    action: 'lambda:InvokeFunction',
    function: postConfirmationLambda.name,
    principal: 'cognito-idp.amazonaws.com',
    sourceArn: userPool.arn,
  });

  // Create Identity Provider for Google
  const googleProvider = new aws.cognito.IdentityProvider('googleProvider', {
    userPoolId: userPool.id,
    providerName: 'Google',
    providerType: 'Google',
    providerDetails: {
      client_id: googleClientId,
      client_secret: googleClientSecret,
      authorize_scopes: 'email profile openid',
    },
    attributeMapping: {
      email: 'email',
      given_name: 'given_name',
      family_name: 'family_name',
    },
  });

  // Create User Pool Client
  const userPoolClient = new aws.cognito.UserPoolClient(
    `${stageName}-userPoolClient`,
    {
      userPoolId: userPool.id,
      generateSecret: false,
      explicitAuthFlows: [
        'ALLOW_USER_PASSWORD_AUTH',
        'ALLOW_USER_SRP_AUTH',
        'ALLOW_REFRESH_TOKEN_AUTH',
      ],
      supportedIdentityProviders: ['COGNITO', 'Google'],
      allowedOauthFlowsUserPoolClient: true,
      allowedOauthFlows: ['code'],
      allowedOauthScopes: ['email', 'openid', 'profile'],
      callbackUrls: [
        'http://localhost:8081/',
        'jfg://callback',
        'https://master.d1673favy6fst1.amplifyapp.com/',
      ], // Replace with your app URLs
      logoutUrls: [
        'http://localhost:8081/',
        'jfg://callback',
        'https://master.d1673favy6fst1.amplifyapp.com/',
      ], // Replace with your app URLs
    },
    { dependsOn: [googleProvider] }
  );

  // Create Domain for hosted UI
  new aws.cognito.UserPoolDomain(`${stageName}-userPoolDomain`, {
    domain: `${stageName}-auth-domain`, // Must be unique across AWS
    userPoolId: userPool.id,
  });

  // Export the IDs as Pulumi outputs
  return {
    userPool,
    userPoolClient,
    userPoolId: userPool.id,
    userPoolClientId: userPoolClient.id,
  };
}

export function createCognitoIdentityPool(
  userPool: aws.cognito.UserPool,
  userPoolClient: aws.cognito.UserPoolClient
) {
  // Create Identity Pool
  const identityPool = new aws.cognito.IdentityPool(`${stageName}-identity-pool`, {
    identityPoolName: `${stageName}_identity_pool`,
    allowUnauthenticatedIdentities: true,
    cognitoIdentityProviders: [
      {
        clientId: userPoolClient.id,
        providerName: userPool.endpoint,
        serverSideTokenCheck: false,
      },
    ],
  });

  // Create IAM role for unauthenticated users (guests)
  const unauthenticatedRole = new aws.iam.Role(`${stageName}-unauthenticated-role`, {
    assumeRolePolicy: identityPool.id.apply((id) =>
      JSON.stringify({
        Version: '2012-10-17',
        Statement: [
          {
            Effect: 'Allow',
            Principal: {
              Federated: 'cognito-identity.amazonaws.com',
            },
            Action: 'sts:AssumeRoleWithWebIdentity',
            Condition: {
              StringEquals: {
                'cognito-identity.amazonaws.com:aud': id,
              },
              'ForAnyValue:StringLike': {
                'cognito-identity.amazonaws.com:amr': 'unauthenticated',
              },
            },
          },
        ],
      })
    ),
  });

  // Create IAM role for authenticated users
  const authenticatedRole = new aws.iam.Role(`${stageName}-authenticated-role`, {
    assumeRolePolicy: identityPool.id.apply((id) =>
      JSON.stringify({
        Version: '2012-10-17',
        Statement: [
          {
            Effect: 'Allow',
            Principal: {
              Federated: 'cognito-identity.amazonaws.com',
            },
            Action: 'sts:AssumeRoleWithWebIdentity',
            Condition: {
              StringEquals: {
                'cognito-identity.amazonaws.com:aud': id,
              },
              'ForAnyValue:StringLike': {
                'cognito-identity.amazonaws.com:amr': 'authenticated',
              },
            },
          },
        ],
      })
    ),
  });

  // Attach roles to identity pool
  new aws.cognito.IdentityPoolRoleAttachment(`${stageName}-identity-pool-roles`, {
    identityPoolId: identityPool.id,
    roles: {
      authenticated: authenticatedRole.arn,
      unauthenticated: unauthenticatedRole.arn,
    },
  });

  return {
    identityPool,
    identityPoolId: identityPool.id,
    authenticatedRole,
    unauthenticatedRole,
  };
}
