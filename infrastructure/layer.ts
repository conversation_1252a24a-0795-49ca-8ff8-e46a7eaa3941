import * as aws from '@pulumi/aws';
import * as pulumi from '@pulumi/pulumi';
import { stageName } from './config';

/**
 * Creates a Lambda Layer containing common dependencies like MikroORM, AWS SDK, and middleware
 * This significantly reduces individual lambda bundle sizes
 */
export function createLambdaLayer(): aws.lambda.LayerVersion {
  const layerName = `${stageName}-jfg-common-layer`;
  
  // Create the layer from the layer directory
  const layer = new aws.lambda.LayerVersion(layerName, {
    layerName: layerName,
    code: new pulumi.asset.FileArchive('../layer'),
    compatibleRuntimes: [aws.lambda.Runtime.NodeJS20dX],
    description: 'Common dependencies for JFG backend lambdas including MikroORM, AWS SDK, and middleware',
    licenseInfo: 'MIT',
  });

  return layer;
}

/**
 * Gets the ARN of the lambda layer for use in lambda functions
 */
export function getLayerArn(layer: aws.lambda.LayerVersion): pulumi.Output<string> {
  return layer.arn;
}
