import { Queue } from '@pulumi/aws/sqs';
import { createLambdaErrorLogAlarm } from '../cloudwatch';
import { sqsBatchWindowMaximum, sqsBatchWindowMinimum } from '../config';
import { createLambdaFunction } from '../lambda';
import { addLambdaPermissionForSQS, addQueueReadPolicyToRole } from '../queue';
import { createCloudWatchAlarmTopic, createMonitoredEventSourceMapping } from '../queueMonitoring';

export interface PlayerResourcesConfig {
  playerQueue: Queue;
  playerDLQ: Queue;
  unattachedPlayersQueue: Queue;
  unattachedPlayersDLQ: Queue;
}

export function createPlayerResources(config: PlayerResourcesConfig) {
  // Create an SNS topic for CloudWatch alarms
  const errorAlarmTopic = createCloudWatchAlarmTopic('player-error');

  let playerQueueRole = addQueueReadPolicyToRole('playerQueue', config.playerQueue);

  const [generatePlayerLambda, generatePlayerLogGroup] = createLambdaFunction(
    'generatePlayer',
    '../dist/generate/player',
    'index.handler',
    {
      QUEUE_URL: config.playerQueue.url,
    },
    playerQueueRole,
    undefined,
    {
      memorySize: 256,
      timeout: 60,
    }
  );

  createLambdaErrorLogAlarm(
    generatePlayerLambda,
    {
      name: 'generate-player',
      description: 'Alarm for error logs in generate player Lambda function',
      alarmActions: [errorAlarmTopic.arn],
    },
    generatePlayerLogGroup
  );
  addLambdaPermissionForSQS('generatePlayer', generatePlayerLambda, config.playerQueue);

  // Add SQS Queue Event Source Mapping to Lambda with monitoring
  createMonitoredEventSourceMapping(
    'generatePlayer',
    generatePlayerLambda,
    config.playerQueue,
    config.playerDLQ,
    10, // batchSize
    sqsBatchWindowMinimum, // maximumBatchingWindowInSeconds
    {
      functionResponseTypes: ['ReportBatchItemFailures'],
    }, // additionalConfig
    [errorAlarmTopic.arn] // alarmActions
  );

  const unattachedPlayersQueueRole = addQueueReadPolicyToRole(
    'unattachedPlayersQueue',
    config.unattachedPlayersQueue
  );

  const [unattachedPlayersLambda] = createLambdaFunction(
    'unattachedPlayersHandler',
    '../dist/generate/unattached-players',
    'index.handler',
    {
      QUEUE_URL: config.unattachedPlayersQueue.url,
    },
    unattachedPlayersQueueRole,
    undefined,
    {
      memorySize: 256,
    }
  );

  addLambdaPermissionForSQS(
    'unattachedPlayersHandler',
    unattachedPlayersLambda,
    config.unattachedPlayersQueue
  );

  // Add SQS Queue Event Source Mapping to Lambda with monitoring
  createMonitoredEventSourceMapping(
    'unattachedPlayersHandler',
    unattachedPlayersLambda,
    config.unattachedPlayersQueue,
    config.unattachedPlayersDLQ,
    10, // batchSize
    sqsBatchWindowMaximum, // maximumBatchingWindowInSeconds
    undefined, // additionalConfig
    [errorAlarmTopic.arn] // alarmActions
  );

  const [getTransferListPlayersLambda] = createLambdaFunction(
    'getTransferListPlayersHandler',
    '../dist/player/getTransferListPlayers',
    'index.handler',
    undefined,
    undefined,
    undefined,
    {
      memorySize: 256,
      timeout: 30,
    }
  );

  // My Bid Transfer List Players API
  const [getMyBidTransferListPlayersLambda] = createLambdaFunction(
    'getMyBidTransferListPlayersHandler',
    '../dist/player/getMyBidTransferListPlayers',
    'index.handler'
  );

  const [useMagicSpongeLambda] = createLambdaFunction(
    'useMagicSpongeHandler',
    '../dist/player/useMagicSponge',
    'index.handler'
  );

  const [useRedCardAppealLambda] = createLambdaFunction(
    'useRedCardAppealHandler',
    '../dist/player/useRedCardAppeal',
    'index.handler'
  );

  const [getPlayerStatsLambda] = createLambdaFunction(
    'getPlayerStatsHandler',
    '../dist/player/getPlayerStats',
    'index.handler'
  );

  return {
    playerLambda: generatePlayerLambda,
    unattachedPlayersLambda,
    getTransferListPlayersLambda,
    getMyBidTransferListPlayersLambda,
    useMagicSpongeLambda,
    useRedCardAppealLambda,
    getPlayerStatsLambda,
    errorAlarmTopic,
  };
}
