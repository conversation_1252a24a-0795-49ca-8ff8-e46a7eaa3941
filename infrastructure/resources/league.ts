import { createLambdaFunction } from '../lambda';

interface LeagueResourcesConfig {}

export function createLeagueResources(_: LeagueResourcesConfig) {
  const [getLeaguesLambda] = createLambdaFunction(
    'getLeaguesHandler',
    '../dist/league/getLeagues',
    'index.handler'
  );

  const [getLeagueLambda] = createLambdaFunction(
    'getLeagueHandler',
    '../dist/league/getLeague',
    'index.handler'
  );

  return { getLeaguesLambda, getLeagueLambda };
}
