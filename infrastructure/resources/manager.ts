import { Queue } from '@pulumi/aws/sqs';
import { createLambdaErrorLogAlarm } from '../cloudwatch';
import { sqsBatchWindowMinimum } from '../config';
import { createScheduledRule } from '../eventBridge';
import { createLambdaFunction } from '../lambda';
import {
  addQueueReadPolicyToRole,
  addQueueSendPolicyToRole,
  createDLQ,
  createQueue,
} from '../queue';
import { createCloudWatchAlarmTopic, createMonitoredEventSourceMapping } from '../queueMonitoring';

export interface ManagerResourcesConfig {
  playerQueue: Queue;
}

export function createManagerResources(config: ManagerResourcesConfig) {
  // Create manager queue
  const createManagerDlq = createDLQ('createManagerDLQ');
  const createManagerQueue = createQueue('createManager', createManagerDlq);

  // Post confirmation Lambda
  let createManagerQueueSendRole = addQueueSendPolicyToRole(
    'createManagerQueueSendRole',
    createManagerQueue
  );

  const [postConfirmationLambda] = createLambdaFunction(
    'postConfirmation',
    '../dist/manager/requestNewManagerOnSignup',
    'index.handler',
    {
      MANAGER_QUEUE_URL: createManagerQueue.url,
    },
    createManagerQueueSendRole
  );

  const [getManagerLambda] = createLambdaFunction(
    'getManagerHandler',
    '../dist/manager/getManager',
    'index.handler',
    undefined,
    undefined,
    undefined,
    {
      memorySize: 256,
      timeout: 30,
    }
  );

  // Create manager Lambda to process queue messages
  let createManagerQueueReadRole = addQueueReadPolicyToRole(
    'createManagerQueueReadRole',
    createManagerQueue
  );

  const [createManagerLambda] = createLambdaFunction(
    'createManagerHandler',
    '../dist/manager/createAndAssignNewManager',
    'index.handler',
    undefined,
    createManagerQueueReadRole,
    undefined,
    {
      memorySize: 256,
      timeout: 120,
    }
  );

  // Create event source mapping to connect the Lambda to the queue
  const createManagerEventSourceMapping = createMonitoredEventSourceMapping(
    'createManager',
    createManagerLambda,
    createManagerQueue,
    createManagerDlq,
    10, // batchSize
    sqsBatchWindowMinimum,
    {
      functionResponseTypes: ['ReportBatchItemFailures'],
    }
  );

  // Create update manager name Lambda
  const [updateManagerNameLambda] = createLambdaFunction(
    'updateManagerNameHandler',
    '../dist/manager/updateManagerName',
    'index.handler'
  );

  const [updateNotificationPreferencesLambda] = createLambdaFunction(
    'updateNotificationPreferencesHandler',
    '../dist/manager/updateNotificationPreferences',
    'index.handler'
  );

  // Create get inbox messages Lambda
  const [getInboxMessagesLambda] = createLambdaFunction(
    'getInboxMessagesHandler',
    '../dist/inbox/getInboxMessages',
    'index.handler'
  );

  const [getRewardsLambda] = createLambdaFunction(
    'getRewardsHandler',
    '../dist/manager/dailyReward',
    'index.handler',
    undefined,
    undefined,
    undefined,
    {
      memorySize: 256,
      timeout: 30,
    }
  );

  const [unsubscribeLambda] = createLambdaFunction(
    'unsubscribeHandler',
    '../dist/manager/unsubscribe',
    'index.handler'
  );

  // Create idle manager processing Lambda
  const [processIdleManagersLambda, processIdleManagersLogGroup] = createLambdaFunction(
    'processIdleManagers',
    '../dist/manager/processIdleManagers',
    'index.handler',
    undefined,
    undefined,
    undefined,
    {
      memorySize: 512,
      timeout: 900, // 15 minutes to handle multiple gameworlds
    }
  );

  // Create an SNS topic for CloudWatch alarms
  const idleManagerErrorAlarmTopic = createCloudWatchAlarmTopic('idle-manager-error');

  // Create CloudWatch alarm for idle manager processing errors
  createLambdaErrorLogAlarm(
    processIdleManagersLambda,
    {
      name: 'process-idle-managers',
      description: 'Alarm for error logs in process idle managers Lambda function',
      alarmActions: [idleManagerErrorAlarmTopic.arn],
    },
    processIdleManagersLogGroup
  );

  // Create EventBridge rule to trigger the lambda at 2am UTC daily
  createScheduledRule({
    name: 'process-idle-managers',
    description: 'Trigger idle manager processing at 2am UTC every day',
    scheduleExpression: 'cron(0 2 * * ? *)', // 2am UTC daily
    lambda: processIdleManagersLambda,
  });

  return {
    postConfirmationLambda,
    getManagerLambda,
    createManagerLambda,
    createManagerEventSourceMapping,
    updateManagerNameLambda,
    updateNotificationPreferencesLambda,
    getInboxMessagesLambda,
    getRewardsLambda,
    unsubscribeLambda,
    processIdleManagersLambda,
    idleManagerErrorAlarmTopic,
  };
}
