import * as pulumi from '@pulumi/pulumi';
import { createCloudFrontDistribution } from '../cloudfront';
import { stageName } from '../config';
import { createEC2Infrastructure } from '../ec2';
import { createEC2InfrastructureOptimized } from '../ec2-cost-optimized';
import { createMonitoringResources } from '../monitoring';

export function createMonolithResources(
  alarmEmail: string,
  fixtureDetailTable: any,
  costOptimized: boolean = true
) {
  // Only create EC2 infrastructure for non-dev environments
  // Dev environment will continue to use serverless for now
  if (stageName === 'dev') {
    return {
      ec2Resources: null,
      monitoringResources: null,
      cloudFrontResources: null,
    };
  }

  // Use cost-optimized infrastructure (no ALB) by default
  const ec2Resources = costOptimized
    ? createEC2InfrastructureOptimized({ fixtureDetailTable })
    : createEC2Infrastructure({ fixtureDetailTable });

  // Create monitoring resources (simplified for cost-optimized version)
  const monitoringResources = costOptimized
    ? createMonitoringResourcesOptimized(
        ec2Resources.instance.id as pulumi.Input<string>,
        alarmEmail
      )
    : createMonitoringResources(
        ec2Resources.instance.id as pulumi.Input<string>,
        (ec2Resources as any).targetGroup?.arn as pulumi.Input<string>,
        alarmEmail
      );

  // Create CloudFront distribution for routing (direct to EC2 if cost-optimized)
  const cloudFrontResources = createCloudFrontDistribution(
    costOptimized
      ? ec2Resources.instance.publicDns
      : (ec2Resources as any).applicationLoadBalancer?.dnsName
  );

  return {
    ec2Resources,
    monitoringResources,
    cloudFrontResources,
  };
}

// Simplified monitoring for cost-optimized version (no ALB health checks)
function createMonitoringResourcesOptimized(instanceId: pulumi.Input<string>, alarmEmail: string) {
  return createMonitoringResources(instanceId, undefined, alarmEmail);
}
