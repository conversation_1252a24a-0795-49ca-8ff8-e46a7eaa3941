import * as pulumi from '@pulumi/pulumi';
import { createLambdaFunction } from '../lambda';

export function createAdminResources() {
  const config = new pulumi.Config();
  const notionApiKey = config.require('notionApiKey');
  const notionDbId = config.requireSecret('notionDbId');

  const [feedbackLambda] = createLambdaFunction(
    'feedbackHandler',
    '../dist/admin/feedback',
    'index.handler',
    {
      NOTION_API_KEY: notionApiKey,
      NOTION_ISSUE_TRACKER_DB_ID: notionDbId,
    }
  );

  const [versionCheckLambda] = createLambdaFunction(
    'versionCheckHandler',
    '../dist/admin/versionCheck',
    'index.handler'
  );

  return {
    feedbackLambda,
    versionCheckLambda,
  };
}
