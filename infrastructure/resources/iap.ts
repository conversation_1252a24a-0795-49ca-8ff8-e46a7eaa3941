import { createLambdaErrorLogAlarm } from '../cloudwatch';
import { createLambdaFunction } from '../lambda';
import { createCloudWatchAlarmTopic } from '../queueMonitoring';

export function createIapResources() {
  // Create an SNS topic for CloudWatch alarms
  const errorAlarmTopic = createCloudWatchAlarmTopic('iap-error');

  // Create Lambda for returning product rewards
  const [getIAPRewardsLambda] = createLambdaFunction(
    'getIAPRewards',
    '../dist/iap/getIapRewards',
    'index.handler'
  );

  // Create Lambda for processing IAP purchases
  const [iapLambda, iapLogGroup] = createLambdaFunction(
    'iapHandler',
    '../dist/iap/webhook',
    'index.handler'
  );

  createLambdaErrorLogAlarm(
    iapLambda,
    {
      name: 'iap-webhook',
      description: 'Alarm for error logs in iap webhook Lambda function',
      alarmActions: [errorAlarmTopic.arn],
    },
    iapLogGroup
  );

  return {
    iapLambda,
    getIAPRewardsLambda,
    errorAlarmTopic,
  };
}
