import { RouteArgs } from '@pulumi/aws-apigateway/types/input';
import { Function } from '@pulumi/aws/lambda';
import { stageName } from '../config';
import { addLambdaInvokePolicyToRole, createLambdaFunction } from '../lambda';
import { addQueueSendPolicyToRole } from '../queue';

export function createDevResources({
  getFixturesLambda,
  fixtureQueue,
  endOfSeasonQueue,
}: {
  getFixturesLambda: Function;
  fixtureQueue: any;
  endOfSeasonQueue: any;
}) {
  const localOnlyRoutes: RouteArgs[] = [];
  if (stageName === 'dev') {
    const [devNewManagerLambda] = createLambdaFunction(
      'devNewManager',
      '../dist/manager/dev-manager',
      'index.handler'
    );

    localOnlyRoutes.push({
      path: '/dev/manager',
      method: 'POST',
      eventHandler: devNewManagerLambda,
    });
  }

  // dev function that we can run against aws as well as localstack
  let devQueueFixturesRole = addLambdaInvokePolicyToRole('devQueueFixtures', [getFixturesLambda]);
  devQueueFixturesRole = addQueueSendPolicyToRole(
    'devQueueFixtures',
    fixtureQueue,
    devQueueFixturesRole
  );
  const [devQueueFixturesLambda] = createLambdaFunction(
    'devQueueFixtures',
    '../dist/fixtures/debugQueueFixtureSimulation',
    'index.handler',
    {
      QUEUE_URL: fixtureQueue.url,
      GET_FIXTURES_LAMBDA_ARN: getFixturesLambda.arn,
    },
    devQueueFixturesRole
  );
  localOnlyRoutes.push({
    path: '/dev/process/{gameworldId}/league/{leagueId}/fixtures',
    method: 'POST',
    eventHandler: devQueueFixturesLambda,
  });

  // Development/testing resources
  let devTriggerEndOfSeasonRole = addQueueSendPolicyToRole(
    'devTriggerEndOfSeason',
    endOfSeasonQueue
  );

  const [devTriggerEndOfSeasonLambda] = createLambdaFunction(
    'devTriggerEndOfSeason',
    '../dist/gameworld/debugTriggerEndOfSeason',
    'index.handler',
    {
      QUEUE_URL: endOfSeasonQueue.url,
    },
    devTriggerEndOfSeasonRole
  );

  localOnlyRoutes.push({
    path: '/dev/trigger/{gameworldId}/endofseason',
    method: 'POST',
    eventHandler: devTriggerEndOfSeasonLambda,
  });

  return localOnlyRoutes;
}
