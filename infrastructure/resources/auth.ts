import { createLambdaFunction } from '../lambda';

export function createAuthResources() {
  // Create guest user creation lambda
  const [createGuestUserLambda] = createLambdaFunction(
    'createGuestUser',
    '../dist/auth/createGuestUser',
    'index.handler'
  );

  // Create migration conflicts detection lambda
  const [getMigrationConflictsLambda] = createLambdaFunction(
    'getMigrationConflicts',
    '../dist/auth/getMigrationConflicts',
    'index.handler'
  );

  // Create migration resolution lambda
  const [resolveMigrationLambda] = createLambdaFunction(
    'resolveMigration',
    '../dist/auth/resolveMigration',
    'index.handler'
  );

  const [createAndAssignNewManagerLambda] = createLambdaFunction(
    'createAndAssignNewManager',
    '../dist/auth/createAndAssignNewManager',
    'index.handler'
  );

  return {
    createGuestUserLambda,
    getMigrationConflictsLambda,
    resolveMigrationLambda,
    createAndAssignNewManagerLambda,
  };
}
