import * as aws from '@pulumi/aws';
import {
  getGlobalDynamoPermissions,
  getGlobalQueuePermissions,
  sshPublicKey,
  stageName,
} from './config';

export interface EC2ResourcesOptimized {
  instance: aws.ec2.Instance;
  securityGroup: aws.ec2.SecurityGroup;
  keyPair?: aws.ec2.KeyPair;
}

export function createEC2InfrastructureOptimized({
  fixtureDetailTable,
}: {
  fixtureDetailTable: aws.dynamodb.Table;
}): EC2ResourcesOptimized {
  // Get default VPC and subnets
  const defaultVpcPromise = aws.ec2.getVpc({ default: true });
  const defaultSubnetsPromise = defaultVpcPromise.then((vpc) =>
    aws.ec2.getSubnets({
      filters: [
        { name: 'vpc-id', values: [vpc.id] },
        { name: 'default-for-az', values: ['true'] },
      ],
    })
  );

  // Create security group for the EC2 instance (no ALB needed)
  const securityGroup = new aws.ec2.SecurityGroup(`${stageName}-monolith-sg-optimized`, {
    description: 'Security group for cost-optimized monolith (no ALB)',
    vpcId: defaultVpcPromise.then((vpc) => vpc.id),

    ingress: [
      {
        description: 'HTTP from CloudFront',
        fromPort: 3000,
        toPort: 3000,
        protocol: 'tcp',
        cidrBlocks: ['0.0.0.0/0'], // CloudFront IPs vary, so allow all (app handles auth)
      },
      {
        description: 'SSH',
        fromPort: 22,
        toPort: 22,
        protocol: 'tcp',
        cidrBlocks: ['0.0.0.0/0'], // Restrict this in production if needed
      },
    ],

    egress: [
      {
        description: 'All outbound traffic',
        fromPort: 0,
        toPort: 0,
        protocol: '-1',
        cidrBlocks: ['0.0.0.0/0'],
      },
    ],

    tags: {
      Name: `${stageName}-monolith-sg-optimized`,
    },
  });

  // Optionally create key pair for SSH access if provided via config
  let keyPair: aws.ec2.KeyPair | undefined;
  if (sshPublicKey) {
    keyPair = new aws.ec2.KeyPair(`${stageName}-monolith-key`, {
      keyName: `${stageName}-monolith-key`,
      publicKey: sshPublicKey,
      tags: { Name: `${stageName}-monolith-key` },
    });
  }

  // Get the latest Amazon Linux 2023 AMI
  const amiPromise = aws.ec2.getAmi({
    mostRecent: true,
    owners: ['amazon'],
    filters: [
      { name: 'name', values: ['al2023-ami-*-arm64'] },
      { name: 'virtualization-type', values: ['hvm'] },
    ],
  });

  // User data script - same as before but optimized for direct CloudFront access
  const userData = `#!/bin/bash
yum update -y
yum install -y git amazon-cloudwatch-agent

# Install Node.js 20
curl -fsSL https://rpm.nodesource.com/setup_20.x | bash -
yum install -y nodejs

# Configure CloudWatch agent (same as before)
cat > /opt/aws/amazon-cloudwatch-agent/etc/amazon-cloudwatch-agent.json << 'EOF'
{
  "agent": {
    "metrics_collection_interval": 60,
    "run_as_user": "cwagent"
  },
  "metrics": {
    "namespace": "CWAgent",
    "metrics_collected": {
      "cpu": {
        "measurement": ["cpu_usage_idle", "cpu_usage_iowait", "cpu_usage_user", "cpu_usage_system"],
        "metrics_collection_interval": 60,
        "totalcpu": false
      },
      "disk": {
        "measurement": ["used_percent"],
        "metrics_collection_interval": 60,
        "resources": ["*"]
      },
      "mem": {
        "measurement": ["mem_used_percent"],
        "metrics_collection_interval": 60
      }
    }
  },
  "logs": {
    "logs_collected": {
      "files": {
        "collect_list": [
          {
            "file_path": "/var/log/monolith.log",
            "log_group_name": "/aws/ec2/${stageName}-monolith",
            "log_stream_name": "{instance_id}/application",
            "timezone": "UTC",
            "retention_in_days": 7
          }
        ]
      }
    }
  }
}
EOF

# Start CloudWatch agent
/opt/aws/amazon-cloudwatch-agent/bin/amazon-cloudwatch-agent-ctl -a fetch-config -m ec2 -c file:/opt/aws/amazon-cloudwatch-agent/etc/amazon-cloudwatch-agent.json -s

# Set up log rotation (same as before)
cat > /etc/logrotate.d/monolith << 'EOF'
/var/log/monolith.log {
    daily
    rotate 7
    compress
    delaycompress
    missingok
    notifempty
    create 644 ec2-user ec2-user
    postrotate
        systemctl reload monolith || true
    endscript
}
EOF

# Create log monitoring script (same as before)
cat > /usr/local/bin/check-log-size.sh << 'EOF'
#!/bin/bash
LOG_FILE="/var/log/monolith.log"
MAX_SIZE_MB=100

if [ -f "$LOG_FILE" ]; then
    SIZE_MB=$(du -m "$LOG_FILE" | cut -f1)
    if [ "$SIZE_MB" -gt "$MAX_SIZE_MB" ]; then
        echo "$(date): Log file $LOG_FILE is \${SIZE_MB}MB, rotating..." >> /var/log/monolith-system.log
        logrotate -f /etc/logrotate.d/monolith
    fi
fi

find /var/log -name "monolith.log.*" -type f -mtime +7 -delete 2>/dev/null || true
EOF

chmod +x /usr/local/bin/check-log-size.sh
echo "0 * * * * root /usr/local/bin/check-log-size.sh" >> /etc/crontab

# Create app directory
mkdir -p /opt/monolith
cd /opt/monolith

# Create systemd service (optimized for direct access)
cat > /etc/systemd/system/monolith.service << 'EOF'
[Unit]
Description=Jumpers for Goalposts Monolith API (Cost Optimized)
After=network.target

[Service]
Type=simple
User=ec2-user
WorkingDirectory=/opt/monolith
ExecStart=/usr/bin/node monolith/server.js
ExecReload=/bin/kill -USR1 \$MAINPID
Restart=always
RestartSec=10
Environment=NODE_ENV=production
Environment=PORT=3000
StandardOutput=append:/var/log/monolith.log
StandardError=append:/var/log/monolith.log

[Install]
WantedBy=multi-user.target
EOF

# Enable the service
systemctl enable monolith
systemctl daemon-reload

# Create placeholder server
cat > /opt/monolith/server.js << 'EOF'
const http = require('http');
const server = http.createServer((req, res) => {
  res.writeHead(200, { 'Content-Type': 'application/json' });
  res.end(JSON.stringify({ 
    message: 'Cost-optimized monolith server', 
    timestamp: new Date().toISOString(),
    path: req.url,
    note: 'Direct CloudFront → EC2 (no ALB)'
  }));
});
server.listen(3000, () => {
  console.log('Cost-optimized server running on port 3000');
});
EOF

chown -R ec2-user:ec2-user /opt/monolith
systemctl start monolith
`;

  // Create IAM role for EC2 instance (same permissions as before)
  const instanceRole = new aws.iam.Role(`${stageName}-monolith-instance-role-optimized`, {
    assumeRolePolicy: JSON.stringify({
      Version: '2012-10-17',
      Statement: [
        {
          Action: 'sts:AssumeRole',
          Effect: 'Allow',
          Principal: {
            Service: 'ec2.amazonaws.com',
          },
        },
      ],
    }),
  });

  // Explicit policy for fixtureDetailTable (read/write)
  new aws.iam.RolePolicy(`${stageName}-monolith-fixture-detail-policy-optimized`, {
    role: instanceRole.id,
    policy: fixtureDetailTable.arn.apply((arn) =>
      JSON.stringify({
        Version: '2012-10-17',
        Statement: [
          {
            Effect: 'Allow',
            Action: [
              'dynamodb:GetItem',
              'dynamodb:Query',
              'dynamodb:Scan',
              'dynamodb:BatchGetItem',
              'dynamodb:PutItem',
              'dynamodb:UpdateItem',
              'dynamodb:DeleteItem',
              'dynamodb:BatchWriteItem',
            ],
            Resource: [arn, `${arn}/index/*`],
          },
        ],
      })
    ),
  });

  // Attach CloudWatch agent policy
  new aws.iam.RolePolicyAttachment(`${stageName}-monolith-cloudwatch-policy-optimized`, {
    role: instanceRole.name,
    policyArn: 'arn:aws:iam::aws:policy/CloudWatchAgentServerPolicy',
  });

  // Add comprehensive policy for Cognito and DynamoDB access (same as before)
  new aws.iam.RolePolicy(`${stageName}-monolith-comprehensive-policy-optimized`, {
    role: instanceRole.id,
    policy: JSON.stringify({
      Version: '2012-10-17',
      Statement: [
        {
          Effect: 'Allow',
          Action: [
            'cognito-idp:InitiateAuth',
            'cognito-idp:RespondToAuthChallenge',
            'cognito-idp:GetUser',
            'cognito-idp:AdminGetUser',
            'cognito-idp:AdminCreateUser',
            'cognito-idp:AdminSetUserPassword',
            'cognito-idp:AdminUpdateUserAttributes',
            'cognito-idp:ListUsers',
            'cognito-identity:GetId',
            'cognito-identity:GetCredentialsForIdentity',
          ],
          Resource: '*',
        },
        {
          Effect: 'Allow',
          Action: [
            'xray:PutTraceSegments',
            'xray:PutTelemetryRecords',
            'xray:GetSamplingRules',
            'xray:GetSamplingTargets',
            'xray:GetSamplingStatisticSummaries',
          ],
          Resource: '*',
        },
        {
          Effect: 'Allow',
          Action: [
            'logs:CreateLogGroup',
            'logs:CreateLogStream',
            'logs:PutLogEvents',
            'logs:DescribeLogStreams',
            'logs:DescribeLogGroups',
          ],
          Resource: '*',
        },
      ],
    }),
  });

  // Add global DynamoDB and SQS permissions (same as before)
  const globalDynamoPerms = getGlobalDynamoPermissions();
  globalDynamoPerms.forEach((permission) => {
    const actions: string[] = [];
    if (permission.permissions === 'read' || permission.permissions === 'both') {
      actions.push('dynamodb:GetItem', 'dynamodb:Query', 'dynamodb:Scan', 'dynamodb:BatchGetItem');
    }
    if (permission.permissions === 'write' || permission.permissions === 'both') {
      actions.push(
        'dynamodb:PutItem',
        'dynamodb:UpdateItem',
        'dynamodb:DeleteItem',
        'dynamodb:BatchWriteItem'
      );
    }
    if (actions.length > 0) {
      new aws.iam.RolePolicy(`${stageName}-monolith-dynamo-${permission.name}-policy-optimized`, {
        role: instanceRole.id,
        policy: permission.table.arn.apply((arn) =>
          JSON.stringify({
            Version: '2012-10-17',
            Statement: [{ Effect: 'Allow', Action: actions, Resource: [arn, `${arn}/index/*`] }],
          })
        ),
      });
    }
  });

  const globalQueuePerms = getGlobalQueuePermissions();
  globalQueuePerms.forEach((permission) => {
    const actions: string[] = [];
    if (permission.permissions === 'send' || permission.permissions === 'both') {
      actions.push('sqs:SendMessage', 'sqs:GetQueueAttributes');
    }
    if (permission.permissions === 'read' || permission.permissions === 'both') {
      actions.push(
        'sqs:ReceiveMessage',
        'sqs:DeleteMessage',
        'sqs:GetQueueAttributes',
        'sqs:ChangeMessageVisibility'
      );
    }
    if (actions.length > 0) {
      new aws.iam.RolePolicy(`${stageName}-monolith-sqs-${permission.name}-policy-optimized`, {
        role: instanceRole.id,
        policy: permission.queue.arn.apply((arn) =>
          JSON.stringify({
            Version: '2012-10-17',
            Statement: [{ Effect: 'Allow', Action: actions, Resource: arn }],
          })
        ),
      });
    }
  });

  // Create instance profile
  const instanceProfile = new aws.iam.InstanceProfile(
    `${stageName}-monolith-instance-profile-optimized`,
    {
      role: instanceRole.name,
    }
  );

  // Create EC2 instance (cost-optimized)
  const instance = new aws.ec2.Instance(`${stageName}-monolith-instance-optimized`, {
    instanceType: 't4g.micro', // ARM-based, cost-effective
    ami: amiPromise.then((ami) => ami.id),
    keyName: keyPair?.keyName,
    vpcSecurityGroupIds: [securityGroup.id],
    subnetId: defaultSubnetsPromise.then((subnets) => subnets.ids[0]),
    iamInstanceProfile: instanceProfile.name,
    userData: userData,
    rootBlockDevice: {
      volumeSize: 30, // GiB
      volumeType: 'gp3',
      deleteOnTermination: true,
    },
    tags: {
      Name: `${stageName}-monolith-instance-optimized`,
      CostOptimized: 'true',
    },
  });

  return {
    instance,
    securityGroup,
    keyPair,
  };
}
