import * as aws from '@pulumi/aws';
import * as pulumi from '@pulumi/pulumi';
import { stageName } from './config';

export interface MonitoringResources {
  cpuAlarm: aws.cloudwatch.MetricAlarm;
  memoryAlarm: aws.cloudwatch.MetricAlarm;
  diskAlarm: aws.cloudwatch.MetricAlarm;
  healthCheckAlarm: aws.cloudwatch.MetricAlarm;
  logGroup: aws.cloudwatch.LogGroup;
  dashboard: aws.cloudwatch.Dashboard;
}

export function createMonitoringResources(
  instanceId: pulumi.Input<string>,
  targetGroupArn: pulumi.Input<string> | undefined,
  alarmEmail: string
): MonitoringResources {
  // Create SNS topic for alerts
  const alertTopic = new aws.sns.Topic(`${stageName}-monolith-alerts`, {
    name: `${stageName}-monolith-alerts`,
  });

  // Subscribe email to alerts
  new aws.sns.TopicSubscription(`${stageName}-monolith-email-alert`, {
    topic: alertTopic.arn,
    protocol: 'email',
    endpoint: alarmEmail,
  });

  // CloudWatch Log Group for application logs
  const logGroup = new aws.cloudwatch.LogGroup(`${stageName}-monolith-logs`, {
    name: `/aws/ec2/${stageName}-monolith`,
    retentionInDays: 30, // Adjust as needed
    tags: {
      Name: `${stageName}-monolith-logs`,
    },
  });

  // CPU Utilization Alarm
  const cpuAlarm = new aws.cloudwatch.MetricAlarm(`${stageName}-monolith-cpu-alarm`, {
    name: `${stageName}-monolith-high-cpu`,
    alarmDescription: 'Monolith instance high CPU utilization',
    metricName: 'CPUUtilization',
    namespace: 'AWS/EC2',
    statistic: 'Average',
    period: 300, // 5 minutes
    evaluationPeriods: 2,
    threshold: 80, // 80% CPU
    comparisonOperator: 'GreaterThanThreshold',
    dimensions: {
      InstanceId: instanceId,
    },
    alarmActions: [alertTopic.arn],
    okActions: [alertTopic.arn],
    tags: {
      Name: `${stageName}-monolith-cpu-alarm`,
    },
  });

  // Memory Utilization Alarm (requires CloudWatch agent)
  const memoryAlarm = new aws.cloudwatch.MetricAlarm(`${stageName}-monolith-memory-alarm`, {
    name: `${stageName}-monolith-high-memory`,
    alarmDescription: 'Monolith instance high memory utilization',
    metricName: 'MemoryUtilization',
    namespace: 'CWAgent',
    statistic: 'Average',
    period: 300,
    evaluationPeriods: 2,
    threshold: 85, // 85% memory
    comparisonOperator: 'GreaterThanThreshold',
    dimensions: {
      InstanceId: instanceId,
    },
    alarmActions: [alertTopic.arn],
    okActions: [alertTopic.arn],
    treatMissingData: 'notBreaching', // Don't alarm if CloudWatch agent not installed
    tags: {
      Name: `${stageName}-monolith-memory-alarm`,
    },
  });

  // Disk Space Alarm
  const diskAlarm = new aws.cloudwatch.MetricAlarm(`${stageName}-monolith-disk-alarm`, {
    name: `${stageName}-monolith-low-disk-space`,
    alarmDescription: 'Monolith instance low disk space',
    metricName: 'DiskSpaceUtilization',
    namespace: 'CWAgent',
    statistic: 'Average',
    period: 300,
    evaluationPeriods: 1,
    threshold: 90, // 90% disk usage
    comparisonOperator: 'GreaterThanThreshold',
    dimensions: {
      InstanceId: instanceId,
      Filesystem: '/dev/xvda1',
      MountPath: '/',
    },
    alarmActions: [alertTopic.arn],
    treatMissingData: 'notBreaching',
    tags: {
      Name: `${stageName}-monolith-disk-alarm`,
    },
  });

  // Application Health Check Alarm (only if ALB exists - cost optimization)
  const healthCheckAlarm = targetGroupArn
    ? new aws.cloudwatch.MetricAlarm(`${stageName}-monolith-health-alarm`, {
        name: `${stageName}-monolith-unhealthy-targets`,
        alarmDescription: 'Monolith application unhealthy',
        metricName: 'UnHealthyHostCount',
        namespace: 'AWS/ApplicationELB',
        statistic: 'Average',
        period: 60, // 1 minute
        evaluationPeriods: 2,
        threshold: 0,
        comparisonOperator: 'GreaterThanThreshold',
        dimensions: {
          TargetGroup: pulumi
            .output(targetGroupArn)
            .apply((arn) => arn.split('/').slice(-3).join('/')),
        },
        alarmActions: [alertTopic.arn],
        okActions: [alertTopic.arn],
        tags: {
          Name: `${stageName}-monolith-health-alarm`,
        },
      })
    : undefined;

  // CloudWatch Dashboard
  const dashboard = new aws.cloudwatch.Dashboard(`${stageName}-monolith-dashboard`, {
    dashboardName: `${stageName}-monolith-monitoring`,
    dashboardBody: JSON.stringify({
      widgets: [
        {
          type: 'metric',
          x: 0,
          y: 0,
          width: 12,
          height: 6,
          properties: {
            metrics: [
              ['AWS/EC2', 'CPUUtilization', 'InstanceId', instanceId],
              ['CWAgent', 'MemoryUtilization', 'InstanceId', instanceId],
            ],
            period: 300,
            stat: 'Average',
            region: 'us-east-2',
            title: 'EC2 Instance Metrics',
            yAxis: {
              left: {
                min: 0,
                max: 100,
              },
            },
          },
        },
        // Conditionally add ALB metrics widget if targetGroupArn is defined
        ...(targetGroupArn
          ? [
              {
                type: 'metric',
                x: 12,
                y: 0,
                width: 12,
                height: 6,
                properties: {
                  metrics: [
                    [
                      'AWS/ApplicationELB',
                      'TargetResponseTime',
                      'TargetGroup',
                      pulumi
                        .output(targetGroupArn)
                        .apply((arn) => arn.split('/').slice(-3).join('/')),
                    ],
                    [
                      'AWS/ApplicationELB',
                      'RequestCount',
                      'TargetGroup',
                      pulumi
                        .output(targetGroupArn)
                        .apply((arn) => arn.split('/').slice(-3).join('/')),
                    ],
                  ],
                  period: 300,
                  stat: 'Average',
                  region: 'us-east-2',
                  title: 'Application Load Balancer Metrics',
                },
              },
            ]
          : []),
        {
          type: 'log',
          x: 0,
          y: 6,
          width: 24,
          height: 6,
          properties: {
            query: `SOURCE '${logGroup.name}'\n| fields @timestamp, level, message, requestId, functionName\n| filter level = "ERROR"\n| sort @timestamp desc\n| limit 100`,
            region: 'us-east-2',
            title: 'Recent Error Logs',
          },
        },
      ],
    }),
  });

  return {
    cpuAlarm,
    memoryAlarm,
    diskAlarm,
    healthCheckAlarm: healthCheckAlarm!,
    logGroup,
    dashboard,
  };
}
