import * as aws from '@pulumi/aws';
import {
  getGlobalDynamoPermissions,
  getGlobalQueuePermissions,
  sshPublicKey,
  stageName,
} from './config';

export interface EC2Resources {
  instance: aws.ec2.Instance;
  securityGroup: aws.ec2.SecurityGroup;
  applicationLoadBalancer: aws.lb.LoadBalancer;
  targetGroup: aws.lb.TargetGroup;
  listener: aws.lb.Listener;
  keyPair?: aws.ec2.KeyPair; // optional now
}

export function createEC2Infrastructure({
  fixtureDetailTable,
}: {
  fixtureDetailTable: aws.dynamodb.Table;
}): EC2Resources {
  // Get default VPC and subnets
  const defaultVpcPromise = aws.ec2.getVpc({ default: true });
  const defaultSubnetsPromise = defaultVpcPromise.then((vpc) =>
    aws.ec2.getSubnets({
      filters: [
        { name: 'vpc-id', values: [vpc.id] },
        { name: 'default-for-az', values: ['true'] },
      ],
    })
  );

  // Create security group for the EC2 instance
  const securityGroup = new aws.ec2.SecurityGroup(`${stageName}-monolith-sg`, {
    description: 'Security group for monolith API server',
    vpcId: defaultVpcPromise.then((vpc) => vpc.id),
    ingress: [
      {
        description: 'HTTP from ALB',
        fromPort: 3000,
        toPort: 3000,
        protocol: 'tcp',
        securityGroups: [], // Will be set after ALB security group is created
      },
      {
        description: 'SSH',
        fromPort: 22,
        toPort: 22,
        protocol: 'tcp',
        cidrBlocks: ['0.0.0.0/0'], // Restrict this in production
      },
    ],
    egress: [
      {
        description: 'All outbound traffic',
        fromPort: 0,
        toPort: 0,
        protocol: '-1',
        cidrBlocks: ['0.0.0.0/0'],
      },
    ],
    tags: { Name: `${stageName}-monolith-sg` },
  });

  // Create security group for the Application Load Balancer
  const albSecurityGroup = new aws.ec2.SecurityGroup(`${stageName}-alb-sg`, {
    description: 'Security group for Application Load Balancer',
    vpcId: defaultVpcPromise.then((vpc) => vpc.id),
    ingress: [
      { description: 'HTTP', fromPort: 80, toPort: 80, protocol: 'tcp', cidrBlocks: ['0.0.0.0/0'] },
      {
        description: 'HTTPS',
        fromPort: 443,
        toPort: 443,
        protocol: 'tcp',
        cidrBlocks: ['0.0.0.0/0'],
      },
    ],
    egress: [
      {
        description: 'All outbound traffic',
        fromPort: 0,
        toPort: 0,
        protocol: '-1',
        cidrBlocks: ['0.0.0.0/0'],
      },
    ],
    tags: { Name: `${stageName}-alb-sg` },
  });

  // Update the EC2 security group to allow traffic from ALB
  new aws.ec2.SecurityGroupRule(`${stageName}-monolith-sg-alb-rule`, {
    type: 'ingress',
    fromPort: 3000,
    toPort: 3000,
    protocol: 'tcp',
    sourceSecurityGroupId: albSecurityGroup.id,
    securityGroupId: securityGroup.id,
  });

  // Optionally create key pair for SSH access if provided via config
  let keyPair: aws.ec2.KeyPair | undefined;
  if (sshPublicKey) {
    keyPair = new aws.ec2.KeyPair(`${stageName}-monolith-key`, {
      keyName: `${stageName}-monolith-key`,
      publicKey: sshPublicKey,
      tags: { Name: `${stageName}-monolith-key` },
    });
  }

  // Get the latest Amazon Linux 2023 AMI
  const amiPromise = aws.ec2.getAmi({
    mostRecent: true,
    owners: ['amazon'],
    filters: [
      { name: 'name', values: ['al2023-ami-*-arm64'] },
      { name: 'virtualization-type', values: ['hvm'] },
    ],
  });

  // User data script to install Node.js, CloudWatch agent, and set up the application
  const userData = `#!/bin/bash
yum update -y
yum install -y git amazon-cloudwatch-agent

# Install Node.js 20
curl -fsSL https://rpm.nodesource.com/setup_20.x | bash -
yum install -y nodejs

# Configure CloudWatch agent
cat > /opt/aws/amazon-cloudwatch-agent/etc/amazon-cloudwatch-agent.json << 'EOF'
{
  "agent": {
    "metrics_collection_interval": 60,
    "run_as_user": "cwagent"
  },
  "metrics": {
    "namespace": "CWAgent",
    "metrics_collected": {
      "cpu": {
        "measurement": [
          "cpu_usage_idle",
          "cpu_usage_iowait",
          "cpu_usage_user",
          "cpu_usage_system"
        ],
        "metrics_collection_interval": 60,
        "totalcpu": false
      },
      "disk": {
        "measurement": [
          "used_percent"
        ],
        "metrics_collection_interval": 60,
        "resources": [
          "*"
        ]
      },
      "diskio": {
        "measurement": [
          "io_time"
        ],
        "metrics_collection_interval": 60,
        "resources": [
          "*"
        ]
      },
      "mem": {
        "measurement": [
          "mem_used_percent"
        ],
        "metrics_collection_interval": 60
      }
    }
  },
  "logs": {
    "logs_collected": {
      "files": {
        "collect_list": [
          {
            "file_path": "/var/log/monolith.log",
            "log_group_name": "/aws/ec2/${stageName}-monolith",
            "log_stream_name": "{instance_id}/application",
            "timezone": "UTC",
            "retention_in_days": 7
          },
          {
            "file_path": "/var/log/messages",
            "log_group_name": "/aws/ec2/${stageName}-monolith",
            "log_stream_name": "{instance_id}/system",
            "timezone": "UTC"
          }
        ]
      }
    }
  }
}
EOF

# Start CloudWatch agent
/opt/aws/amazon-cloudwatch-agent/bin/amazon-cloudwatch-agent-ctl -a fetch-config -m ec2 -c file:/opt/aws/amazon-cloudwatch-agent/etc/amazon-cloudwatch-agent.json -s

# Set up log rotation for monolith logs
cat > /etc/logrotate.d/monolith << 'EOF'
/var/log/monolith.log {
    daily
    rotate 7
    compress
    delaycompress
    missingok
    notifempty
    create 644 ec2-user ec2-user
    postrotate
        # Send USR1 signal to systemd to reopen log files
        systemctl reload monolith || true
    endscript
}
EOF

# Set up log rotation for system logs (optional)
cat > /etc/logrotate.d/monolith-system << 'EOF'
/var/log/monolith-system.log {
    daily
    rotate 3
    compress
    delaycompress
    missingok
    notifempty
    create 644 root root
}
EOF

# Create log monitoring script
cat > /usr/local/bin/check-log-size.sh << 'EOF'
#!/bin/bash
# Check log file sizes and clean up if necessary

LOG_FILE="/var/log/monolith.log"
MAX_SIZE_MB=100

if [ -f "$LOG_FILE" ]; then
    SIZE_MB=$(du -m "$LOG_FILE" | cut -f1)
    if [ "$SIZE_MB" -gt "$MAX_SIZE_MB" ]; then
        echo "$(date): Log file $LOG_FILE is \${SIZE_MB}MB, rotating..." >> /var/log/monolith-system.log
        logrotate -f /etc/logrotate.d/monolith
    fi
fi

# Clean up old compressed logs (keep only 7 days)
find /var/log -name "monolith.log.*" -type f -mtime +7 -delete 2>/dev/null || true
EOF

chmod +x /usr/local/bin/check-log-size.sh

# Add cron job to check log size every hour
echo "0 * * * * root /usr/local/bin/check-log-size.sh" >> /etc/crontab

# Create app directory
mkdir -p /opt/monolith
cd /opt/monolith

# Create a simple systemd service file
cat > /etc/systemd/system/monolith.service << EOF
[Unit]
Description=Jumpers for Goalposts Monolith API
After=network.target

[Service]
Type=simple
User=ec2-user
WorkingDirectory=/opt/monolith
ExecStart=/usr/bin/node server.js
ExecReload=/bin/kill -USR1 \$MAINPID
Restart=always
RestartSec=10
Environment=NODE_ENV=production
Environment=PORT=3000
StandardOutput=append:/var/log/monolith.log
StandardError=append:/var/log/monolith.log

[Install]
WantedBy=multi-user.target
EOF

# Enable the service
systemctl enable monolith
systemctl daemon-reload

# Create a placeholder server for now
cat > /opt/monolith/server.js << 'EOF'
const http = require('http');
const server = http.createServer((req, res) => {
  res.writeHead(200, { 'Content-Type': 'application/json' });
  res.end(JSON.stringify({ 
    message: 'Monolith server placeholder', 
    timestamp: new Date().toISOString(),
    path: req.url 
  }));
});
server.listen(3000, () => {
  console.log('Server running on port 3000');
});
EOF

chown -R ec2-user:ec2-user /opt/monolith
systemctl start monolith
`;

  // Create IAM role for EC2 instance
  const instanceRole = new aws.iam.Role(`${stageName}-monolith-instance-role`, {
    assumeRolePolicy: JSON.stringify({
      Version: '2012-10-17',
      Statement: [
        {
          Action: 'sts:AssumeRole',
          Effect: 'Allow',
          Principal: {
            Service: 'ec2.amazonaws.com',
          },
        },
      ],
    }),
  });

  // Attach CloudWatch agent policy
  new aws.iam.RolePolicyAttachment(`${stageName}-monolith-cloudwatch-policy`, {
    role: instanceRole.name,
    policyArn: 'arn:aws:iam::aws:policy/CloudWatchAgentServerPolicy',
  });

  // Add comprehensive policy for Cognito and DynamoDB access (same as Lambda functions)
  new aws.iam.RolePolicy(`${stageName}-monolith-comprehensive-policy`, {
    role: instanceRole.id,
    policy: JSON.stringify({
      Version: '2012-10-17',
      Statement: [
        // Cognito permissions for authentication
        {
          Effect: 'Allow',
          Action: [
            'cognito-idp:InitiateAuth',
            'cognito-idp:RespondToAuthChallenge',
            'cognito-idp:GetUser',
            'cognito-idp:AdminGetUser',
            'cognito-idp:AdminCreateUser',
            'cognito-idp:AdminSetUserPassword',
            'cognito-idp:AdminUpdateUserAttributes',
            'cognito-idp:ListUsers',
            'cognito-identity:GetId',
            'cognito-identity:GetCredentialsForIdentity',
          ],
          Resource: '*',
        },
        // X-Ray permissions (same as Lambda)
        {
          Effect: 'Allow',
          Action: [
            'xray:PutTraceSegments',
            'xray:PutTelemetryRecords',
            'xray:GetSamplingRules',
            'xray:GetSamplingTargets',
            'xray:GetSamplingStatisticSummaries',
          ],
          Resource: '*',
        },
        // CloudWatch Logs permissions
        {
          Effect: 'Allow',
          Action: [
            'logs:CreateLogGroup',
            'logs:CreateLogStream',
            'logs:PutLogEvents',
            'logs:DescribeLogStreams',
            'logs:DescribeLogGroups',
          ],
          Resource: '*',
        },
      ],
    }),
  });

  // Add global DynamoDB permissions (same as Lambda functions)
  const globalDynamoPerms = getGlobalDynamoPermissions();
  globalDynamoPerms.forEach((permission) => {
    const actions: string[] = [];

    if (permission.permissions === 'read' || permission.permissions === 'both') {
      actions.push('dynamodb:GetItem', 'dynamodb:Query', 'dynamodb:Scan', 'dynamodb:BatchGetItem');
    }

    if (permission.permissions === 'write' || permission.permissions === 'both') {
      actions.push(
        'dynamodb:PutItem',
        'dynamodb:UpdateItem',
        'dynamodb:DeleteItem',
        'dynamodb:BatchWriteItem'
      );
    }

    if (actions.length > 0) {
      new aws.iam.RolePolicy(`${stageName}-monolith-dynamo-${permission.name}-policy`, {
        role: instanceRole.id,
        policy: permission.table.arn.apply((arn) =>
          JSON.stringify({
            Version: '2012-10-17',
            Statement: [
              {
                Effect: 'Allow',
                Action: actions,
                Resource: [
                  arn, // Table ARN
                  `${arn}/index/*`, // Index ARNs
                ],
              },
            ],
          })
        ),
      });
    }
  });

  // Add global SQS permissions (same as Lambda functions)
  const globalQueuePerms = getGlobalQueuePermissions();
  globalQueuePerms.forEach((permission) => {
    const actions: string[] = [];

    if (permission.permissions === 'send' || permission.permissions === 'both') {
      actions.push('sqs:SendMessage', 'sqs:GetQueueAttributes');
    }

    if (permission.permissions === 'read' || permission.permissions === 'both') {
      actions.push(
        'sqs:ReceiveMessage',
        'sqs:DeleteMessage',
        'sqs:GetQueueAttributes',
        'sqs:ChangeMessageVisibility'
      );
    }

    if (actions.length > 0) {
      new aws.iam.RolePolicy(`${stageName}-monolith-sqs-${permission.name}-policy`, {
        role: instanceRole.id,
        policy: permission.queue.arn.apply((arn) =>
          JSON.stringify({
            Version: '2012-10-17',
            Statement: [
              {
                Effect: 'Allow',
                Action: actions,
                Resource: arn,
              },
            ],
          })
        ),
      });
    }
  });

  // Explicit policy for fixtureDetailTable (read/write)
  new aws.iam.RolePolicy(`${stageName}-monolith-fixture-detail-policy`, {
    role: instanceRole.id,
    policy: fixtureDetailTable.arn.apply((arn) =>
      JSON.stringify({
        Version: '2012-10-17',
        Statement: [
          {
            Effect: 'Allow',
            Action: [
              'dynamodb:GetItem',
              'dynamodb:Query',
              'dynamodb:Scan',
              'dynamodb:BatchGetItem',
              'dynamodb:PutItem',
              'dynamodb:UpdateItem',
              'dynamodb:DeleteItem',
              'dynamodb:BatchWriteItem',
            ],
            Resource: [arn, `${arn}/index/*`],
          },
        ],
      })
    ),
  });

  // Create instance profile
  const instanceProfile = new aws.iam.InstanceProfile(`${stageName}-monolith-instance-profile`, {
    role: instanceRole.name,
  });

  // Create EC2 instance
  const instance = new aws.ec2.Instance(`${stageName}-monolith-instance`, {
    instanceType: 't4g.micro',
    ami: amiPromise.then((ami) => ami.id),
    keyName: keyPair?.keyName,
    vpcSecurityGroupIds: [securityGroup.id],
    subnetId: defaultSubnetsPromise.then((subnets) => subnets.ids[0]),
    iamInstanceProfile: instanceProfile.name,
    userData: userData,
    tags: { Name: `${stageName}-monolith-instance` },
    rootBlockDevice: {
      volumeSize: 12, // GiB
      volumeType: 'gp3',
      deleteOnTermination: true,
    },
  });

  // Create Application Load Balancer
  const applicationLoadBalancer = new aws.lb.LoadBalancer(`${stageName}-monolith-alb`, {
    loadBalancerType: 'application',
    securityGroups: [albSecurityGroup.id],
    subnets: defaultSubnetsPromise.then((subnets) => subnets.ids),
    tags: { Name: `${stageName}-monolith-alb` },
  });

  // Create target group
  const targetGroup = new aws.lb.TargetGroup(`${stageName}-monolith-tg`, {
    port: 3000,
    protocol: 'HTTP',
    vpcId: defaultVpcPromise.then((vpc) => vpc.id),
    targetType: 'instance',
    healthCheck: {
      enabled: true,
      path: '/ping',
      port: '3000',
      protocol: 'HTTP',
      healthyThreshold: 2,
      unhealthyThreshold: 2,
      timeout: 5,
      interval: 30,
      matcher: '200',
    },
    tags: { Name: `${stageName}-monolith-tg` },
  });

  // Attach instance to target group
  new aws.lb.TargetGroupAttachment(`${stageName}-monolith-tg-attachment`, {
    targetGroupArn: targetGroup.arn,
    targetId: instance.id,
    port: 3000,
  });

  // Create listener for the load balancer
  const listener = new aws.lb.Listener(`${stageName}-monolith-listener`, {
    loadBalancerArn: applicationLoadBalancer.arn,
    port: 80,
    protocol: 'HTTP',
    defaultActions: [{ type: 'forward', targetGroupArn: targetGroup.arn }],
  });

  return { instance, securityGroup, applicationLoadBalancer, targetGroup, listener, keyPair };
}
