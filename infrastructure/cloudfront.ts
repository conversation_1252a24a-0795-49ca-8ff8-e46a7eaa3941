import * as aws from '@pulumi/aws';
import { Output } from '@pulumi/pulumi';
import { stageName } from './config';

export interface CloudFrontResources {
  distribution: aws.cloudfront.Distribution;
  certificate: aws.acm.Certificate;
}

export function createCloudFrontDistribution(ec2PublicDns: Output<string>): CloudFrontResources {
  // Create a provider for us-east-1 (required for CloudFront certificates)
  const east1Provider = new aws.Provider('east1-cf', { region: 'us-east-1' });

  // Create an origin request policy that forwards everything (headers, cookies, query strings)
  const allForwardPolicy = new aws.cloudfront.OriginRequestPolicy(
    `${stageName}-all-forward`,
    {
      name: `${stageName}-all-forward`,
      comment: 'Forward all headers, cookies, and query strings for API behavior',
      headersConfig: { headerBehavior: 'allViewer' },
      cookiesConfig: { cookieBehavior: 'all' },
      queryStringsConfig: { queryStringBehavior: 'all' },
    },
    { provider: east1Provider }
  );

  // Create ACM certificate in us-east-1 for CloudFront
  const certificate = new aws.acm.Certificate(
    `${stageName}-cloudfront-cert`,
    {
      domainName: 'jfg-stage-v2.rwscripts.com',
      validationMethod: 'DNS',
      tags: {
        Name: `${stageName}-cloudfront-cert`,
      },
    },
    { provider: east1Provider }
  );

  // Create CloudFront distribution
  const distribution = new aws.cloudfront.Distribution(`${stageName}-cloudfront`, {
    aliases: ['jfg-stage-v2.rwscripts.com'],

    // Single origin - EC2 instance directly (cost optimization: no ALB needed)
    origins: [
      {
        domainName: ec2PublicDns,
        originId: 'monolith-ec2',
        customOriginConfig: {
          httpPort: 3000,
          httpsPort: 443,
          originProtocolPolicy: 'http-only', // EC2 serves HTTP on port 3000
          originSslProtocols: ['TLSv1.2'],
        },
      },
    ],

    // Default cache behavior - route all traffic to EC2 directly
    defaultCacheBehavior: {
      targetOriginId: 'monolith-ec2',
      viewerProtocolPolicy: 'redirect-to-https',
      allowedMethods: ['DELETE', 'GET', 'HEAD', 'OPTIONS', 'PATCH', 'POST', 'PUT'],
      cachedMethods: ['GET', 'HEAD'],
      compress: true,
      cachePolicyId: '4135ea2d-6df8-44a3-9df3-4b5a84be39ad', // Managed-CachingDisabled
      originRequestPolicyId: allForwardPolicy.id, // Custom policy to forward all headers/cookies/query strings
    },

    restrictions: {
      geoRestriction: {
        restrictionType: 'none',
      },
    },

    viewerCertificate: {
      acmCertificateArn: certificate.arn,
      sslSupportMethod: 'sni-only',
      minimumProtocolVersion: 'TLSv1.2_2021',
    },

    enabled: true,
    isIpv6Enabled: true,
    comment: `${stageName} API distribution with v2 routing to monolith`,

    tags: {
      Name: `${stageName}-cloudfront`,
      Environment: stageName,
    },
  });

  return {
    distribution,
    certificate,
  };
}
