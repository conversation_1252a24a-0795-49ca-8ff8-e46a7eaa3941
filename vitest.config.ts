import { resolve } from 'path';
import { defineConfig } from 'vitest/config';

export default defineConfig({
  // Always resolve paths from the project root
  root: resolve(__dirname),
  test: {
    globals: true,
    environment: 'node',
    setupFiles: ['./vitest.setup.ts'],
    reporters: ['default', 'junit'],
    outputFile: 'reports/junit.xml',

    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html'],
      exclude: [
        'node_modules/',
        'dist/',
        'infrastructure/',
        'scripts/',
        'src/testing/',
        '**/*.test.ts',
        '**/*.spec.ts',
        'src/migrations/',
        'src/migrations_backup/',
      ],
    },

    exclude: [
      'node_modules/',
      'dist/',
      'layer/',
      'infrastructure/',
      'src/migrations/',
      'src/migrations_backup/',
    ],

    testTimeout: 10000,
    hookTimeout: 10000,
  },
  resolve: {
    alias: {
      '@': resolve(__dirname, './src'),
    },
  },
  esbuild: {
    target: 'es2022',
  },
});
