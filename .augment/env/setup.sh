#!/bin/bash
set -e

# Update package lists
sudo apt-get update

# Install Node.js 22.x (matching the GitHub workflow)
curl -fsSL https://deb.nodesource.com/setup_22.x | sudo -E bash -
sudo apt-get install -y nodejs

# Verify Node.js and npm installation
node --version
npm --version

# Navigate to workspace
cd /mnt/persist/workspace

# Install dependencies
npm ci

# Install Jest and React Native Testing Library dependencies
npm install --save-dev jest @testing-library/react-native @testing-library/jest-native jest-environment-node

# Create Jest configuration
cat > jest.config.js << 'EOF'
module.exports = {
  preset: 'react-native',
  setupFilesAfterEnv: ['<rootDir>/jest.setup.js'],
  testEnvironment: 'node',
  transformIgnorePatterns: [
    'node_modules/(?!(react-native|@react-native|expo|@expo|@tanstack|react-native-reanimated|react-native-gesture-handler|react-native-screens|react-native-safe-area-context|@react-native-async-storage|react-native-web|styled-components)/)'
  ],
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/src/$1'
  },
  collectCoverageFrom: [
    'src/**/*.{ts,tsx}',
    '!src/**/*.d.ts',
    '!src/**/*.test.{ts,tsx}',
    '!src/**/*.spec.{ts,tsx}'
  ],
  testMatch: [
    '<rootDir>/src/**/__tests__/**/*.{ts,tsx}',
    '<rootDir>/src/**/*.{test,spec}.{ts,tsx}'
  ]
};
EOF

# Create Jest setup file
cat > jest.setup.js << 'EOF'
import '@testing-library/jest-native/extend-expect';

// Mock react-native modules
jest.mock('react-native/Libraries/Animated/NativeAnimatedHelper');

// Mock Expo modules
jest.mock('expo-constants', () => ({
  default: {
    expoConfig: {
      extra: {
        appTitle: {
          development: 'Test App'
        }
      }
    }
  }
}));

jest.mock('expo-router', () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    back: jest.fn()
  }),
  useLocalSearchParams: () => ({}),
  Stack: {
    Screen: ({ children }) => children
  }
}));

jest.mock('expo-linking', () => ({
  createURL: jest.fn()
}));

jest.mock('expo-notifications', () => ({
  requestPermissionsAsync: jest.fn(),
  scheduleNotificationAsync: jest.fn(),
  cancelAllScheduledNotificationsAsync: jest.fn()
}));

// Mock AWS Amplify
jest.mock('aws-amplify', () => ({
  Amplify: {
    configure: jest.fn()
  }
}));

jest.mock('@aws-amplify/react-native', () => ({}));
jest.mock('@aws-amplify/ui-react-native', () => ({}));

// Mock React Native modules
jest.mock('react-native-config', () => ({
  API_URL: 'http://localhost:3000'
}));

jest.mock('react-native-get-random-values', () => ({}));
jest.mock('react-native-url-polyfill/auto', () => ({}));

// Mock styled-components
jest.mock('styled-components/native', () => {
  const styled = require('styled-components').default;
  return styled;
});

// Global test timeout
jest.setTimeout(10000);
EOF

# Add test script to package.json
npm pkg set scripts.test="jest"
npm pkg set scripts.test:watch="jest --watch"
npm pkg set scripts.test:coverage="jest --coverage"

# Install React Native preset for Jest
npm install --save-dev react-native

echo "Jest configuration completed successfully!"