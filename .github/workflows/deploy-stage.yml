name: Deploy to Stage

on:
  workflow_run:
    workflows:
      - Run Tests
    types:
      - completed
    branches:
      - main

concurrency:
  group: staging-environment
  cancel-in-progress: false

jobs:
  deploy:
    if: ${{ github.event.workflow_run.conclusion == 'success' }}
    runs-on: ubuntu-latest
    environment: staging

    steps:
      - uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'

      - name: Install dependencies (root)
        run: npm ci

      - name: Build application
        run: npm run build

      - name: Install dependencies (infrastructure)
        working-directory: ./infrastructure
        run: npm ci

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.AWS_REGION }}

      - name: Setup Pulumi
        uses: pulumi/actions@v4

      - name: Deploy infrastructure to stage
        working-directory: ./infrastructure
        run: pulumi up --yes --non-interactive --skip-preview --stack stage
        env:
          PULUMI_ACCESS_TOKEN: ${{ secrets.PULUMI_ACCESS_TOKEN }}

      - name: Build monolith application
        run: npm run monolith:build

      - name: Get deployment information
        id: get-deployment-info
        working-directory: ./infrastructure
        run: |
          INSTANCE_IP=$(pulumi stack output monolithPublicIp --stack stage 2>/dev/null || echo "")
          CLOUDFRONT_DOMAIN=$(pulumi stack output cloudFrontDistributionDomain --stack stage 2>/dev/null || echo "")

          if [ -z "$INSTANCE_IP" ]; then
            echo "No monolith instance found, skipping deployment"
            echo "skip_deployment=true" >> $GITHUB_OUTPUT
          else
            echo "instance_ip=$INSTANCE_IP" >> $GITHUB_OUTPUT
            echo "skip_deployment=false" >> $GITHUB_OUTPUT
            echo "cloudfront_domain=$CLOUDFRONT_DOMAIN" >> $GITHUB_OUTPUT

            echo "🚀 Deployment Information:"
            echo "EC2 Instance IP: $INSTANCE_IP"
            echo "CloudFront Domain: $CLOUDFRONT_DOMAIN"
            echo "Test URL: https://$CLOUDFRONT_DOMAIN/ping"
          fi
        env:
          PULUMI_ACCESS_TOKEN: ${{ secrets.PULUMI_ACCESS_TOKEN }}

      - name: Deploy monolith to EC2
        if: steps.get-deployment-info.outputs.skip_deployment == 'false'
        run: |
          set -euo pipefail
          # Create deployment package WITHOUT node_modules (install on remote)
          DEPLOY_DIR="deploy-$(date +%Y%m%d-%H%M%S)"
          echo "Using deployment directory: $DEPLOY_DIR"
          mkdir -p "$DEPLOY_DIR"

          # Copy built files (flatten dist contents to match infrastructure ExecStart path)
          cp -r dist-monolith/* "$DEPLOY_DIR/"
          cp package.json "$DEPLOY_DIR/"
          cp package-lock.json "$DEPLOY_DIR/"

          # Ensure no node_modules sneaks in
          rm -rf "$DEPLOY_DIR/node_modules"

          # Create deployment archive (smaller, faster)
          tar -czf "$DEPLOY_DIR.tar.gz" "$DEPLOY_DIR"

          # Setup SSH
          mkdir -p ~/.ssh
          echo "${{ secrets.EC2_PRIVATE_KEY }}" > ~/.ssh/monolith-key.pem
            # Normalize line endings if key pasted with CRLF
          sed -i 's/\r$//' ~/.ssh/monolith-key.pem
          chmod 600 ~/.ssh/monolith-key.pem

          # Pre-upload cleanup to ensure we have space
          echo "Cleaning up remote /tmp to ensure upload space..."
          ssh -i ~/.ssh/monolith-key.pem -o StrictHostKeyChecking=no ec2-user@${{ steps.get-deployment-info.outputs.instance_ip }} <<'CLEANUP_EOF'
            set -e
            echo "=== Disk usage before cleanup ==="
            df -h /tmp
            
            # Clean up old deployment files
            sudo find /tmp -name "deploy-*" -type f -mtime +1 -delete 2>/dev/null || true
            sudo find /tmp -name "*.tar.gz" -mtime +1 -delete 2>/dev/null || true
            
            # Remove any existing swap files
            sudo swapoff /tmp/swapfile 2>/dev/null || true
            sudo rm -f /tmp/swapfile 2>/dev/null || true
            
            # Clean npm cache if it exists
            rm -rf /tmp/.npm 2>/dev/null || true
            
            # Clean up any other temp files
            sudo find /tmp -type f -atime +1 -size +10M -delete 2>/dev/null || true
            
            echo "=== Disk usage after cleanup ==="
            df -h /tmp
          CLEANUP_EOF

          # Upload archive to home directory first, then move to /tmp
          echo "Uploading deployment package to home directory..."
          scp -i ~/.ssh/monolith-key.pem -o StrictHostKeyChecking=no "$DEPLOY_DIR.tar.gz" ec2-user@${{ steps.get-deployment-info.outputs.instance_ip }}:~/

          # Step 1: Stop service and move files
          echo "Step 1: Stopping service and moving deployment files..."
          ssh -i ~/.ssh/monolith-key.pem -o StrictHostKeyChecking=no ec2-user@${{ steps.get-deployment-info.outputs.instance_ip }} "
            set -euo pipefail
            DEPLOY_DIR='$DEPLOY_DIR'
            echo '--- Disk usage before deployment ---'
            df -h /
            df -h /tmp || true
            sudo systemctl stop monolith || true

            # Move uploaded file from home to /tmp
            echo 'Moving deployment package to /tmp...'
            mv ~/\"\${DEPLOY_DIR}.tar.gz\" /tmp/

            # Prune old backups (keep last 3)
            PRUNE_LIST=\$(ls -1dt /opt/monolith.backup.* 2>/dev/null | tail -n +4 || true)
            if [ -n \"\$PRUNE_LIST\" ]; then
              echo \"Pruning old backups\"
              echo \"\$PRUNE_LIST\" | xargs -r sudo rm -rf
            fi
          "

          # Step 2: Extract and backup
          echo "Step 2: Extracting deployment package and backing up existing installation..."
          ssh -i ~/.ssh/monolith-key.pem -o StrictHostKeyChecking=no ec2-user@${{ steps.get-deployment-info.outputs.instance_ip }} "
            set -euo pipefail
            DEPLOY_DIR='$DEPLOY_DIR'
            cd /tmp
            echo 'Extracting deployment package...'
            tar -xzf \"\${DEPLOY_DIR}.tar.gz\"
            rm -f \"\${DEPLOY_DIR}.tar.gz\"

            # Backup existing deployment AFTER ensuring extraction succeeded
            if [ -d /opt/monolith ]; then
              echo 'Backing up existing deployment...'
              sudo mv /opt/monolith /opt/monolith.backup.\$(date +%Y%m%d-%H%M%S)
            fi

            echo 'Moving new deployment to /opt/monolith...'
            sudo mv \"\$DEPLOY_DIR\" /opt/monolith
            sudo chown -R ec2-user:ec2-user /opt/monolith

            # Sanity check for built server file
            if [ ! -f /opt/monolith/monolith/server.js ]; then
              echo 'ERROR: /opt/monolith/monolith/server.js missing after deployment' >&2
              ls -al /opt/monolith/monolith || ls -al /opt/monolith
              exit 1
            fi
            echo 'Deployment files extracted and installed successfully'
          "

          # Step 3: Install dependencies
          echo "Step 3: Installing production dependencies..."
          ssh -i ~/.ssh/monolith-key.pem -o StrictHostKeyChecking=no ec2-user@${{ steps.get-deployment-info.outputs.instance_ip }} "
            set -euo pipefail
            cd /opt/monolith
            echo 'Installing production dependencies on remote...'
            
            # Check available memory
            FREE_MEM=\$(free -m | awk 'NR==2{printf \"%.0f\", \$7}')
            echo \"Available memory: \${FREE_MEM}MB\"
            
            # Check available disk space in /tmp
            AVAILABLE_DISK_MB=\$(df -m /tmp | awk 'NR==2 {print \$4}')
            echo \"Available disk space in /tmp: \${AVAILABLE_DISK_MB}MB\"
            
            # Create temporary swap if memory is low (less than 1GB free) and we have disk space
            SWAP_CREATED=false
            if [ \"\$FREE_MEM\" -lt 1000 ]; then
              echo \"Low memory detected. Checking if we can create swap space...\"
              
              # Calculate swap size based on available disk space (use 80% of available, max 2GB)
              MAX_SWAP_MB=\$((AVAILABLE_DISK_MB * 80 / 100))
              if [ \"\$MAX_SWAP_MB\" -gt 2048 ]; then
                MAX_SWAP_MB=2048
              fi
              
              if [ \"\$MAX_SWAP_MB\" -gt 256 ]; then
                echo \"Creating \${MAX_SWAP_MB}MB swap file...\"
                if ! swapon --show | grep -q /tmp/swapfile; then
                  # Use dd instead of fallocate for better compatibility
                  if sudo dd if=/dev/zero of=/tmp/swapfile bs=1M count=\"\$MAX_SWAP_MB\" 2>/dev/null; then
                    sudo chmod 600 /tmp/swapfile
                    if sudo mkswap /tmp/swapfile 2>/dev/null && sudo swapon /tmp/swapfile 2>/dev/null; then
                      SWAP_CREATED=true
                      echo \"Temporary swap space of \${MAX_SWAP_MB}MB created successfully\"
                    else
                      echo \"Warning: Could not activate swap file, removing and proceeding without swap\"
                      sudo rm -f /tmp/swapfile 2>/dev/null || true
                    fi
                  else
                    echo \"Warning: Could not create swap file, proceeding without swap\"
                  fi
                fi
              else
                echo \"Warning: Not enough disk space for meaningful swap (\${MAX_SWAP_MB}MB available), proceeding without swap\"
              fi
            fi
            
            # Install dependencies with memory-friendly options
            export NODE_OPTIONS=\"--max-old-space-size=256\"
            export npm_config_maxsockets=1
            export npm_config_progress=false
            
            echo 'Running npm install...'
            npm i --production --silent --prefer-offline --no-audit --no-fund --maxsockets 1 --cache /tmp/.npm
            
            # Clean up swap if we created it
            if [ \"\$SWAP_CREATED\" = true ]; then
              echo \"Cleaning up temporary swap space...\"
              sudo swapoff /tmp/swapfile || true
              sudo rm -f /tmp/swapfile || true
              echo \"Temporary swap space removed\"
            fi
            
            echo 'Dependencies installed successfully'
          "

          # Step 4: Create environment and service configuration
          echo "Step 4: Creating environment and service configuration..."
          ssh -i ~/.ssh/monolith-key.pem -o StrictHostKeyChecking=no ec2-user@${{ steps.get-deployment-info.outputs.instance_ip }} "
            echo 'NODE_ENV=production' | sudo tee /opt/monolith/.env > /dev/null &&
            echo 'PORT=3000' | sudo tee -a /opt/monolith/.env > /dev/null &&
            echo 'STAGE=stage' | sudo tee -a /opt/monolith/.env > /dev/null &&
            echo 'DATABASE_URL=${{ secrets.DATABASE_URL }}' | sudo tee -a /opt/monolith/.env > /dev/null &&
            echo 'DATABASE_USER=postgres' | sudo tee -a /opt/monolith/.env > /dev/null &&
            echo 'DATABASE_PASSWORD=${{ secrets.DATABASE_PASSWORD }}' | sudo tee -a /opt/monolith/.env > /dev/null &&
            echo 'DATABASE_NAME=postgres' | sudo tee -a /opt/monolith/.env > /dev/null &&
            echo 'DATABASE_PORT=5432' | sudo tee -a /opt/monolith/.env > /dev/null &&
            echo 'REGION=us-east-2' | sudo tee -a /opt/monolith/.env > /dev/null &&
            echo 'COGNITO_USER_POOL_ID=${{ secrets.COGNITO_USER_POOL_ID }}' | sudo tee -a /opt/monolith/.env > /dev/null &&
            echo 'COGNITO_USER_POOL_CLIENT_ID=${{ secrets.COGNITO_USER_POOL_CLIENT_ID }}' | sudo tee -a /opt/monolith/.env > /dev/null &&
            echo 'SENTRY_DSN=https://<EMAIL>/4509911419125840' | sudo tee -a /opt/monolith/.env > /dev/null &&
            echo 'EMAIL_QUEUE_URL=${{ vars.EMAIL_QUEUE_URL }}' | sudo tee -a /opt/monolith/.env > /dev/null &&
            echo 'INBOX_TABLE_NAME=${{ vars.INBOX_TABLE_NAME }}' | sudo tee -a /opt/monolith/.env > /dev/null &&
            echo 'FIXTURE_DETAIL_TABLE_NAME=${{ vars.FIXTURE_DETAIL_TABLE_NAME }}' | sudo tee -a /opt/monolith/.env > /dev/null &&
            echo 'TRACER_ENABLED=true' | sudo tee -a /opt/monolith/.env > /dev/null
          "

          # Update systemd service environment file only (service already exists from infrastructure)
          # Note: The systemd service is created during EC2 initialization and includes log rotation support

          # Step 5: Start service
          echo "Step 5: Starting the monolith service..."
          ssh -i ~/.ssh/monolith-key.pem -o StrictHostKeyChecking=no ec2-user@${{ steps.get-deployment-info.outputs.instance_ip }} "
            set -euo pipefail
            sudo systemctl daemon-reload
            sudo systemctl enable monolith
            sudo systemctl start monolith
            sleep 10
            if ! sudo systemctl is-active --quiet monolith; then
              echo 'Service failed to start. Recent log:' >&2
              sudo tail -n 100 /var/log/monolith.log || true
              sudo journalctl -u monolith -n 50 --no-pager || true
              exit 1
            fi
            sudo systemctl status monolith --no-pager
            echo '--- Disk usage after deployment ---'
            df -h /
            echo 'Deployment completed successfully!'
          "

          # Local cleanup
          rm -rf "$DEPLOY_DIR" "$DEPLOY_DIR.tar.gz" ~/.ssh/monolith-key.pem

