name: Run Tests

on:
  push:
    branches:
      - '**'

jobs:
  test:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run tests
        run: npm run test:ci

      - name: Upload test coverage
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: coverage-report
          path: coverage/
          retention-days: 5

      - name: Upload test results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: test-results
          path: coverage/
          retention-days: 5

      - name: Test Reporter
        uses: dorny/test-reporter@v2.1.0
        if: always()
        with:
          name: Vitest Results
          path: reports/junit.xml
          reporter: jest-junit

  e2e-test:
    runs-on: ubuntu-latest

    services:
      postgres:
        image: postgres:15-alpine
        env:
          POSTGRES_DB: jfg_test
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: postgres
        ports:
          - 5433:5432
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
      - uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Wait for PostgreSQL
        run: |
          until pg_isready -h localhost -p 5433 -U postgres; do
            echo "Waiting for PostgreSQL..."
            sleep 2
          done

      - name: Run E2E tests
        env:
          DATABASE_NAME: jfg_test
          DATABASE_URL: localhost
          DATABASE_PORT: 5433
          DATABASE_USER: postgres
          DATABASE_PASSWORD: postgres
          STAGE: dev
        run: npm run test:e2e:all

      - name: Upload E2E test results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: e2e-test-results
          path: coverage/
          retention-days: 5
