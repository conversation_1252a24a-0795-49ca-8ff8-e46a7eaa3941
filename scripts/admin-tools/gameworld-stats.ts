#!/usr/bin/env node
/**
 * Admin tool to show detailed statistics for a specific gameworld
 *
 * Usage: npm run admin:gameworld-stats <gameworld-id>
 */

import { MikroORM, PostgreSqlDriver } from '@mikro-orm/postgresql';
import 'dotenv/config';
import config from '../../mikro-orm.config.js';
import { logger } from '../../src/utils/logger.js';

interface DetailedStats {
  gameworld: {
    id: string;
    endDate: number;
    highestManageableTier: number;
  };
  leagues: {
    total: number;
    byTier: { tier: number; count: number }[];
  };
  teams: {
    total: number;
    managed: number;
    unmanaged: number;
    byTier: { tier: number; count: number }[];
  };
  players: {
    total: number;
    transferListed: number;
    injured: number;
    suspended: number;
    retiring: number;
  };
  managers: {
    total: number;
    active: number; // logged in within last 7 days
    withTeams: number;
  };
  fixtures: {
    total: number;
    played: number;
    upcoming: number;
  };
  transfers: {
    activeListings: number;
    pendingRequests: number;
    totalBids: number;
  };
  scouting: {
    scoutedPlayers: number;
    pendingRequests: number;
  };
}

async function getGameworldStats(gameworldId: string): Promise<DetailedStats | null> {
  logger.info(`Fetching detailed statistics for gameworld: ${gameworldId}`);

  // Initialize MikroORM
  const orm = await MikroORM.init<PostgreSqlDriver>(config);
  const connection = orm.em.getConnection();

  try {
    // First, verify the gameworld exists
    const gameworldQuery =
      'SELECT id, end_date as "endDate", highest_manageable_tier as "highestManageableTier" FROM gameworld WHERE id = $1';
    const gameworldResult = await connection.execute(gameworldQuery, [gameworldId]);

    if (gameworldResult.length === 0) {
      logger.error(`Gameworld with ID ${gameworldId} does not exist`);
      return null;
    }

    const gameworld = gameworldResult[0];

    // Get league statistics
    const leagueStatsQuery = `
      SELECT 
        COUNT(*) as total,
        json_agg(json_build_object('tier', tier, 'count', count)) as by_tier
      FROM (
        SELECT tier, COUNT(*) as count
        FROM league 
        WHERE gameworld_id = $1
        GROUP BY tier
        ORDER BY tier
      ) tier_counts;
    `;
    const leagueStats = await connection.execute(leagueStatsQuery, [gameworldId]);

    // Get team statistics
    const teamStatsQuery = `
      SELECT 
        COUNT(*) as total,
        COUNT(m.manager_id) as managed,
        COUNT(*) - COUNT(m.manager_id) as unmanaged,
        json_agg(json_build_object('tier', tier, 'count', count)) as by_tier
      FROM team t
      LEFT JOIN manager m ON t.team_id = m.team_id
      LEFT JOIN (
        SELECT tier, COUNT(*) as count
        FROM team 
        WHERE gameworld_id = $1
        GROUP BY tier
        ORDER BY tier
      ) tier_counts ON true
      WHERE t.gameworld_id = $1;
    `;
    const teamStats = await connection.execute(teamStatsQuery, [gameworldId]);

    // Get player statistics
    const playerStatsQuery = `
      SELECT 
        COUNT(*) as total,
        COUNT(*) FILTER (WHERE is_transfer_listed = true) as transfer_listed,
        COUNT(*) FILTER (WHERE injured_until IS NOT NULL AND injured_until > EXTRACT(EPOCH FROM NOW()) * 1000) as injured,
        COUNT(*) FILTER (WHERE suspended_for_games > 0) as suspended,
        COUNT(*) FILTER (WHERE retiring_at_end_of_season = true) as retiring
      FROM players 
      WHERE gameworld_id = $1;
    `;
    const playerStats = await connection.execute(playerStatsQuery, [gameworldId]);

    // Get manager statistics
    const sevenDaysAgo = Date.now() - 7 * 24 * 60 * 60 * 1000;
    const managerStatsQuery = `
      SELECT 
        COUNT(*) as total,
        COUNT(*) FILTER (WHERE last_active > $2) as active,
        COUNT(*) FILTER (WHERE team_id IS NOT NULL) as with_teams
      FROM manager 
      WHERE gameworld_id = $1;
    `;
    const managerStats = await connection.execute(managerStatsQuery, [gameworldId, sevenDaysAgo]);

    // Get fixture statistics
    const fixtureStatsQuery = `
      SELECT 
        COUNT(*) as total,
        COUNT(*) FILTER (WHERE played = true) as played,
        COUNT(*) FILTER (WHERE played = false) as upcoming
      FROM fixture 
      WHERE gameworld_id = $1;
    `;
    const fixtureStats = await connection.execute(fixtureStatsQuery, [gameworldId]);

    // Get transfer statistics
    const transferStatsQuery = `
      SELECT 
        (SELECT COUNT(*) FROM transfer_list WHERE gameworld_id = $1) as active_listings,
        (SELECT COUNT(*) FROM transfer_request tr 
         JOIN players p ON tr.player_id = p.player_id 
         WHERE p.gameworld_id = $1) as pending_requests,
        (SELECT COUNT(*) FROM bid_history bh 
         JOIN transfer_list tl ON bh.transfer_listing_id = tl.id 
         WHERE tl.gameworld_id = $1) as total_bids;
    `;
    const transferStats = await connection.execute(transferStatsQuery, [gameworldId]);

    // Get scouting statistics
    const scoutingStatsQuery = `
      SELECT 
        (SELECT COUNT(*) FROM scouted_players WHERE gameworld_id = $1) as scouted_players,
        (SELECT COUNT(*) FROM scouting_requests WHERE gameworld_id = $1 AND processed = false) as pending_requests;
    `;
    const scoutingStats = await connection.execute(scoutingStatsQuery, [gameworldId]);

    return {
      gameworld: {
        id: gameworldId,
        endDate: gameworld?.endDate,
        highestManageableTier: gameworld?.highestManageableTier,
      },
      leagues: {
        total: leagueStats[0]?.total || 0,
        byTier: leagueStats[0]?.by_tier || [],
      },
      teams: {
        total: teamStats[0]?.total || 0,
        managed: teamStats[0]?.managed || 0,
        unmanaged: teamStats[0]?.unmanaged || 0,
        byTier: teamStats[0]?.by_tier || [],
      },
      players: {
        total: playerStats[0]?.total || 0,
        transferListed: playerStats[0]?.transfer_listed || 0,
        injured: playerStats[0]?.injured || 0,
        suspended: playerStats[0]?.suspended || 0,
        retiring: playerStats[0]?.retiring || 0,
      },
      managers: {
        total: managerStats[0]?.total || 0,
        active: managerStats[0]?.active || 0,
        withTeams: managerStats[0]?.with_teams || 0,
      },
      fixtures: {
        total: fixtureStats[0]?.total || 0,
        played: fixtureStats[0]?.played || 0,
        upcoming: fixtureStats[0]?.upcoming || 0,
      },
      transfers: {
        activeListings: transferStats[0]?.active_listings || 0,
        pendingRequests: transferStats[0]?.pending_requests || 0,
        totalBids: transferStats[0]?.total_bids || 0,
      },
      scouting: {
        scoutedPlayers: scoutingStats[0]?.scouted_players || 0,
        pendingRequests: scoutingStats[0]?.pending_requests || 0,
      },
    };
  } catch (error) {
    logger.error('Failed to fetch gameworld statistics:', { error });
    throw error;
  } finally {
    await orm.close(true);
  }
}

function displayStats(stats: DetailedStats) {
  const endDate = new Date(stats.gameworld.endDate);
  const isActive = endDate.getTime() > Date.now();
  const status = isActive ? '🟢 ACTIVE' : '🔴 ENDED';

  console.log('\n=== GAMEWORLD STATISTICS ===\n');
  console.log(`Status: ${status}`);
  console.log(`ID: ${stats.gameworld.id}`);
  console.log(`End Date: ${endDate.toISOString()}`);
  console.log(`Highest Manageable Tier: ${stats.gameworld.highestManageableTier}`);

  console.log('\n--- LEAGUES ---');
  console.log(`Total: ${stats.leagues.total}`);
  if (stats.leagues.byTier.length > 0) {
    console.log('By Tier:');
    stats.leagues.byTier.forEach((tier) => {
      console.log(`  Tier ${tier.tier}: ${tier.count} leagues`);
    });
  }

  console.log('\n--- TEAMS ---');
  console.log(`Total: ${stats.teams.total}`);
  console.log(`Managed: ${stats.teams.managed}`);
  console.log(`Unmanaged: ${stats.teams.unmanaged}`);
  if (stats.teams.byTier.length > 0) {
    console.log('By Tier:');
    stats.teams.byTier.forEach((tier) => {
      console.log(`  Tier ${tier.tier}: ${tier.count} teams`);
    });
  }

  console.log('\n--- PLAYERS ---');
  console.log(`Total: ${stats.players.total}`);
  console.log(`Transfer Listed: ${stats.players.transferListed}`);
  console.log(`Injured: ${stats.players.injured}`);
  console.log(`Suspended: ${stats.players.suspended}`);
  console.log(`Retiring: ${stats.players.retiring}`);

  console.log('\n--- MANAGERS ---');
  console.log(`Total: ${stats.managers.total}`);
  console.log(`Active (last 7 days): ${stats.managers.active}`);
  console.log(`With Teams: ${stats.managers.withTeams}`);

  console.log('\n--- FIXTURES ---');
  console.log(`Total: ${stats.fixtures.total}`);
  console.log(`Played: ${stats.fixtures.played}`);
  console.log(`Upcoming: ${stats.fixtures.upcoming}`);

  console.log('\n--- TRANSFERS ---');
  console.log(`Active Listings: ${stats.transfers.activeListings}`);
  console.log(`Pending Requests: ${stats.transfers.pendingRequests}`);
  console.log(`Total Bids: ${stats.transfers.totalBids}`);

  console.log('\n--- SCOUTING ---');
  console.log(`Scouted Players: ${stats.scouting.scoutedPlayers}`);
  console.log(`Pending Requests: ${stats.scouting.pendingRequests}`);
}

// Execute the function
(async () => {
  const gameworldId = process.argv[2];

  if (!gameworldId) {
    console.error('Usage: npm run admin:gameworld-stats <gameworld-id>');
    process.exit(1);
  }

  // Validate UUID format
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  if (!uuidRegex.test(gameworldId)) {
    console.error('Invalid gameworld ID format. Must be a valid UUID.');
    process.exit(1);
  }

  const stats = await getGameworldStats(gameworldId);
  if (stats) {
    displayStats(stats);
    process.exit(0);
  } else {
    process.exit(1);
  }
})().catch((error) => {
  logger.error('Failed to get gameworld statistics:', error);
  process.exit(1);
});
