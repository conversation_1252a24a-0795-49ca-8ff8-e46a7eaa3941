#!/usr/bin/env node
/**
 * Admin tool to list all gameworlds and their basic information
 *
 * Usage: npm run admin:list-gameworlds
 */

import { MikroORM, PostgreSqlDriver } from '@mikro-orm/postgresql';
import 'dotenv/config';
import config from '../../mikro-orm.config.js';
import { logger } from '../../src/utils/logger.js';

interface GameworldInfo {
  id: string;
  endDate: number;
  highestManageableTier: number;
  leagueCount: number;
  teamCount: number;
  playerCount: number;
  managerCount: number;
  fixtureCount: number;
}

async function listGameworlds() {
  logger.info('Fetching gameworld information...');

  // Initialize MikroORM
  const orm = await MikroORM.init<PostgreSqlDriver>(config);
  const connection = orm.em.getConnection();

  try {
    // Get all gameworlds with related counts
    const query = `
      SELECT 
        g.id,
        g.end_date as "endDate",
        g.highest_manageable_tier as "highestManageableTier",
        COALESCE(l.league_count, 0) as "leagueCount",
        COALESCE(t.team_count, 0) as "teamCount",
        COALESCE(p.player_count, 0) as "playerCount",
        COALESCE(m.manager_count, 0) as "managerCount",
        COALESCE(f.fixture_count, 0) as "fixtureCount"
      FROM gameworld g
      LEFT JOIN (
        SELECT gameworld_id, COUNT(*) as league_count 
        FROM league 
        GROUP BY gameworld_id
      ) l ON g.id = l.gameworld_id
      LEFT JOIN (
        SELECT gameworld_id, COUNT(*) as team_count 
        FROM team 
        GROUP BY gameworld_id
      ) t ON g.id = t.gameworld_id
      LEFT JOIN (
        SELECT gameworld_id, COUNT(*) as player_count 
        FROM players 
        GROUP BY gameworld_id
      ) p ON g.id = p.gameworld_id
      LEFT JOIN (
        SELECT gameworld_id, COUNT(*) as manager_count 
        FROM manager 
        WHERE gameworld_id IS NOT NULL
        GROUP BY gameworld_id
      ) m ON g.id = m.gameworld_id
      LEFT JOIN (
        SELECT gameworld_id, COUNT(*) as fixture_count 
        FROM fixture 
        GROUP BY gameworld_id
      ) f ON g.id = f.gameworld_id
      ORDER BY g.end_date DESC;
    `;

    const gameworlds = (await connection.execute(query)) as GameworldInfo[];

    if (gameworlds.length === 0) {
      console.log('No gameworlds found.');
      return;
    }

    console.log('\n=== GAMEWORLDS ===\n');

    gameworlds.forEach((gw, index) => {
      const endDate = gw.endDate ? new Date(Number(gw.endDate)) : new Date(0);
      const isActive = endDate.getTime() > Date.now();
      const status = isActive ? '🟢 ACTIVE' : '🔴 ENDED';

      console.log(`${index + 1}. ${status}`);
      console.log(`   ID: ${gw.id}`);
      console.log(`   End Date: ${endDate.toISOString()}`);
      console.log(`   Highest Manageable Tier: ${gw.highestManageableTier}`);
      console.log(`   Leagues: ${gw.leagueCount}`);
      console.log(`   Teams: ${gw.teamCount}`);
      console.log(`   Players: ${gw.playerCount}`);
      console.log(`   Managers: ${gw.managerCount}`);
      console.log(`   Fixtures: ${gw.fixtureCount}`);
      console.log('');
    });

    // Summary
    const totalGameworlds = gameworlds.length;
    const activeGameworlds = gameworlds.filter(
      (gw) => new Date(gw.endDate).getTime() > Date.now()
    ).length;
    const totalTeams = gameworlds.reduce((sum, gw) => sum + Number(gw.teamCount), 0);
    const totalPlayers = gameworlds.reduce((sum, gw) => sum + Number(gw.playerCount), 0);
    const totalManagers = gameworlds.reduce((sum, gw) => sum + Number(gw.managerCount), 0);

    console.log('=== SUMMARY ===');
    console.log(`Total Gameworlds: ${totalGameworlds}`);
    console.log(`Active Gameworlds: ${activeGameworlds}`);
    console.log(`Total Teams: ${totalTeams}`);
    console.log(`Total Players: ${totalPlayers}`);
    console.log(`Total Managers: ${totalManagers}`);
  } catch (error) {
    logger.error('Failed to fetch gameworld information:', { error });
    throw error;
  } finally {
    // Close the ORM connection
    await orm.close(true);
  }
}

// Execute the function
(async () => {
  listGameworlds()
    .then(() => {
      process.exit(0);
    })
    .catch((error) => {
      logger.error('Failed to list gameworlds:', error);
      process.exit(1);
    });
})();
