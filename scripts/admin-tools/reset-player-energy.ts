#!/usr/bin/env node
/**
 * Admin tool to reset player energy to 100 for all players in a gameworld
 * Useful for testing or fixing energy issues
 *
 * Usage: npm run admin:reset-player-energy <gameworld-id>
 */

import { MikroORM, PostgreSqlDriver } from '@mikro-orm/postgresql';
import 'dotenv/config';
import config from '../../mikro-orm.config.js';
import { logger } from '../../src/utils/logger.js';

async function resetPlayerEnergy(gameworldId: string): Promise<boolean> {
  logger.info(`Starting player energy reset for gameworld: ${gameworldId}`);

  const orm = await MikroORM.init<PostgreSqlDriver>(config);
  const connection = orm.em.getConnection();

  try {
    // First, verify the gameworld exists
    const gameworldExists = await connection.execute('SELECT id FROM gameworld WHERE id = $1', [gameworldId]);
    if (gameworldExists.length === 0) {
      logger.error(`Gameworld with ID ${gameworldId} does not exist`);
      return false;
    }

    // Get current energy statistics
    const energyStatsQuery = `
      SELECT 
        COUNT(*) as total_players,
        COUNT(*) FILTER (WHERE energy < 100) as players_needing_reset,
        AVG(energy) as average_energy,
        MIN(energy) as min_energy,
        MAX(energy) as max_energy
      FROM players 
      WHERE gameworld_id = $1;
    `;
    
    const energyStats = await connection.execute(energyStatsQuery, [gameworldId]);
    const stats = energyStats[0];

    logger.info(`Current energy statistics:`);
    logger.info(`  Total players: ${stats.total_players}`);
    logger.info(`  Players needing reset: ${stats.players_needing_reset}`);
    logger.info(`  Average energy: ${Math.round(stats.average_energy * 100) / 100}`);
    logger.info(`  Min energy: ${stats.min_energy}`);
    logger.info(`  Max energy: ${stats.max_energy}`);

    if (stats.players_needing_reset === 0) {
      logger.info('All players already have 100 energy. No reset needed.');
      return true;
    }

    // Start transaction
    await connection.begin();
    logger.info('Started database transaction');

    // Reset energy to 100 for all players with energy < 100
    const resetQuery = 'UPDATE players SET energy = 100 WHERE gameworld_id = $1 AND energy < 100';
    const result = await connection.execute(resetQuery, [gameworldId]);
    const updatedCount = result.affectedRows || 0;

    // Commit transaction
    await connection.commit();
    
    logger.info(`Successfully reset energy for ${updatedCount} players to 100`);
    return true;

  } catch (error) {
    logger.error('Failed to reset player energy:', { error });
    // Rollback transaction
    try {
      await connection.rollback();
      logger.info('Transaction rolled back');
    } catch (rollbackError) {
      logger.error('Error rolling back transaction:', { rollbackError });
    }
    throw error;
  } finally {
    await orm.close(true);
  }
}

async function confirmReset(gameworldId: string) {
  const dbName = process.env.DATABASE_NAME;
  console.log(`Database: ${dbName}`);
  console.log(`You are about to reset ALL player energy to 100 in gameworld: ${gameworldId}`);
  
  const readline = await import('readline');
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout,
  });
  
  return new Promise<boolean>((resolve) => {
    rl.question('Are you sure you want to continue? (yes/no): ', (answer) => {
      rl.close();
      if (answer.trim().toLowerCase() === 'yes') {
        resolve(true);
      } else {
        console.log('Reset cancelled.');
        resolve(false);
      }
    });
  });
}

// Execute the function
(async () => {
  const gameworldId = process.argv[2];
  
  if (!gameworldId) {
    console.error('Usage: npm run admin:reset-player-energy <gameworld-id>');
    process.exit(1);
  }

  // Validate UUID format
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  if (!uuidRegex.test(gameworldId)) {
    console.error('Invalid gameworld ID format. Must be a valid UUID.');
    process.exit(1);
  }

  const confirmed = await confirmReset(gameworldId);
  if (!confirmed) {
    process.exit(0);
  }

  resetPlayerEnergy(gameworldId)
    .then((success) => {
      if (success) {
        logger.info('Player energy reset completed successfully');
        process.exit(0);
      } else {
        process.exit(1);
      }
    })
    .catch((error) => {
      logger.error('Player energy reset failed:', error);
      process.exit(1);
    });
})();
