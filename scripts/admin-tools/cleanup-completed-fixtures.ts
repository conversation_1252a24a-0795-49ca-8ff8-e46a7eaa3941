#!/usr/bin/env node
/**
 * Admin tool to clean up duplicate fixtures
 * Ensures each team only has one fixture at the same time each day
 *
 * Usage: npm run admin:cleanup-fixtures [gameworld-id] [--dry-run]
 */

/**
 * Admin tool to clean up duplicate fixtures
 * Ensures each team only has one fixture at the same time each day
 *
 * Usage: npm run admin:cleanup-fixtures [gameworld-id] [--dry-run]
 */
import { MikroORM, PostgreSqlDriver } from '@mikro-orm/postgresql';
import 'dotenv/config';
import config from '../../mikro-orm.config.js';
import { LeagueMovementService } from '../../src/services/gameworld/league-movement-service.js';
import { logger } from '../../src/utils/logger.js';

// Execute the function
(async () => {
  const args = process.argv.slice(2);
  const gameworldId = args.find((arg) => !arg.startsWith('--'));

  if (gameworldId) {
    // Validate UUID format
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(gameworldId)) {
      console.error('Invalid gameworld ID format. Must be a valid UUID.');
      process.exit(1);
    }
  }

  try {
    const orm = await MikroORM.init<PostgreSqlDriver>(config);
    const service = new LeagueMovementService({});
    await service.optimizeFixtureDeletion(orm.em.fork(), gameworldId!);
    console.log(`\n✅ Cleanup completed.`);

    process.exit(0);
  } catch (error) {
    logger.error('Fixture cleanup failed:', { error });
    process.exit(1);
  }
})();
