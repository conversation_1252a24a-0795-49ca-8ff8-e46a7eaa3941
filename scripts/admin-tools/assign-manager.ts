import { MikroORM, raw } from '@mikro-orm/core';
import mikroOrmConfig from '../../mikro-orm.config.js';
import { AvailableTeam } from '../../src/entities/AvailableTeam.js';
import { Manager } from '../../src/entities/Manager.js';
import { Team } from '../../src/entities/Team.js';
import { logger } from '../../src/utils/logger.js';

async function main() {
  // Gather inputs interactively if not provided
  let managerId = process.argv[2];
  let email = process.argv[3];

  const readline = await import('readline');
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout,
  });

  function ask(question: string): Promise<string> {
    return new Promise((resolve) => {
      rl.question(question, (answer) => resolve(answer.trim()));
    });
  }

  if (!managerId) {
    managerId = await ask('Enter manager ID: ');
  }
  if (!email) {
    email = await ask('Enter manager email: ');
  }
  rl.close();

  if (!managerId || !email) {
    console.error('Manager ID and email are required.');
    process.exit(1);
  }

  const orm = await MikroORM.init(mikroOrmConfig);
  const em = orm.em.fork();

  // Check if manager already exists
  const existingManager = await em.findOne(Manager, { managerId });
  if (existingManager) {
    console.error('Manager already exists:', existingManager);
    await orm.close();
    process.exit(1);
  }

  // Get a random available team
  const qb = em.qb(AvailableTeam, 'at');

  const [availableTeam] = await qb
    .orderBy({ [raw(`RANDOM()`)]: 'ASC' })
    .limit(1)
    .getResult();

  if (!availableTeam) {
    console.error('No available teams found.');
    await orm.close();
    process.exit(1);
  }

  // Remove the team from available teams
  await em.removeAndFlush(availableTeam);

  // Assign the team to the manager
  const manager = new Manager();
  manager.managerId = managerId;
  manager.team = em.getReference(Team, availableTeam.teamId);
  manager.email = email;
  manager.gameworldId = availableTeam.gameworldId;
  manager.scoutTokens = 3;
  manager.superScoutTokens = 0;
  manager.createdAt = Date.now();
  manager.lastActive = Date.now();

  await em.persistAndFlush(manager);

  console.log(
    `Manager ${managerId} assigned to team ${availableTeam.teamId} in gameworld ${availableTeam.gameworldId}`
  );
  await orm.close();
}

main().catch((err) => {
  logger.error('Error assigning manager:', { err });
  process.exit(1);
});
