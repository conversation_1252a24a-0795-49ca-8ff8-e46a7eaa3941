#!/usr/bin/env node
/**
 * Admin tool to list managers in a gameworld with their details
 *
 * Usage: npm run admin:list-managers <gameworld-id>
 */

import { MikroORM, PostgreSqlDriver } from '@mikro-orm/postgresql';
import 'dotenv/config';
import config from '../../mikro-orm.config.js';
import { logger } from '../../src/utils/logger.js';

interface ManagerDetails {
  managerId: string;
  firstName?: string;
  lastName?: string;
  email?: string;
  teamId?: string;
  teamName?: string;
  tier?: number;
  createdAt: number;
  lastActive: number;
  scoutTokens: number;
  superScoutTokens: number;
  magicSponges: number;
  cardAppeals: number;
  trainingBoosts: number;
  role: string;
}

async function listManagers(gameworldId: string) {
  logger.info(`Fetching managers for gameworld: ${gameworldId}`);

  const orm = await MikroORM.init<PostgreSqlDriver>(config);
  const connection = orm.em.getConnection();

  try {
    // First, verify the gameworld exists
    const gameworldExists = await connection.execute('SELECT id FROM gameworld WHERE id = $1', [gameworldId]);
    if (gameworldExists.length === 0) {
      logger.error(`Gameworld with ID ${gameworldId} does not exist`);
      return false;
    }

    // Get all managers in the gameworld
    const query = `
      SELECT 
        m.manager_id as "managerId",
        m.first_name as "firstName",
        m.last_name as "lastName",
        m.email,
        m.team_id as "teamId",
        t.team_name as "teamName",
        t.tier,
        m.created_at as "createdAt",
        m.last_active as "lastActive",
        m.scout_tokens as "scoutTokens",
        m.super_scout_tokens as "superScoutTokens",
        m.magic_sponges as "magicSponges",
        m.card_appeals as "cardAppeals",
        m.training_boosts as "trainingBoosts",
        m.role
      FROM manager m
      LEFT JOIN team t ON m.team_id = t.team_id
      WHERE m.gameworld_id = $1
      ORDER BY m.last_active DESC;
    `;

    const managers = await connection.execute(query, [gameworldId]) as ManagerDetails[];

    if (managers.length === 0) {
      console.log(`No managers found in gameworld ${gameworldId}`);
      return true;
    }

    console.log(`\n=== MANAGERS IN GAMEWORLD ${gameworldId} ===\n`);

    const now = Date.now();
    const oneDayAgo = now - (24 * 60 * 60 * 1000);
    const oneWeekAgo = now - (7 * 24 * 60 * 60 * 1000);

    managers.forEach((manager, index) => {
      const lastActiveDate = new Date(manager.lastActive);
      const createdDate = new Date(manager.createdAt);
      
      // Determine activity status
      let activityStatus = '🔴 Inactive';
      if (manager.lastActive > oneDayAgo) {
        activityStatus = '🟢 Active (24h)';
      } else if (manager.lastActive > oneWeekAgo) {
        activityStatus = '🟡 Recent (7d)';
      }

      console.log(`${index + 1}. ${activityStatus}`);
      console.log(`   ID: ${manager.managerId}`);
      console.log(`   Name: ${manager.firstName || 'N/A'} ${manager.lastName || 'N/A'}`);
      console.log(`   Email: ${manager.email || 'N/A'}`);
      console.log(`   Role: ${manager.role}`);
      
      if (manager.teamId) {
        console.log(`   Team: ${manager.teamName} (Tier ${manager.tier})`);
        console.log(`   Team ID: ${manager.teamId}`);
      } else {
        console.log(`   Team: No team assigned`);
      }
      
      console.log(`   Created: ${createdDate.toISOString()}`);
      console.log(`   Last Active: ${lastActiveDate.toISOString()}`);
      console.log(`   Resources:`);
      console.log(`     Scout Tokens: ${manager.scoutTokens}`);
      console.log(`     Super Scout Tokens: ${manager.superScoutTokens}`);
      console.log(`     Magic Sponges: ${manager.magicSponges}`);
      console.log(`     Card Appeals: ${manager.cardAppeals}`);
      console.log(`     Training Boosts: ${manager.trainingBoosts}`);
      console.log('');
    });

    // Summary statistics
    const totalManagers = managers.length;
    const managersWithTeams = managers.filter(m => m.teamId).length;
    const managersWithoutTeams = totalManagers - managersWithTeams;
    const activeManagers = managers.filter(m => m.lastActive > oneDayAgo).length;
    const recentManagers = managers.filter(m => m.lastActive > oneWeekAgo && m.lastActive <= oneDayAgo).length;
    const inactiveManagers = managers.filter(m => m.lastActive <= oneWeekAgo).length;
    const adminManagers = managers.filter(m => m.role === 'admin').length;

    console.log('=== SUMMARY ===');
    console.log(`Total Managers: ${totalManagers}`);
    console.log(`With Teams: ${managersWithTeams}`);
    console.log(`Without Teams: ${managersWithoutTeams}`);
    console.log(`Active (24h): ${activeManagers}`);
    console.log(`Recent (7d): ${recentManagers}`);
    console.log(`Inactive (>7d): ${inactiveManagers}`);
    console.log(`Admins: ${adminManagers}`);

    // Tier distribution for managers with teams
    if (managersWithTeams > 0) {
      const tierCounts = managers
        .filter(m => m.tier !== undefined)
        .reduce((acc, m) => {
          acc[m.tier!] = (acc[m.tier!] || 0) + 1;
          return acc;
        }, {} as Record<number, number>);

      console.log('\nTier Distribution:');
      Object.keys(tierCounts)
        .sort((a, b) => Number(a) - Number(b))
        .forEach(tier => {
          console.log(`  Tier ${tier}: ${tierCounts[Number(tier)]} managers`);
        });
    }

    return true;

  } catch (error) {
    logger.error('Failed to fetch managers:', { error });
    throw error;
  } finally {
    await orm.close(true);
  }
}

// Execute the function
(async () => {
  const gameworldId = process.argv[2];
  
  if (!gameworldId) {
    console.error('Usage: npm run admin:list-managers <gameworld-id>');
    process.exit(1);
  }

  // Validate UUID format
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  if (!uuidRegex.test(gameworldId)) {
    console.error('Invalid gameworld ID format. Must be a valid UUID.');
    process.exit(1);
  }

  listManagers(gameworldId)
    .then((success) => {
      if (success) {
        process.exit(0);
      } else {
        process.exit(1);
      }
    })
    .catch((error) => {
      logger.error('Failed to list managers:', error);
      process.exit(1);
    });
})();
