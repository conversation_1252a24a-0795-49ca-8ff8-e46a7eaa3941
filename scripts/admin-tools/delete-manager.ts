#!/usr/bin/env node
/**
 * Admin tool to delete a manager and clean up their associated data
 * This will remove the manager and free up their team for other players
 *
 * Usage: npm run admin:delete-manager <manager-id>
 */

import { MikroORM, PostgreSqlDriver } from '@mikro-orm/postgresql';
import 'dotenv/config';
import config from '../../mikro-orm.config.js';
import { logger } from '../../src/utils/logger.js';

interface ManagerInfo {
  managerId: string;
  firstName?: string;
  lastName?: string;
  email?: string;
  gameworldId?: string;
  teamId?: string;
  teamName?: string;
  createdAt: number;
  lastActive: number;
}

async function getManagerInfo(managerId: string): Promise<ManagerInfo | null> {
  const orm = await MikroORM.init<PostgreSqlDriver>(config);
  const connection = orm.em.getConnection();

  try {
    const query = `
      SELECT 
        m.manager_id as "managerId",
        m.first_name as "firstName",
        m.last_name as "lastName",
        m.email,
        m.gameworld_id as "gameworldId",
        m.team_id as "teamId",
        t.team_name as "teamName",
        m.created_at as "createdAt",
        m.last_active as "lastActive"
      FROM manager m
      LEFT JOIN team t ON m.team_id = t.team_id
      WHERE m.manager_id = $1;
    `;

    const result = await connection.execute(query, [managerId]);
    return result.length > 0 ? result[0] : null;
  } finally {
    await orm.close(true);
  }
}

async function deleteManager(managerId: string): Promise<boolean> {
  logger.info(`Starting manager deletion process for ID: ${managerId}`);

  const orm = await MikroORM.init<PostgreSqlDriver>(config);
  const connection = orm.em.getConnection();

  try {
    // Get manager info first
    const managerInfo = await getManagerInfo(managerId);
    if (!managerInfo) {
      logger.error(`Manager with ID ${managerId} does not exist`);
      return false;
    }

    logger.info(`Found manager: ${managerInfo.firstName} ${managerInfo.lastName} (${managerInfo.email})`);
    if (managerInfo.teamId) {
      logger.info(`Manager has team: ${managerInfo.teamName} (${managerInfo.teamId})`);
    }

    // Start transaction
    await connection.begin();
    logger.info('Started database transaction');

    let deletedRecords = 0;

    // 1. Delete scouted players for this manager's team
    if (managerInfo.teamId && managerInfo.gameworldId) {
      const scoutedPlayersResult = await connection.execute(
        'DELETE FROM scouted_players WHERE gameworld_id = $1 AND team_team_id = $2',
        [managerInfo.gameworldId, managerInfo.teamId]
      );
      const scoutedDeleted = scoutedPlayersResult.affectedRows || 0;
      deletedRecords += scoutedDeleted;
      if (scoutedDeleted > 0) {
        logger.info(`Deleted ${scoutedDeleted} scouted player records`);
      }

      // 2. Delete scouting requests for this manager's team
      const scoutingRequestsResult = await connection.execute(
        'DELETE FROM scouting_requests WHERE gameworld_id = $1 AND team_team_id = $2',
        [managerInfo.gameworldId, managerInfo.teamId]
      );
      const scoutingDeleted = scoutingRequestsResult.affectedRows || 0;
      deletedRecords += scoutingDeleted;
      if (scoutingDeleted > 0) {
        logger.info(`Deleted ${scoutingDeleted} scouting request records`);
      }

      // 3. Delete transfer requests where this team is buyer or seller
      const transferRequestsResult = await connection.execute(
        'DELETE FROM transfer_request WHERE "buyerTeam" = $1 OR "sellerTeam" = $1',
        [managerInfo.teamId]
      );
      const transferDeleted = transferRequestsResult.affectedRows || 0;
      deletedRecords += transferDeleted;
      if (transferDeleted > 0) {
        logger.info(`Deleted ${transferDeleted} transfer request records`);
      }

      // 4. Delete bid history for this team
      const bidHistoryResult = await connection.execute(
        'DELETE FROM bid_history WHERE team_team_id = $1',
        [managerInfo.teamId]
      );
      const bidsDeleted = bidHistoryResult.affectedRows || 0;
      deletedRecords += bidsDeleted;
      if (bidsDeleted > 0) {
        logger.info(`Deleted ${bidsDeleted} bid history records`);
      }

      // 5. Delete transactions for this team
      const transactionsResult = await connection.execute(
        'DELETE FROM transactions WHERE team_team_id = $1',
        [managerInfo.teamId]
      );
      const transactionsDeleted = transactionsResult.affectedRows || 0;
      deletedRecords += transactionsDeleted;
      if (transactionsDeleted > 0) {
        logger.info(`Deleted ${transactionsDeleted} transaction records`);
      }

      // 6. Add team to available teams if it's not already there
      const availableTeamResult = await connection.execute(
        'INSERT INTO available_team (id, gameworld_id, team_id) VALUES (gen_random_uuid(), $1, $2) ON CONFLICT (gameworld_id, team_id) DO NOTHING',
        [managerInfo.gameworldId, managerInfo.teamId]
      );
      const availableAdded = availableTeamResult.affectedRows || 0;
      if (availableAdded > 0) {
        logger.info(`Added team to available teams`);
      }
    }

    // 7. Delete purchases for this manager
    const purchasesResult = await connection.execute(
      'DELETE FROM purchases WHERE manager_id = $1',
      [managerId]
    );
    const purchasesDeleted = purchasesResult.affectedRows || 0;
    deletedRecords += purchasesDeleted;
    if (purchasesDeleted > 0) {
      logger.info(`Deleted ${purchasesDeleted} purchase records`);
    }

    // 8. Finally, delete the manager
    const managerResult = await connection.execute(
      'DELETE FROM manager WHERE manager_id = $1',
      [managerId]
    );
    const managerDeleted = managerResult.affectedRows || 0;
    deletedRecords += managerDeleted;

    if (managerDeleted === 0) {
      throw new Error('Failed to delete manager record');
    }

    // Commit transaction
    await connection.commit();
    logger.info(`Successfully deleted manager ${managerId} and cleaned up associated data`);
    logger.info(`Total records deleted/modified: ${deletedRecords}`);
    
    if (managerInfo.teamId) {
      logger.info(`Team ${managerInfo.teamName} is now available for new managers`);
    }
    
    return true;

  } catch (error) {
    logger.error('Failed to delete manager:', { error });
    // Rollback transaction
    try {
      await connection.rollback();
      logger.info('Transaction rolled back');
    } catch (rollbackError) {
      logger.error('Error rolling back transaction:', { rollbackError });
    }
    throw error;
  } finally {
    await orm.close(true);
  }
}

async function confirmDeletion(managerInfo: ManagerInfo) {
  const dbName = process.env.DATABASE_NAME;
  console.log(`Database: ${dbName}`);
  console.log(`You are about to DELETE manager:`);
  console.log(`  ID: ${managerInfo.managerId}`);
  console.log(`  Name: ${managerInfo.firstName} ${managerInfo.lastName}`);
  console.log(`  Email: ${managerInfo.email}`);
  if (managerInfo.teamId) {
    console.log(`  Team: ${managerInfo.teamName} (${managerInfo.teamId})`);
  }
  console.log(`  Created: ${new Date(managerInfo.createdAt).toISOString()}`);
  console.log(`  Last Active: ${new Date(managerInfo.lastActive).toISOString()}`);
  console.log('\nThis action is IRREVERSIBLE!');
  
  const readline = await import('readline');
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout,
  });
  
  return new Promise<boolean>((resolve) => {
    rl.question('Are you absolutely sure you want to continue? Type "DELETE" to confirm: ', (answer) => {
      rl.close();
      if (answer.trim() === 'DELETE') {
        resolve(true);
      } else {
        console.log('Deletion cancelled.');
        resolve(false);
      }
    });
  });
}

// Execute the function
(async () => {
  const managerId = process.argv[2];
  
  if (!managerId) {
    console.error('Usage: npm run admin:delete-manager <manager-id>');
    process.exit(1);
  }

  // Validate UUID format
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  if (!uuidRegex.test(managerId)) {
    console.error('Invalid manager ID format. Must be a valid UUID.');
    process.exit(1);
  }

  // Get manager info for confirmation
  const managerInfo = await getManagerInfo(managerId);
  if (!managerInfo) {
    console.error(`Manager with ID ${managerId} does not exist.`);
    process.exit(1);
  }

  const confirmed = await confirmDeletion(managerInfo);
  if (!confirmed) {
    process.exit(0);
  }

  deleteManager(managerId)
    .then((success) => {
      if (success) {
        logger.info('Manager deletion completed successfully');
        process.exit(0);
      } else {
        process.exit(1);
      }
    })
    .catch((error) => {
      logger.error('Manager deletion failed:', error);
      process.exit(1);
    });
})();
