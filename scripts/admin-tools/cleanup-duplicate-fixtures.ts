#!/usr/bin/env node
/**
 * Admin tool to clean up duplicate fixtures
 * Ensures each team only has one fixture at the same time each day
 *
 * Usage: npm run admin:cleanup-fixtures [gameworld-id] [--dry-run]
 */

/**
 * Admin tool to clean up duplicate fixtures
 * Ensures each team only has one fixture at the same time each day
 *
 * Usage: npm run admin:cleanup-fixtures [gameworld-id] [--dry-run]
 */
import { MikroORM, PostgreSqlDriver } from '@mikro-orm/postgresql';
import 'dotenv/config';
import config from '../../mikro-orm.config.js';
import { Fixture } from '../../src/entities/Fixture.js';
import { logger } from '../../src/utils/logger.js';

interface DuplicateFixture {
  fixtureId: string;
  gameworldId: string;
  leagueId: string;
  homeTeamId: string;
  awayTeamId: string;
  date: number;
  played: boolean;
  simulatedAt?: number;
  duplicateGroup: string; // identifier for the duplicate group
}

interface DuplicateGroup {
  key: string;
  fixtures: DuplicateFixture[];
  teamsInvolved: string[];
  date: number;
}

async function findDuplicateFixtures(gameworldId?: string): Promise<DuplicateGroup[]> {
  logger.info('Scanning for duplicate fixtures...');

  const orm = await MikroORM.init<PostgreSqlDriver>(config);
  const connection = orm.em.getConnection();

  try {
    // Build query with optional gameworld filter
    let whereClause = 'WHERE f.played = false'; // Only look at unplayed fixtures
    const params: string[] = [];

    if (gameworldId) {
      whereClause += ' AND f.gameworld_id = ?';
      params.push(gameworldId);
    }

    // Simplified approach: Find all fixtures where teams have conflicts at the same time
    const query = `
      SELECT
        f1.fixture_id as "fixtureId",
        f1.gameworld_id as "gameworldId",
        f1.league_id as "leagueId",
        f1.home_team_team_id as "homeTeamId",
        f1.away_team_team_id as "awayTeamId",
        f1.date,
        f1.played,
        f1.simulated_at as "simulatedAt"
      FROM fixture f1
      ${whereClause.replace('f.', 'f1.')}
      AND EXISTS (
        SELECT 1 FROM fixture f2
        WHERE f2.fixture_id != f1.fixture_id
        AND f2.date = f1.date
        AND f2.played = false
        AND (
          f2.home_team_team_id = f1.home_team_team_id
          OR f2.home_team_team_id = f1.away_team_team_id
          OR f2.away_team_team_id = f1.home_team_team_id
          OR f2.away_team_team_id = f1.away_team_team_id
        )
        ${gameworldId ? 'AND f2.gameworld_id = f1.gameworld_id' : ''}
      )
      ORDER BY f1.gameworld_id, f1.date, f1.home_team_team_id, f1.away_team_team_id;
    `;

    const duplicateFixtures = (await connection.execute(query, params)) as DuplicateFixture[];

    if (duplicateFixtures.length === 0) {
      logger.info('No duplicate fixtures found');
      return [];
    }

    // Group duplicates by gameworld, date, and teams involved
    const groupMap: { [key: string]: DuplicateGroup } = {};

    for (const fixture of duplicateFixtures) {
      // Create a key that represents the conflict scenario
      const teamsInvolved = [fixture.homeTeamId, fixture.awayTeamId].sort();
      const groupKey = `${fixture.gameworldId}_${fixture.date}_${teamsInvolved.join('_')}`;

      if (!groupMap[groupKey]) {
        groupMap[groupKey] = {
          key: groupKey,
          fixtures: [],
          teamsInvolved,
          date: fixture.date,
        };
      }

      groupMap[groupKey].fixtures.push({
        ...fixture,
        duplicateGroup: groupKey,
      });
    }

    // Also find fixtures where the same teams are involved at the same time
    const duplicateGroups: DuplicateGroup[] = [];

    for (const groupKey in groupMap) {
      const group = groupMap[groupKey];
      // Find all fixtures at this time involving any of these teams
      const teamList = group.teamsInvolved.map((t) => `'${t}'`).join(',');
      const timeQuery = `
        SELECT 
          f.fixture_id as "fixtureId",
          f.gameworld_id as "gameworldId",
          f.league_id as "leagueId", 
          f.home_team_team_id as "homeTeamId",
          f.away_team_team_id as "awayTeamId",
          f.date,
          f.played,
          f.simulated_at as "simulatedAt"
        FROM fixture f
        WHERE f.gameworld_id = ? 
        AND f.date = ?
        AND f.played = false
        AND (f.home_team_team_id IN (${teamList}) OR f.away_team_team_id IN (${teamList}))
        ORDER BY f.fixture_id;
      `;

      const timeFixtures = (await connection.execute(timeQuery, [
        group.fixtures[0].gameworldId,
        group.date,
      ])) as DuplicateFixture[];

      if (timeFixtures.length > 1) {
        const allTeamsInvolved = new Set<string>();
        timeFixtures.forEach((f) => {
          allTeamsInvolved.add(f.homeTeamId);
          allTeamsInvolved.add(f.awayTeamId);
        });

        duplicateGroups.push({
          key: `${group.fixtures[0].gameworldId}_${group.date}_${Array.from(allTeamsInvolved).sort().join('_')}`,
          fixtures: timeFixtures.map((f) => ({ ...f, duplicateGroup: group.key })),
          teamsInvolved: Array.from(allTeamsInvolved),
          date: group.date,
        });
      }
    }

    return duplicateGroups;
  } catch (e) {
    console.error(e);
  } finally {
    await orm.close(true);
  }
}

async function cleanupDuplicates(
  duplicateGroups: DuplicateGroup[],
  dryRun: boolean = false
): Promise<number> {
  if (duplicateGroups.length === 0) {
    logger.info('No duplicates to clean up');
    return 0;
  }

  const orm = await MikroORM.init<PostgreSqlDriver>(config);

  try {
    let totalDeleted = 0;

    if (dryRun) {
      // For dry run, just count what would be deleted
      for (const group of duplicateGroups) {
        const fixtures = group.fixtures;
        if (fixtures.length <= 1) continue;

        // Sort fixtures to determine which one to keep
        fixtures.sort((a, b) => {
          // Keep non-simulated fixtures over simulated ones
          if (a.simulatedAt && !b.simulatedAt) return 1;
          if (!a.simulatedAt && b.simulatedAt) return -1;

          // If both are simulated or both are not, keep the earliest created (by fixture ID)
          return a.fixtureId.localeCompare(b.fixtureId);
        });

        const fixtureToKeep = fixtures[0];
        const fixturesToDelete = fixtures.slice(1);

        logger.info(`Duplicate group at ${new Date(Number(group.date)).toISOString()}:`);
        logger.info(`  Teams involved: ${group.teamsInvolved.join(', ')}`);
        logger.info(
          `  Would keep fixture: ${fixtureToKeep.fixtureId} (${fixtureToKeep.homeTeamId} vs ${fixtureToKeep.awayTeamId})`
        );
        logger.info(`  Would delete ${fixturesToDelete.length} duplicate(s)`);

        for (const fixture of fixturesToDelete) {
          logger.info(
            `    Would delete: ${fixture.fixtureId} (${fixture.homeTeamId} vs ${fixture.awayTeamId})`
          );
          totalDeleted++;
        }
      }

      logger.info(`DRY RUN: Would delete ${totalDeleted} duplicate fixtures`);
      return totalDeleted;
    }

    // For actual deletion, use transaction
    const em = orm.em.fork();

    return await em.transactional(async (txEm) => {
      logger.info('Started database transaction');

      for (const group of duplicateGroups) {
        const fixtures = group.fixtures;

        if (fixtures.length <= 1) continue;

        // Sort fixtures to determine which one to keep
        fixtures.sort((a, b) => {
          // Keep non-simulated fixtures over simulated ones
          if (a.simulatedAt && !b.simulatedAt) return 1;
          if (!a.simulatedAt && b.simulatedAt) return -1;

          // If both are simulated or both are not, keep the earliest created (by fixture ID)
          return a.fixtureId.localeCompare(b.fixtureId);
        });

        const fixtureToKeep = fixtures[0];
        const fixturesToDelete = fixtures.slice(1);

        logger.info(`Duplicate group at ${new Date(Number(group.date)).toISOString()}:`);
        logger.info(`  Teams involved: ${group.teamsInvolved.join(', ')}`);
        logger.info(
          `  Keeping fixture: ${fixtureToKeep.fixtureId} (${fixtureToKeep.homeTeamId} vs ${fixtureToKeep.awayTeamId})`
        );
        logger.info(`  Deleting ${fixturesToDelete.length} duplicate(s)`);

        for (const fixture of fixturesToDelete) {
          logger.info(
            `    Deleting: ${fixture.fixtureId} (${fixture.homeTeamId} vs ${fixture.awayTeamId})`
          );

          // Use the transactional entity manager to delete
          await txEm.nativeDelete(Fixture, { fixtureId: fixture.fixtureId });
          totalDeleted++;
        }
      }

      logger.info(`Successfully deleted ${totalDeleted} duplicate fixtures`);
      return totalDeleted;
    });
  } catch (error) {
    logger.error('Failed to cleanup duplicate fixtures:', { error });
    throw error;
  } finally {
    await orm.close(true);
  }
}

function displayDuplicates(duplicateGroups: DuplicateGroup[]) {
  if (duplicateGroups.length === 0) {
    console.log('✅ No duplicate fixtures found!');
    return;
  }

  console.log(`\n🔍 Found ${duplicateGroups.length} groups of duplicate fixtures:\n`);

  duplicateGroups.forEach((group, index) => {
    const date = new Date(Number(group.date));
    console.log(`${index + 1}. Conflict at ${date.toISOString()}`);
    console.log(`   Teams involved: ${group.teamsInvolved.join(', ')}`);
    console.log(`   Duplicate fixtures (${group.fixtures.length}):`);

    group.fixtures.forEach((fixture, fIndex) => {
      const status = fixture.simulatedAt ? '🎮 Simulated' : '⏳ Pending';
      console.log(
        `     ${fIndex + 1}. ${fixture.fixtureId} - ${fixture.homeTeamId} vs ${fixture.awayTeamId} ${status}`
      );
    });
    console.log('');
  });
}

async function confirmCleanup(duplicateGroups: DuplicateGroup[], dryRun: boolean) {
  if (dryRun) return true;

  const totalToDelete = duplicateGroups.reduce(
    (sum, group) => sum + Math.max(0, group.fixtures.length - 1),
    0
  );

  console.log(`You are about to DELETE ${totalToDelete} duplicate fixtures.`);
  console.log('This action is IRREVERSIBLE!');

  const readline = await import('readline');
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout,
  });

  return new Promise<boolean>((resolve) => {
    rl.question('Are you sure you want to continue? Type "DELETE" to confirm: ', (answer) => {
      rl.close();
      if (answer.trim() === 'DELETE') {
        resolve(true);
      } else {
        console.log('Cleanup cancelled.');
        resolve(false);
      }
    });
  });
}

// Execute the function
(async () => {
  const args = process.argv.slice(2);
  const gameworldId = args.find((arg) => !arg.startsWith('--'));
  const dryRun = args.includes('--dry-run');

  if (gameworldId) {
    // Validate UUID format
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(gameworldId)) {
      console.error('Invalid gameworld ID format. Must be a valid UUID.');
      process.exit(1);
    }
  }

  if (dryRun) {
    console.log('🔍 DRY RUN MODE - No changes will be made\n');
  }

  try {
    const duplicateGroups = await findDuplicateFixtures(gameworldId);
    displayDuplicates(duplicateGroups);

    if (duplicateGroups.length > 0) {
      const confirmed = await confirmCleanup(duplicateGroups, dryRun);
      if (confirmed) {
        const deletedCount = await cleanupDuplicates(duplicateGroups, dryRun);
        console.log(
          `\n✅ Cleanup completed. ${dryRun ? 'Would delete' : 'Deleted'} ${deletedCount} fixtures.`
        );
      }
    }

    process.exit(0);
  } catch (error) {
    logger.error('Fixture cleanup failed:', error);
    process.exit(1);
  }
})();
