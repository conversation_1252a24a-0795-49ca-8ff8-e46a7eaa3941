#!/usr/bin/env node
/**
 * Admin tool to delete a gameworld and all its associated data
 * This script will delete all data related to a specific gameworld ID
 *
 * Usage: npm run admin:delete-gameworld <gameworld-id>
 */

import { MikroORM, PostgreSqlDriver } from '@mikro-orm/postgresql';
import 'dotenv/config';
import config from '../../mikro-orm.config.js';
import { logger } from '../../src/utils/logger.js';

// Define the order of table deletion based on foreign key dependencies
// Tables with foreign keys should be deleted before the tables they reference
const DELETION_ORDER = [
  // Child tables first
  {
    table: 'bid_history',
    joinCondition: 'transfer_listing_id IN (SELECT id FROM transfer_list WHERE gameworld_id = ?)',
  },
  {
    table: 'player_match_history',
    joinCondition: 'player_id IN (SELECT player_id FROM players WHERE gameworld_id = ?)',
  },
  {
    table: 'player_overall_stats',
    joinCondition: 'player_id IN (SELECT player_id FROM players WHERE gameworld_id = ?)',
  },
  {
    table: 'player_attributes',
    joinCondition: 'player_id IN (SELECT player_id FROM players WHERE gameworld_id = ?)',
  },
  {
    table: 'transactions',
    joinCondition: 'team_id IN (SELECT team_id FROM team WHERE gameworld_id = ?)',
  },
  { table: 'fixture', condition: 'gameworld_id = ?' },
  { table: 'scouted_players', condition: 'gameworld_id = ?' },
  { table: 'scouting_requests', condition: 'gameworld_id = ?' },
  { table: 'transfer_list', condition: 'gameworld_id = ?' },
  {
    table: 'transfer_request',
    joinCondition: 'player_id IN (SELECT player_id FROM players WHERE gameworld_id = ?)',
  },
  { table: 'players', condition: 'gameworld_id = ?' },
  { table: 'available_team', condition: 'gameworld_id = ?' },
  {
    table: 'team_training_slots',
    joinCondition: 'team_team_id IN (SELECT team_id FROM team WHERE gameworld_id = ?)',
  },
  { table: 'manager', condition: 'gameworld_id = ?' },
  { table: 'team', condition: 'gameworld_id = ?' },
  {
    table: 'league_rules',
    joinCondition: 'league_id IN (SELECT id FROM league WHERE gameworld_id = ?)',
  },
  {
    table: 'league_children',
    joinCondition:
      'parent_league_id IN (SELECT id FROM league WHERE gameworld_id = ?) OR child_league_id IN (SELECT id FROM league WHERE gameworld_id = ?)',
  },
  { table: 'league', joinCondition: 'gameworld_id = ?' },
  { table: 'gameworld', condition: 'id = ?' },
];

async function deleteGameworld(gameworldId: string) {
  logger.info(`Starting gameworld deletion process for ID: ${gameworldId}`);

  // Initialize MikroORM
  logger.info('Connecting to database...');
  const orm = await MikroORM.init<PostgreSqlDriver>(config);
  const connection = orm.em.getConnection();
  let transaction: any;
  try {
    // First, verify the gameworld exists
    const gameworldExists = await connection.execute('SELECT id FROM gameworld WHERE id = ?', [
      gameworldId,
    ]);
    if (gameworldExists.length === 0) {
      logger.error(`Gameworld with ID ${gameworldId} does not exist`);
      return false;
    }

    logger.info(`Found gameworld with ID: ${gameworldId}`);

    // Start transaction
    transaction = await connection.begin();
    logger.info('Started database transaction');

    let totalDeleted = 0;

    // Delete data in the defined order
    for (const { table, condition, joinCondition } of DELETION_ORDER) {
      try {
        let query: string;
        if (condition) {
          query = `DELETE FROM "${table}" WHERE ${condition}`;
        } else if (joinCondition) {
          query = `DELETE FROM "${table}" WHERE ${joinCondition}`;
        } else {
          throw new Error(`No deletion condition defined for table ${table}`);
        }

        logger.info(`Deleting from table: ${table}`);
        const result = await connection.execute(query, [gameworldId]);
        const deletedCount = result.length || 0;
        totalDeleted += deletedCount;

        if (deletedCount > 0) {
          logger.info(`Deleted ${deletedCount} records from table: ${table}`);
        } else {
          logger.debug(`No records to delete from table: ${table}`);
        }
      } catch (error) {
        logger.error(`Error deleting from table ${table}:`, { error });
        throw error;
      }
    }

    // Commit transaction
    await connection.commit(transaction);
    logger.info(`Successfully deleted gameworld ${gameworldId} and all associated data`);
    logger.info(`Total records deleted: ${totalDeleted}`);
    return true;
  } catch (error) {
    logger.error('Failed to delete gameworld:', { error });
    // Rollback transaction
    try {
      await connection.rollback(transaction);
      logger.info('Transaction rolled back');
    } catch (rollbackError) {
      logger.error('Error rolling back transaction:', { rollbackError });
    }
    throw error;
  } finally {
    // Close the ORM connection
    logger.info('Closing database connection...');
    await orm.close(true);
    logger.info('Database connection closed');
  }
}

async function confirmDeletion(gameworldId: string) {
  const dbName = process.env.DATABASE_NAME;
  if (!dbName) {
    console.error('DATABASE_NAME environment variable is not set.');
    process.exit(1);
  }

  console.log(`Database: ${dbName}`);
  console.log(`You are about to DELETE gameworld: ${gameworldId} and ALL its associated data.`);
  console.log('This action is IRREVERSIBLE!');

  const readline = await import('readline');
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout,
  });

  return new Promise<boolean>((resolve) => {
    rl.question(
      'Are you absolutely sure you want to continue? Type "DELETE" to confirm: ',
      (answer) => {
        rl.close();
        if (answer.trim() === 'DELETE') {
          resolve(true);
        } else {
          console.log('Deletion cancelled.');
          resolve(false);
        }
      }
    );
  });
}

// Execute the function
(async () => {
  const gameworldId = process.argv[2];

  if (!gameworldId) {
    console.error('Usage: npm run admin:delete-gameworld <gameworld-id>');
    process.exit(1);
  }

  // Validate UUID format
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  if (!uuidRegex.test(gameworldId)) {
    console.error('Invalid gameworld ID format. Must be a valid UUID.');
    process.exit(1);
  }

  const confirmed = await confirmDeletion(gameworldId);
  if (!confirmed) {
    process.exit(0);
  }

  deleteGameworld(gameworldId)
    .then((success) => {
      if (success) {
        logger.info('Gameworld deletion completed successfully');
        process.exit(0);
      } else {
        process.exit(1);
      }
    })
    .catch((error) => {
      logger.error('Gameworld deletion failed:', error);
      process.exit(1);
    });
})();
