# Admin Tools for Jumpers for Goalposts Backend

This directory contains administrative tools for managing the Jumpers for Goalposts game database. These tools are designed to be run locally by administrators for maintenance, debugging, and data management tasks.

## Prerequisites

- Node.js and npm installed
- Proper environment variables configured in `.env` file:
  - `DATABASE_NAME`
  - `DATABASE_URL`
  - `DATABASE_USER`
  - `DATABASE_PASSWORD`
  - `DATABASE_PORT` (optional, defaults to 5432)

## Available Tools

### Interactive Menu
```bash
npm run admin:menu
```
Provides an interactive menu interface to access all admin tools. This is the recommended way to use the admin tools as it provides a user-friendly interface with input validation.

### Individual Tools

#### 1. List Gameworlds
```bash
npm run admin:list-gameworlds
```
Lists all gameworlds in the database with basic statistics including:
- Gameworld ID and status (active/ended)
- End date and highest manageable tier
- Counts of leagues, teams, players, managers, and fixtures
- Summary statistics across all gameworlds

#### 2. Gameworld Statistics
```bash
npm run admin:gameworld-stats <gameworld-id>
```
Shows detailed statistics for a specific gameworld including:
- League distribution by tier
- Team statistics (managed vs unmanaged, by tier)
- Player statistics (transfer listed, injured, suspended, retiring)
- Manager statistics (total, active, with teams)
- Fixture statistics (total, played, upcoming)
- Transfer market statistics (active listings, pending requests, bids)
- Scouting statistics

#### 3. List Managers
```bash
npm run admin:list-managers <gameworld-id>
```
Lists all managers in a specific gameworld with details:
- Manager information (name, email, role)
- Team assignment and tier
- Activity status (active, recent, inactive)
- Resource counts (scout tokens, magic sponges, etc.)
- Summary statistics and tier distribution

#### 4. Delete Gameworld
```bash
npm run admin:delete-gameworld <gameworld-id>
```
**⚠️ DESTRUCTIVE OPERATION ⚠️**

Completely deletes a gameworld and ALL associated data including:
- All players, teams, and leagues in the gameworld
- All managers associated with the gameworld
- All fixtures, transfer listings, and bids
- All scouting data and requests
- All transactions and training data
- The gameworld itself

This operation is **IRREVERSIBLE** and requires explicit confirmation.

#### 5. Delete Manager
```bash
npm run admin:delete-manager <manager-id>
```
**⚠️ DESTRUCTIVE OPERATION ⚠️**

Deletes a specific manager and cleans up their associated data:
- Removes all scouted players for their team
- Deletes all scouting requests from their team
- Removes transfer requests involving their team
- Deletes bid history for their team
- Removes transaction history
- Adds their team back to available teams (if they had one)
- Deletes purchase records
- Removes the manager record

This operation is **IRREVERSIBLE** and requires explicit confirmation.

#### 6. Reset Player Energy
```bash
npm run admin:reset-player-energy <gameworld-id>
```
Resets the energy of all players in a gameworld to 100. Useful for:
- Testing scenarios
- Fixing energy-related issues
- Preparing for special events

Shows current energy statistics before performing the reset.

#### 7. Clean Up Duplicate Fixtures
```bash
npm run admin:cleanup-fixtures [gameworld-id] [--dry-run]
```
Identifies and removes duplicate fixtures where teams have multiple fixtures scheduled at the same time. Features:
- Scans for fixtures where any team appears in multiple fixtures at the same date/time
- Groups duplicates and shows which fixtures conflict
- Keeps the most appropriate fixture (non-simulated over simulated, earliest created)
- Supports dry-run mode to preview changes without making them
- Can target a specific gameworld or scan all gameworlds
- Only processes unplayed fixtures for safety

**Options:**
- `gameworld-id` (optional): Limit cleanup to a specific gameworld
- `--dry-run`: Preview what would be deleted without making changes

**Examples:**
```bash
# Dry run for all gameworlds
npm run admin:cleanup-fixtures --dry-run

# Clean up specific gameworld
npm run admin:cleanup-fixtures 12345678-1234-1234-1234-123456789abc

# Dry run for specific gameworld
npm run admin:cleanup-fixtures 12345678-1234-1234-1234-123456789abc --dry-run
```

## Safety Features

### Input Validation
- All tools validate UUID formats for gameworld and manager IDs
- Database existence checks before performing operations
- Clear error messages for invalid inputs

### Confirmation Prompts
- Destructive operations require explicit confirmation
- Shows detailed information about what will be affected
- Requires typing "DELETE" for the most dangerous operations

### Transaction Safety
- All database operations use transactions
- Automatic rollback on errors
- Detailed logging of all operations

### Database Connection Management
- Proper connection initialization and cleanup
- Uses the same MikroORM configuration as the main application
- Handles connection errors gracefully

## Usage Examples

### Check what gameworlds exist
```bash
npm run admin:list-gameworlds
```

### Get detailed stats for a specific gameworld
```bash
npm run admin:gameworld-stats 12345678-1234-1234-1234-123456789abc
```

### See who's managing teams in a gameworld
```bash
npm run admin:list-managers 12345678-1234-1234-1234-123456789abc
```

### Remove a problematic manager
```bash
npm run admin:delete-manager ************************************
```

### Clean up an old/test gameworld
```bash
npm run admin:delete-gameworld 12345678-1234-1234-1234-123456789abc
```

### Fix player energy issues
```bash
npm run admin:reset-player-energy 12345678-1234-1234-1234-123456789abc
```

### Clean up duplicate fixtures
```bash
# Preview what would be cleaned up
npm run admin:cleanup-fixtures --dry-run

# Clean up duplicates in a specific gameworld
npm run admin:cleanup-fixtures 12345678-1234-1234-1234-123456789abc
```

## Best Practices

1. **Always backup your database** before running destructive operations
2. **Use the interactive menu** (`npm run admin:menu`) for better user experience
3. **Verify gameworld/manager IDs** using list commands before deletion
4. **Test on development environment** first when possible
5. **Monitor logs** during operations for any errors or warnings

## Troubleshooting

### Environment Variables Not Set
Ensure your `.env` file contains all required database connection variables.

### Permission Denied
Make sure your database user has sufficient permissions for all operations.

### Connection Timeout
Check your database connection settings and network connectivity.

### Transaction Rollback
If an operation fails, the transaction will be automatically rolled back. Check the logs for specific error details.

## Development

To add new admin tools:

1. Create a new TypeScript file in `scripts/admin-tools/`
2. Follow the existing pattern for database connection and error handling
3. Add the script to `package.json` with appropriate npm script
4. Update the `admin-menu.ts` file to include the new tool
5. Update this README with documentation

All admin tools should:
- Use proper TypeScript types
- Include comprehensive error handling
- Use transactions for database operations
- Provide clear user feedback
- Include input validation
- Follow the existing code style
