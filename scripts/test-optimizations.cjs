const fs = require('fs');
const path = require('path');

/**
 * Test script to validate lambda optimizations
 */

function testBundleSizes() {
  console.log('🔍 Testing bundle sizes...');
  
  const distDir = path.join(__dirname, '..', 'dist');
  const lambdaFunctions = [];
  
  // Find all lambda functions
  function findLambdas(dir, basePath = '') {
    const items = fs.readdirSync(dir);
    
    for (const item of items) {
      const fullPath = path.join(dir, item);
      const relativePath = path.join(basePath, item);
      
      if (fs.statSync(fullPath).isDirectory()) {
        // Check if this directory contains an index.js (lambda function)
        const indexPath = path.join(fullPath, 'index.js');
        if (fs.existsSync(indexPath)) {
          const size = fs.statSync(indexPath).size;
          lambdaFunctions.push({
            name: relativePath,
            size: size,
            sizeMB: Math.round(size / 1024 / 1024 * 100) / 100
          });
        } else {
          // Recurse into subdirectories
          findLambdas(fullPath, relativePath);
        }
      }
    }
  }
  
  findLambdas(distDir);
  
  // Sort by size
  lambdaFunctions.sort((a, b) => b.size - a.size);
  
  console.log(`\n📊 Found ${lambdaFunctions.length} lambda functions:`);
  console.log('Top 10 largest functions:');
  
  lambdaFunctions.slice(0, 10).forEach((lambda, index) => {
    const status = lambda.sizeMB < 1 ? '✅' : lambda.sizeMB < 2 ? '⚠️' : '❌';
    console.log(`${index + 1}. ${status} ${lambda.name}: ${lambda.sizeMB} MB`);
  });
  
  // Statistics
  const totalSize = lambdaFunctions.reduce((sum, lambda) => sum + lambda.size, 0);
  const averageSize = totalSize / lambdaFunctions.length;
  const optimizedCount = lambdaFunctions.filter(lambda => lambda.sizeMB < 1).length;
  
  console.log(`\n📈 Statistics:`);
  console.log(`Total functions: ${lambdaFunctions.length}`);
  console.log(`Average size: ${Math.round(averageSize / 1024 / 1024 * 100) / 100} MB`);
  console.log(`Functions under 1MB: ${optimizedCount}/${lambdaFunctions.length} (${Math.round(optimizedCount / lambdaFunctions.length * 100)}%)`);
  console.log(`Total deployment size: ${Math.round(totalSize / 1024 / 1024 * 100) / 100} MB`);
  
  return {
    totalFunctions: lambdaFunctions.length,
    averageSizeMB: Math.round(averageSize / 1024 / 1024 * 100) / 100,
    optimizedCount,
    optimizedPercentage: Math.round(optimizedCount / lambdaFunctions.length * 100)
  };
}

function testLayerExists() {
  console.log('\n🔍 Testing lambda layer...');
  
  const layerDir = path.join(__dirname, '..', 'layer');
  const nodeModulesDir = path.join(layerDir, 'nodejs', 'node_modules');
  
  if (!fs.existsSync(layerDir)) {
    console.log('❌ Layer directory not found');
    return false;
  }
  
  if (!fs.existsSync(nodeModulesDir)) {
    console.log('❌ Layer node_modules not found');
    return false;
  }
  
  // Check for key dependencies (excluding ES modules that were removed for compatibility)
  const keyDependencies = [
    '@mikro-orm/core',
    '@mikro-orm/postgresql',
    '@aws-sdk/client-dynamodb',
    'pg'
  ];
  
  const missingDeps = [];
  for (const dep of keyDependencies) {
    const depPath = path.join(nodeModulesDir, dep);
    if (!fs.existsSync(depPath)) {
      missingDeps.push(dep);
    }
  }
  
  if (missingDeps.length > 0) {
    console.log(`❌ Missing dependencies in layer: ${missingDeps.join(', ')}`);
    return false;
  }
  
  // Calculate layer size
  function getDirectorySize(dir) {
    let size = 0;
    const items = fs.readdirSync(dir);
    
    for (const item of items) {
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        size += getDirectorySize(fullPath);
      } else {
        size += stat.size;
      }
    }
    
    return size;
  }
  
  const layerSize = getDirectorySize(layerDir);
  const layerSizeMB = Math.round(layerSize / 1024 / 1024 * 100) / 100;
  
  console.log(`✅ Layer exists with all key dependencies`);
  console.log(`📦 Layer size: ${layerSizeMB} MB`);
  
  return true;
}

function testBuildConfiguration() {
  console.log('\n🔍 Testing build configuration...');
  
  const buildScript = path.join(__dirname, 'build.cjs');
  const buildLayerScript = path.join(__dirname, 'build-layer.cjs');
  
  if (!fs.existsSync(buildScript)) {
    console.log('❌ Build script not found');
    return false;
  }
  
  if (!fs.existsSync(buildLayerScript)) {
    console.log('❌ Build layer script not found');
    return false;
  }
  
  // Check if build script externalizes layer dependencies
  const buildContent = fs.readFileSync(buildScript, 'utf8');
  
  if (!buildContent.includes('layerDependencies')) {
    console.log('❌ Build script does not externalize layer dependencies');
    return false;
  }
  
  console.log('✅ Build configuration is correct');
  return true;
}

function main() {
  console.log('🚀 Lambda Optimization Validation\n');
  
  const bundleResults = testBundleSizes();
  const layerExists = testLayerExists();
  const buildConfigOk = testBuildConfiguration();
  
  console.log('\n📋 Summary:');
  console.log(`Bundle optimization: ${bundleResults.optimizedPercentage >= 80 ? '✅' : '❌'} ${bundleResults.optimizedPercentage}% of functions under 1MB`);
  console.log(`Layer creation: ${layerExists ? '✅' : '❌'}`);
  console.log(`Build configuration: ${buildConfigOk ? '✅' : '❌'}`);
  
  const allTestsPassed = bundleResults.optimizedPercentage >= 80 && layerExists && buildConfigOk;
  
  if (allTestsPassed) {
    console.log('\n🎉 All optimizations are working correctly!');
    console.log('\nNext steps:');
    console.log('1. Deploy the optimized lambdas: npm run deploy');
    console.log('2. Monitor cold start performance in CloudWatch');
    console.log('3. Test a few lambda functions to ensure functionality');
  } else {
    console.log('\n⚠️ Some optimizations need attention. Please review the issues above.');
  }
  
  process.exit(allTestsPassed ? 0 : 1);
}

if (require.main === module) {
  main();
}

module.exports = {
  testBundleSizes,
  testLayerExists,
  testBuildConfiguration
};
