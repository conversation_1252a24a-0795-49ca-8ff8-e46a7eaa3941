import { MikroORM } from '@mikro-orm/core';
import { PostgreSqlDriver } from '@mikro-orm/postgresql';
import config from '../mikro-orm.config.js';
import { logger } from '../src/utils/logger.js';

async function resetDatabase() {
  logger.info('Starting complete database reset process');

  // Initialize MikroORM
  logger.info('Connecting to database...');
  const orm = await MikroORM.init<PostgreSqlDriver>(config);
  const connection = orm.em.getConnection();

  try {
    // Disable foreign key checks temporarily
    logger.info('Disabling foreign key checks...');
    await connection.execute('SET session_replication_role = replica;');

    // Get all tables in the database
    logger.info('Getting all tables...');
    const result = await connection.execute(`
      SELECT tablename FROM pg_tables
      WHERE schemaname = 'public' AND
      tablename != 'mikro_orm_migrations';
    `);

    // Drop all tables
    logger.info('Dropping all tables...');
    for (const row of result) {
      const tableName = row.tablename;
      logger.info(`Dropping table ${tableName}...`);
      await connection.execute(`DROP TABLE IF EXISTS "${tableName}" CASCADE;`);
    }

    // Drop the migrations table
    logger.info('Dropping migrations table...');
    await connection.execute('DROP TABLE IF EXISTS "mikro_orm_migrations" CASCADE;');

    // Create a new migrations table
    logger.info('Creating new migrations table...');
    await connection.execute(`
      CREATE TABLE "mikro_orm_migrations" (
        "id" SERIAL PRIMARY KEY,
        "name" varchar(255) NOT NULL,
        "executed_at" timestamp NOT NULL DEFAULT now()
      );
    `);

    // Re-enable foreign key checks
    logger.info('Re-enabling foreign key checks...');
    await connection.execute('SET session_replication_role = DEFAULT;');

    logger.info('Database reset successfully!');
  } catch (error) {
    logger.error('Error resetting database:', { error });
    throw error;
  } finally {
    // Close the connection
    await orm.close(true);
  }
}

resetDatabase().catch(err => {
  logger.error('Failed to reset database:', err);
  process.exit(1);
});
