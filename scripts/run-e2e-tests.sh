#!/bin/bash

# Script to run end-to-end tests with PostgreSQL Docker container

set -e

echo "🚀 Starting E2E test environment..."

# Function to cleanup on exit
cleanup() {
    echo "🧹 Cleaning up..."
    docker-compose -f docker-compose.test.yml down -v
}

# Set trap to cleanup on script exit
trap cleanup EXIT

# Start PostgreSQL container
echo "📦 Starting PostgreSQL test container..."
docker-compose -f docker-compose.test.yml up -d postgres-test

# Wait for PostgreSQL to be ready
echo "⏳ Waiting for PostgreSQL to be ready..."
timeout=60
counter=0

while ! docker-compose -f docker-compose.test.yml exec -T postgres-test pg_isready -U postgres -d jfg_test > /dev/null 2>&1; do
    if [ $counter -ge $timeout ]; then
        echo "❌ PostgreSQL failed to start within $timeout seconds"
        exit 1
    fi
    echo "Waiting for PostgreSQL... ($counter/$timeout)"
    sleep 1
    counter=$((counter + 1))
done

echo "✅ PostgreSQL is ready!"

# Set environment variables for the test
export TEST_DATABASE_NAME=jfg_test
export TEST_DATABASE_HOST=localhost
export TEST_DATABASE_PORT=5433
export TEST_DATABASE_USER=postgres
export TEST_DATABASE_PASSWORD=postgres

# Run the E2E tests
echo "🧪 Running E2E tests..."
npm run test -- src/functions/fixtures/simulateFixtures.e2e.test.ts

echo "✅ E2E tests completed successfully!"
