const esbuild = require('esbuild');
const path = require('path');
const fs = require('fs');
const glob = require('glob');

const rootDir = path.join(__dirname, '..');

// Clean previous monolith build (now dist-monolith)
try {
  const oldDir = path.join(rootDir, 'dist-monolith');
  if (fs.existsSync(oldDir)) fs.rmSync(oldDir, { recursive: true, force: true });
} catch (e) {
  console.warn('Warning: could not clean previous monolith build', e);
}

// Collect all TS source files (exclude tests & declaration files)
const entryPoints = glob.sync('src/**/*.ts', {
  cwd: rootDir,
  nodir: true,
  ignore: ['**/*.d.ts', '**/*.test.ts', '**/*.spec.ts', '**/__tests__/**'],
});

if (entryPoints.length === 0) {
  console.error('No TypeScript files found to build.');
  process.exit(1);
}

function resolveAliasImport(currentFile, spec) {
  // spec starts with '@/'
  const relPart = spec.replace(/^@\//, '');
  const base = path.join(rootDir, 'src', relPart);
  const candidates = [];
  const ext = path.extname(base);
  if (ext) {
    // Always try the path as-is
    candidates.push(base);
    if (ext === '.js' || ext === '.mjs' || ext === '.cjs') {
      // Underlying TS source possibilities
      candidates.push(base.slice(0, -ext.length) + '.ts');
      candidates.push(base.slice(0, -ext.length) + '.tsx');
      // index form (e.g., dir/index.js coming from dir/index.ts)
      if (path.basename(base, ext) === 'index') {
        candidates.push(path.join(path.dirname(base), 'index.ts'));
      }
    } else if (ext === '.ts' || ext === '.tsx') {
      // Already explicit TS import (rare in NodeNext with alias) – ok
    }
  } else {
    // No extension supplied – common case
    candidates.push(base + '.ts');
    candidates.push(base + '.tsx');
    candidates.push(path.join(base, 'index.ts'));
    candidates.push(path.join(base, 'index.tsx'));
  }
  let target;
  for (const c of candidates) {
    if (fs.existsSync(c)) {
      target = c;
      break;
    }
  }
  if (!target) return spec; // leave unchanged (will very likely error later)
  let rel = path.relative(path.dirname(currentFile), target).replace(/\\/g, '/');
  if (!rel.startsWith('.')) rel = './' + rel;
  // Ensure .js extension in emitted import (pointing to transpiled output)
  rel = rel.replace(/\.(ts|tsx)$/, '.js');
  return rel;
}

const aliasRewritePlugin = {
  name: 'alias-rewrite',
  setup(build) {
    build.onLoad({ filter: /\.ts$/ }, async (args) => {
      let source = await fs.promises.readFile(args.path, 'utf8');
      // Patterns handle multi-line import/export clauses.
      const patterns = [
        /import\s+[\s\S]*?from\s+['"](@\/[^'";]+)['"];?/g, // import ... from '@/...'
        /import\s+['"](@\/[^'";]+)['"];?/g, // bare side-effect import
        /export\s+[\s\S]*?from\s+['"](@\/[^'";]+)['"];?/g, // export ... from '@/...'
        /export\s+\*\s+from\s+['"](@\/[^'";]+)['"];?/g, // export * from '@/...'
        /require\(\s*['"](@\/[^'";]+)['"]\s*\)/g, // require('@/...')
        /import\(\s*['"](@\/[^'";]+)['"]\s*\)/g, // dynamic import('@/...')
      ];
      for (const pattern of patterns) {
        source = source.replace(pattern, (match, p1) => {
          const rewritten = resolveAliasImport(args.path, p1);
          return match.replace(p1, rewritten);
        });
      }
      return { contents: source, loader: 'ts' };
    });
  },
};

// Perform unbundled transpilation preserving directory structure
esbuild
  .build({
    entryPoints,
    outbase: 'src',
    outdir: 'dist-monolith',
    platform: 'node',
    target: 'node20',
    format: 'esm', // switched to ESM to avoid interop issues with ESM-only deps like middy
    bundle: false,
    sourcemap: process.env.STAGE === 'dev',
    minify: false,
    treeShaking: false,
    absWorkingDir: rootDir,
    tsconfig: path.join(rootDir, 'tsconfig.json'),
    plugins: [aliasRewritePlugin],
    logLevel: 'info',
  })
  .then(() => {
    console.log('Monolith transpilation complete (unbundled, ESM)');

    // Generate dist-monolith/package.json with runtime deps (ESM)
    try {
      const rootPkg = JSON.parse(fs.readFileSync(path.join(rootDir, 'package.json'), 'utf8'));
      const runtimePkg = {
        name: 'monolith-runtime',
        private: true,
        version: rootPkg.version || '0.0.0',
        type: 'module', // ESM
        description: 'Runtime package for monolith server (generated)',
        license: rootPkg.license || undefined,
        scripts: {
          start: 'node monolith/server.js',
        },
        dependencies: rootPkg.dependencies || {},
      };
      const pkgPath = path.join(rootDir, 'dist-monolith', 'package.json');
      fs.writeFileSync(pkgPath, JSON.stringify(runtimePkg, null, 2));
      console.log('Created dist-monolith/package.json with runtime dependencies (ESM)');
    } catch (e) {
      console.warn('Could not write enhanced dist-monolith/package.json', e);
    }

    // Copy MJML templates to dist-monolith/templates
    const templatesSrc = path.join(rootDir, 'src', 'services', 'email', 'templates');
    const templatesDest = path.join(rootDir, 'dist-monolith', 'templates');
    try {
      if (fs.existsSync(templatesSrc)) {
        fs.mkdirSync(templatesDest, { recursive: true });
        for (const f of fs.readdirSync(templatesSrc)) {
          if (f.endsWith('.mjml')) {
            fs.copyFileSync(path.join(templatesSrc, f), path.join(templatesDest, f));
          }
        }
        console.log('Templates copied');
      }
    } catch (e) {
      console.warn('Template copy failed:', e);
    }

    // Copy commentary.csv for getLocalisation
    const commentarySrc = path.join(rootDir, 'src', 'simulation', 'commentary.csv');
    const commentaryDestDir = path.join(rootDir, 'dist-monolith', 'functions', 'fixtures');
    const commentaryDest = path.join(commentaryDestDir, 'commentary.csv');
    try {
      if (fs.existsSync(commentarySrc)) {
        fs.mkdirSync(commentaryDestDir, { recursive: true });
        fs.copyFileSync(commentarySrc, commentaryDest);
        console.log('Copied commentary.csv to dist-monolith/functions/fixtures/');
      } else {
        console.warn('commentary.csv not found at', commentarySrc);
      }
    } catch (e) {
      console.warn('Failed to copy commentary.csv:', e);
    }

    console.log('\nRun with:');
    console.log('  node dist-monolith/monolith/server.js');
  })
  .catch((err) => {
    console.error(err);
    process.exit(1);
  });
