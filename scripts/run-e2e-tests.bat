@echo off
setlocal enabledelayedexpansion

echo 🚀 Starting E2E test environment...

REM Start PostgreSQL container
echo 📦 Starting PostgreSQL test container...
docker-compose -f docker-compose.test.yml up -d postgres-test

REM Wait for PostgreSQL to be ready
echo ⏳ Waiting for PostgreSQL to be ready...
set timeout=60
set counter=0

:wait_loop
docker-compose -f docker-compose.test.yml exec -T postgres-test pg_isready -U postgres -d jfg_test >nul 2>&1
if %errorlevel% equ 0 goto postgres_ready

if !counter! geq %timeout% (
    echo ❌ PostgreSQL failed to start within %timeout% seconds
    goto cleanup
)

echo Waiting for PostgreSQL... (!counter!/%timeout%)
timeout /t 1 /nobreak >nul
set /a counter+=1
goto wait_loop

:postgres_ready
echo ✅ PostgreSQL is ready!

REM Set environment variables for the test
set TEST_DATABASE_NAME=jfg_test
set TEST_DATABASE_HOST=localhost
set TEST_DATABASE_PORT=5433
set TEST_DATABASE_USER=postgres
set TEST_DATABASE_PASSWORD=postgres

REM Run the E2E tests
echo 🧪 Running E2E tests...
npm run test -- src/functions/fixtures/simulateFixtures.e2e.test.ts

if %errorlevel% equ 0 (
    echo ✅ E2E tests completed successfully!
) else (
    echo ❌ E2E tests failed!
)

:cleanup
echo 🧹 Cleaning up...
docker-compose -f docker-compose.test.yml down -v

endlocal
