@echo off
REM Deploy monolith to EC2 instance (Windows helper)
REM Usage: deploy-monolith.bat [stage] [instance-ip]

setlocal enabledelayedexpansion

set STAGE=%1
set INSTANCE_IP=%2
if "%STAGE%"=="" set STAGE=stage

if "%INSTANCE_IP%"=="" (
    echo Usage: %0 [stage] ^<instance-ip^>
    echo Example: %0 stage ***********
    echo (INSTANCE_IP optional if you only want to build the archive)
)

REM Generate timestamp (locale independent)
for /f "delims=" %%i in ('powershell -NoLogo -NoProfile -Command "(Get-Date).ToString('yyyyMMdd-HHmmss')"') do set TS=%%i
set DEPLOY_DIR=deploy-%TS%

echo Deploying monolith build for %STAGE% (folder: %DEPLOY_DIR%)

REM Build (full + monolith bundle)
echo Building application...
call npm run build || goto :fail
call npm run monolith:build || goto :fail

echo Creating deployment directory %DEPLOY_DIR% ...
mkdir "%DEPLOY_DIR%" || goto :fail

REM Copy built output + manifests (exclude node_modules; install on remote)
xcopy /E /I /Y dist\* "%DEPLOY_DIR%\" >nul
copy package.json "%DEPLOY_DIR%\" >nul
if exist package-lock.json copy package-lock.json "%DEPLOY_DIR%\" >nul
if exist "%DEPLOY_DIR%\node_modules" rmdir /S /Q "%DEPLOY_DIR%\node_modules"

REM Create archive: prefer tar.gz, fallback to zip
set ARCHIVE=%DEPLOY_DIR%.tar.gz
where tar >nul 2>&1
if %errorlevel%==0 (
    echo Creating tar.gz archive %ARCHIVE% ...
    tar -czf "%ARCHIVE%" "%DEPLOY_DIR%" || goto :fail
) else (
    set ARCHIVE=%DEPLOY_DIR%.zip
    echo tar not found, creating zip archive %ARCHIVE% ...
    powershell -NoLogo -NoProfile -Command "Compress-Archive -Path '%DEPLOY_DIR%/*' -DestinationPath '%ARCHIVE%' -Force" || goto :fail
)

echo.
echo Archive created: %ARCHIVE%

if "%INSTANCE_IP%"=="" goto :manual

REM If instance IP provided, attempt automated upload (requires ssh / scp available)
where scp >nul 2>&1
if not %errorlevel%==0 (
    echo scp not found in PATH; skipping automatic upload.
    goto :manual
)

set KEY_FILE=%USERPROFILE%\.ssh\%STAGE%-monolith-key.pem
if not exist "%KEY_FILE%" (
    echo SSH key not found at %KEY_FILE% - skipping automatic upload.
    goto :manual
)

echo Uploading %ARCHIVE% to ec2-user@%INSTANCE_IP%:/tmp/ ...
scp -i "%KEY_FILE%" -o StrictHostKeyChecking=no "%ARCHIVE%" ec2-user@%INSTANCE_IP%:/tmp/ || goto :manual

echo Running remote deployment steps...
if /I "%ARCHIVE:~-4%"==".zip" (
  echo NOTE: Remote script expects tar.gz; manual steps required for .zip.
  goto :manual
)

REM Build remote script in a temp file
set TEMP_SCRIPT=%TEMP%\monolith_remote_%TS%.sh
>"%TEMP_SCRIPT%" echo set -euo pipefail
>>"%TEMP_SCRIPT%" echo echo '--- Disk usage before deployment ---'
>>"%TEMP_SCRIPT%" echo df -h /
>>"%TEMP_SCRIPT%" echo df -h /tmp ^|^| true
>>"%TEMP_SCRIPT%" echo sudo systemctl stop monolith ^|^| true
>>"%TEMP_SCRIPT%" echo '[ -d /opt/monolith ] && sudo mv /opt/monolith /opt/monolith.backup.$(date +%%Y%%m%%d-%%H%%M%%S) || true'
>>"%TEMP_SCRIPT%" echo cd /tmp
>>"%TEMP_SCRIPT%" echo tar -xzf %ARCHIVE%
>>"%TEMP_SCRIPT%" echo rm -f %ARCHIVE%
>>"%TEMP_SCRIPT%" echo sudo mv %DEPLOY_DIR% /opt/monolith
>>"%TEMP_SCRIPT%" echo sudo chown -R ec2-user:ec2-user /opt/monolith
>>"%TEMP_SCRIPT%" echo cd /opt/monolith
>>"%TEMP_SCRIPT%" echo npm ci --production --silent
>>"%TEMP_SCRIPT%" echo 'sudo tee /opt/monolith/.env > /dev/null <<EOL'
>>"%TEMP_SCRIPT%" echo NODE_ENV=production
>>"%TEMP_SCRIPT%" echo PORT=3000
>>"%TEMP_SCRIPT%" echo STAGE=%STAGE%
>>"%TEMP_SCRIPT%" echo '# DATABASE_URL='
>>"%TEMP_SCRIPT%" echo '# COGNITO_USER_POOL_ID='
>>"%TEMP_SCRIPT%" echo '# COGNITO_USER_POOL_CLIENT_ID='
>>"%TEMP_SCRIPT%" echo EOL
>>"%TEMP_SCRIPT%" echo 'sudo tee /etc/systemd/system/monolith.service > /dev/null <<EOL'
>>"%TEMP_SCRIPT%" echo [Unit]
>>"%TEMP_SCRIPT%" echo Description=Jumpers for Goalposts Monolith API
>>"%TEMP_SCRIPT%" echo After=network.target
>>"%TEMP_SCRIPT%" echo
>>"%TEMP_SCRIPT%" echo [Service]
>>"%TEMP_SCRIPT%" echo Type=simple
>>"%TEMP_SCRIPT%" echo User=ec2-user
>>"%TEMP_SCRIPT%" echo WorkingDirectory=/opt/monolith
>>"%TEMP_SCRIPT%" echo ExecStart=/usr/bin/node monolith/server.cjs
>>"%TEMP_SCRIPT%" echo Restart=always
>>"%TEMP_SCRIPT%" echo RestartSec=10
>>"%TEMP_SCRIPT%" echo EnvironmentFile=/opt/monolith/.env
>>"%TEMP_SCRIPT%" echo StandardOutput=append:/var/log/monolith.log
>>"%TEMP_SCRIPT%" echo StandardError=append:/var/log/monolith.log
>>"%TEMP_SCRIPT%" echo
>>"%TEMP_SCRIPT%" echo [Install]
>>"%TEMP_SCRIPT%" echo WantedBy=multi-user.target
>>"%TEMP_SCRIPT%" echo EOL
>>"%TEMP_SCRIPT%" echo sudo systemctl daemon-reload
>>"%TEMP_SCRIPT%" echo sudo systemctl enable monolith
>>"%TEMP_SCRIPT%" echo sudo systemctl start monolith
>>"%TEMP_SCRIPT%" echo sleep 8
>>"%TEMP_SCRIPT%" echo 'sudo systemctl status monolith --no-pager || (sudo journalctl -u monolith --no-pager -n 80; exit 1)'
>>"%TEMP_SCRIPT%" echo echo 'Remote deployment completed.'

REM Execute remote script
type "%TEMP_SCRIPT%" | ssh -i "%KEY_FILE%" -o StrictHostKeyChecking=no ec2-user@%INSTANCE_IP% "bash -s" || goto :manual

del "%TEMP_SCRIPT%" >nul 2>&1

echo Remote deployment finished.

goto :cleanup

:manual
echo.
echo Manual deployment steps (tar.gz expected on remote):
echo   scp %ARCHIVE% ec2-user@%INSTANCE_IP%:/tmp/
echo   ssh ec2-user@%INSTANCE_IP%
echo   sudo systemctl stop monolith ^|^| true
echo   cd /tmp
if not "%ARCHIVE:~-4%"==".zip" (
  echo   tar -xzf %ARCHIVE%
) else (
  echo   unzip %ARCHIVE%
)
echo   sudo mv /opt/monolith /opt/monolith.backup.$(date +%%Y%%m%%d-%%H%%M%%S) 2^> /dev/null
if not "%ARCHIVE:~-4%"==".zip" (
  echo   sudo mv %DEPLOY_DIR% /opt/monolith
) else (
  echo   sudo mkdir -p /opt/monolith ^&^& sudo cp -R * /opt/monolith
)
echo   cd /opt/monolith
echo   npm ci --production --silent
echo   sudo tee /opt/monolith/.env ^> /dev/null <<EOL
echo   NODE_ENV=production
echo   PORT=3000
echo   STAGE=%STAGE%
echo   # DATABASE_URL=
echo   # COGNITO_USER_POOL_ID=
echo   # COGNITO_USER_POOL_CLIENT_ID=
echo   EOL
echo   sudo tee /etc/systemd/system/monolith.service ^> /dev/null <<EOL
echo   [Unit]
echo   Description=Jumpers for Goalposts Monolith API
echo   After=network.target


echo   [Service]
echo   Type=simple
echo   User=ec2-user
echo   WorkingDirectory=/opt/monolith
echo   ExecStart=/usr/bin/node monolith/server.cjs
echo   Restart=always
echo   RestartSec=10
echo   EnvironmentFile=/opt/monolith/.env

echo   [Install]
echo   WantedBy=multi-user.target

echo   EOL
echo   sudo systemctl daemon-reload
echo   sudo systemctl enable monolith
echo   sudo systemctl start monolith

echo.

:cleanup
REM (Optionally keep the directory; remove to save space)
REM rmdir /S /Q "%DEPLOY_DIR%"

echo Done. Archive: %ARCHIVE%
exit /b 0

:fail
echo Deployment packaging failed with error code %errorlevel%.
exit /b %errorlevel%
