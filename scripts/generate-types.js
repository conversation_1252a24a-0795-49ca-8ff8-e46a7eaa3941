import * as fs from 'fs/promises';
import { compile } from 'json-schema-to-typescript';
import * as path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function generateTypes() {
  const schemasDir = path.join(__dirname, '../src/schemas');
  const typesDir = path.join(__dirname, '../src/types/generated');

  // Ensure types directory exists
  await fs.mkdir(typesDir, { recursive: true });

  // Read all schema files
  const schemaFiles = await fs.readdir(schemasDir);

  for (const file of schemaFiles) {
    if (!file.endsWith('.json')) continue;

    const schemaPath = path.join(schemasDir, file);
    const schema = JSON.parse(await fs.readFile(schemaPath, 'utf8'));

    // Convert schema name to type name (e.g., team-created-event.json -> TeamCreatedEvent)
    const typeName = file
      .replace('.json', '')
      .split('-')
      .map((part) => part.charAt(0).toUpperCase() + part.slice(1))
      .join('');

    const ts = await compile(schema, typeName, {
      bannerComment:
        '/* eslint-disable */\n/** This file was automatically generated by json-schema-to-typescript */',
      style: {
        singleQuote: true,
      },
    });

    const outputPath = path.join(typesDir, `${file.replace('.json', '.ts')}`);
    await fs.writeFile(outputPath, ts);
    console.log(`Generated types for ${file}`);
  }
}

generateTypes().catch(console.error);
