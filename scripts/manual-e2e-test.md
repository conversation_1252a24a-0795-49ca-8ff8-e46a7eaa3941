# Manual E2E Test Execution Guide

Since the automated scripts have some TypeScript path resolution issues, here's a step-by-step guide to manually run the E2E tests for critical lambda functions.

## Step 1: Start Test Database

```bash
# Start PostgreSQL test container
docker-compose -f docker-compose.test.yml up -d postgres-test

# Wait for it to be ready (should return "postgres-test:5432 - accepting connections")
docker-compose -f docker-compose.test.yml exec postgres-test pg_isready -U postgres -d jfg_test
```

## Step 2: Set Environment Variables

### For Windows (PowerShell):
```powershell
$env:TEST_DATABASE_NAME="jfg_test"
$env:TEST_DATABASE_HOST="localhost"
$env:TEST_DATABASE_PORT="5433"
$env:TEST_DATABASE_USER="postgres"
$env:TEST_DATABASE_PASSWORD="postgres"
```

### For Unix/Linux/macOS:
```bash
export TEST_DATABASE_NAME=jfg_test
export TEST_DATABASE_HOST=localhost
export TEST_DATABASE_PORT=5433
export TEST_DATABASE_USER=postgres
export TEST_DATABASE_PASSWORD=postgres
```

## Step 3: Run the Tests

### Run All E2E Tests
```bash
# Run all E2E tests
npm run test -- "**/*.e2e.test.ts" --run

# Or with more verbose output:
npx vitest run "**/*.e2e.test.ts" --reporter=verbose
```

### Run Specific E2E Tests
```bash
# Run the comprehensive end-of-season flow test (RECOMMENDED)
npm run test -- src/functions/gameworld/endOfSeasonFlow.e2e.test.ts --run

# Run only simulateFixtures E2E test
npm run test -- src/functions/fixtures/simulateFixtures.e2e.test.ts --run

# Run only processEndOfSeason E2E test
npm run test -- src/functions/league/processEndOfSeason.e2e.test.ts --run

# Run only processFixtureGenerationQueue E2E test
npm run test -- src/functions/generate/processFixtureGenerationQueue.e2e.test.ts --run
```

## Step 4: Interpret Results

### If the tests pass ✅
- **endOfSeasonFlow**: The complete end-of-season workflow is working correctly (MOST IMPORTANT)
- **simulateFixtures**: All database updates are happening correctly (manager stats, team standings, player data)
- **processEndOfSeason**: Player aging, retirement, youth generation, fixture generation requests, and cleanup are working properly
- **processFixtureGenerationQueue**: Fixture generation, gameworld updates, and cleanup are working correctly

### If the tests fail ❌
Look for these common issues:

#### endOfSeasonFlow Issues:
1. **Duplicate youth player generation**:
   ```
   🚨 DUPLICATE YOUTH PLAYER GENERATION DETECTED:
   - processEndOfSeason generated: 4 requests
   - processLeagueMovement generated: 4 requests
   - Total: 8 requests (should be 4)
   ```
   This confirms the duplicate issue you mentioned.

2. **Missing SQS calls**:
   ```
   Expected SQS calls: 2 (end-of-season + league-movement)
   Received: 1
   ```
   This indicates one of the initial triggers isn't working.

3. **Flow interruption**:
   ```
   Step 3: Process League Movement - FAILED
   ```
   This indicates where in the flow the issue occurred.

#### simulateFixtures Issues:
1. **Manager stats not updating**:
   ```
   Expected: 11 (initial: 10, +1 for win)
   Received: 10 (no change)
   ```
   This indicates the `updateManagerStats` method isn't working.

2. **Team standings not updating**:
   ```
   Expected: 6 (initial: 5, +1 for match played)
   Received: 5 (no change)
   ```
   This indicates the `updateTeamStandings` method isn't working.

#### processEndOfSeason Issues:
1. **Player aging not working**:
   ```
   Expected: 26 (initial: 25, +1 for aging)
   Received: 25 (no change)
   ```
   This indicates the bulk player aging isn't working.

2. **Youth players not requested**:
   ```
   Expected SQS calls: 1
   Received: 0
   ```
   This indicates the youth player generation SQS messages aren't being sent.

3. **Fixture cleanup not happening**:
   ```
   Expected DynamoDB calls: 1
   Received: 0
   ```
   This indicates old fixture details aren't being cleaned up.

#### processFixtureGenerationQueue Issues:
1. **Fixtures not being created**:
   ```
   Expected fixture count: >0
   Received: 0
   ```
   This indicates the fixture generation logic isn't working.

2. **Gameworld end date not updated**:
   ```
   Expected end date: >1234567890
   Received: 1234567890 (no change)
   ```
   This indicates the season end date calculation isn't working.

3. **Insufficient teams handling**:
   ```
   Expected batch failures: 1
   Received: 0
   ```
   This indicates error handling for insufficient teams isn't working.

4. **Transaction rollback**:
   ```
   Error: Transaction was rolled back
   ```
   This indicates an error occurred during the database transaction.

## Step 5: Debug Issues

If you find issues, add debugging to the test:

```typescript
// Add this after the simulation runs in the test
console.log('=== DEBUG INFO ===');
const debugFixture = await orm.em.findOne(Fixture, { fixtureId: fixture.fixtureId });
console.log('Fixture played:', debugFixture?.played);
console.log('Fixture score:', debugFixture?.score);

const debugHomeManager = await orm.em.findOne(Manager, { managerId: homeManager.managerId });
console.log('Home manager wins:', debugHomeManager?.wins);
console.log('Home manager goals scored:', debugHomeManager?.goalsScored);
```

## Step 6: Cleanup

```bash
# Stop and remove the test database
docker-compose -f docker-compose.test.yml down -v
```

## Common Issues and Solutions

### "Cannot connect to database"
- Make sure Docker is running
- Check if port 5433 is available: `netstat -an | grep 5433`
- Verify the container is running: `docker ps`

### "Module not found" errors
- The test file has TypeScript path resolution issues
- Try running from the project root directory
- Ensure all dependencies are installed: `npm install`

### Test timeout
- The test might be taking longer than expected
- Increase the timeout in vitest.config.ts
- Check if the database is responding slowly

### "Manager stats not updating" (Your specific issue)
This is exactly what the test is designed to catch! If this fails, it means:
- The `updateManagerStats` method in `FixtureDatabaseService` isn't being called
- The method is being called but not persisting changes
- There's a transaction issue preventing the updates

Check the `updateManagerStats` method in `src/services/fixtures/fixture-database-service.ts` around line 486.
