# Systemd Service Configuration ✅

## Issue Fixed

**Problem**: Two different systemd service configurations were being created:
1. **Infrastructure** (`infrastructure/ec2.ts`) - Created during EC2 initialization
2. **GitHub Actions** (`.github/workflows/deploy-stage.yml`) - Recreated during deployment

**Solution**: Use single source of truth from infrastructure, deployment only updates environment variables.

## Current Configuration

### Single Systemd Service Definition
The systemd service is created **once** during EC2 initialization with full log rotation support:

```ini
[Unit]
Description=Jumpers for Goalposts Monolith API
After=network.target

[Service]
Type=simple
User=ec2-user
WorkingDirectory=/opt/monolith
ExecStart=/usr/bin/node monolith/server.js
ExecReload=/bin/kill -USR1 $MAINPID    # ← Log rotation support
Restart=always
RestartSec=10
Environment=NODE_ENV=production
Environment=PORT=3000
EnvironmentFile=/opt/monolith/.env     # ← Deployment secrets
StandardOutput=append:/var/log/monolith.log
StandardError=append:/var/log/monolith.log

[Install]
WantedBy=multi-user.target
```

### Key Features

#### **Log Rotation Support**
- `ExecReload=/bin/kill -USR1 $MAINPID` - Allows log rotation without service restart
- `StandardOutput/StandardError` - Logs to `/var/log/monolith.log`
- Works with logrotate configuration

#### **Environment Management**
- `Environment=NODE_ENV=production` - Base environment
- `Environment=PORT=3000` - Application port
- `EnvironmentFile=/opt/monolith/.env` - Deployment-specific secrets

#### **Process Management**
- `User=ec2-user` - Runs as non-root user
- `Restart=always` - Auto-restart on failure
- `RestartSec=10` - Wait 10 seconds before restart

## Deployment Process

### Infrastructure Phase (Once)
```bash
# During EC2 initialization (infrastructure/ec2.ts)
1. Creates systemd service file
2. Sets up log rotation configuration
3. Installs monitoring scripts
4. Enables service for auto-start
```

### Deployment Phase (Each Deploy)
```bash
# During GitHub Actions deployment
1. Stops service
2. Updates application files
3. Updates .env file with secrets
4. Starts service (uses existing systemd config)
```

## Environment Variables

### Infrastructure-Set (Fixed)
```bash
NODE_ENV=production
PORT=3000
```

### Deployment-Set (From GitHub Secrets)
```bash
# Updated during each deployment in /opt/monolith/.env
STAGE=stage
DATABASE_URL=postgresql://...
DATABASE_HOST=...
DATABASE_NAME=...
DATABASE_USER=...
DATABASE_PASSWORD=...
DATABASE_PORT=5432
COGNITO_USER_POOL_ID=...
COGNITO_USER_POOL_CLIENT_ID=...
```

## Service Management Commands

### Basic Operations
```bash
# Check service status
sudo systemctl status monolith

# Start/stop/restart service
sudo systemctl start monolith
sudo systemctl stop monolith
sudo systemctl restart monolith

# Reload service (for log rotation)
sudo systemctl reload monolith

# Enable/disable auto-start
sudo systemctl enable monolith
sudo systemctl disable monolith
```

### Log Management
```bash
# View service logs (systemd journal)
sudo journalctl -u monolith -f

# View application logs (direct file)
sudo tail -f /var/log/monolith.log

# Force log rotation
sudo logrotate -f /etc/logrotate.d/monolith
```

### Troubleshooting
```bash
# Check service configuration
sudo systemctl cat monolith

# Check environment variables
sudo systemctl show monolith --property=Environment

# Test service file syntax
sudo systemd-analyze verify /etc/systemd/system/monolith.service

# Reload systemd after manual changes
sudo systemctl daemon-reload
```

## Configuration Files

### Service File Location
```bash
/etc/systemd/system/monolith.service
```

### Environment File Location
```bash
/opt/monolith/.env
```

### Log Configuration
```bash
/etc/logrotate.d/monolith
```

### Monitoring Script
```bash
/usr/local/bin/check-log-size.sh
```

## Benefits of Single Configuration

### ✅ **Consistency**
- One source of truth for service configuration
- No conflicts between infrastructure and deployment
- Predictable behavior across deployments

### ✅ **Log Rotation Support**
- `ExecReload` command enables graceful log rotation
- No service downtime during log rotation
- Proper integration with logrotate

### ✅ **Simplified Deployment**
- Deployment only updates application code and secrets
- No risk of breaking systemd configuration
- Faster deployment (no service file recreation)

### ✅ **Easier Maintenance**
- Service configuration changes go through infrastructure code
- Version controlled and reviewable
- Consistent across environments

## Deployment Verification

After deployment, verify the service is working correctly:

```bash
# Check service is running
sudo systemctl is-active monolith

# Check recent logs
sudo journalctl -u monolith --since "5 minutes ago"

# Test application
curl http://localhost:3000/ping

# Check environment variables are loaded
sudo systemctl show monolith --property=Environment
```

## Future Changes

To modify the systemd service configuration:

1. **Update** `infrastructure/ec2.ts`
2. **Deploy infrastructure** with `pulumi up`
3. **Restart service** to pick up changes

**Do NOT** modify the service file directly on the server or in GitHub Actions - use infrastructure as code! 🚀
