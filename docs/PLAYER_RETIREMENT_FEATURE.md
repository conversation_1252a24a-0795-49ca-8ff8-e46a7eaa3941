# Player Retirement Feature Implementation

## Overview
This feature implements player retirement functionality where players have a chance to announce their retirement based on their age. The retirement system follows these rules:

- **Age 37**: 25% chance of retirement
- **Age 38**: 50% chance of retirement
- **Age 39**: 100% chance of retirement

## Implementation Details

### 1. Database Changes
- Added `retiringAtEndOfSeason` boolean field to the `Player` entity
- This field indicates when a player has announced their retirement

### 2. Repository Updates
- Added `getPlayersByGameworld()` method to retrieve all players in a gameworld
- Added `removePlayer()` method to delete retired players from the database
- Updated mock repositories for testing

### 3. Core Logic (`src/functions/league/logic/PlayerRetirement.ts`)
- **`shouldPlayerRetire(age: number)`**: Determines retirement probability based on age
- **`processPlayerAging(gameworldId, repositories)`**: Efficient bulk processing of player aging and retirement
- **`sendRetirementNotifications(retiringPlayers, repositories)`**: Sends notifications grouped by team for efficiency
- **Bulk SQL Operations**: Uses MikroORM's `nativeUpdate` and `nativeDelete` for performance

### 4. Two-Season Retirement Process
**Season 1**: Players are marked for retirement (`retiringAtEndOfSeason = true`)
**Season 2**: Previously marked players are removed from the game

This ensures players complete their final season before retirement.

### 5. End of Season Integration
Updated `processEndOfSeason.ts` to include retirement processing:
1. **Bulk age increment**: All players age +1 using efficient SQL
2. **Remove previously retired players**: Players marked last season are deleted
3. **Mark new retiring players**: Process retirement decisions for eligible players
4. **Send notifications**: Grouped by team to minimize database calls
5. Process league movements and other end-of-season logic

### 6. Performance Optimizations
- **Bulk SQL operations** instead of individual player updates
- **Team-grouped notifications** to reduce database calls
- **Efficient queries** for eligible players using age filters

### 7. Notification System
- Added `sendPlayerRetirementNotification()` method to `NotificationManager`
- Sends notifications via email, push notifications, and inbox messages
- Notifications include player name, age, and retirement message

### 6. UI Integration
- The `PlayerResponse` type automatically includes the `retiringAtEndOfSeason` field
- UI can display retirement status for players without additional API changes
- Retirement status is available in team player lists and other player endpoints

## Testing
Comprehensive test coverage includes:
- **Unit tests** for retirement probability logic
- **Integration tests** for player aging and retirement processing
- **Notification tests** for retirement announcements
- **End-of-season tests** for complete workflow

## API Impact
- **No breaking changes** to existing APIs
- Player responses now include `retiringAtEndOfSeason` boolean field
- Retirement notifications appear in team inbox messages

## Workflow
1. **During Season**: Players continue playing normally
2. **End of Season**:
   - All players age by 1 year
   - Retirement probability calculated for eligible players
   - Retirement notifications sent to team managers
   - League movements and other processing occurs
   - Retired players removed from the game

## Task Reference
- **Task ID**: TJFG-026
- **Monday.com URL**: https://realworld666s-team.monday.com/item/TJFG-026

## Files Modified
- `src/entities/Player.ts` - Added retirement field
- `src/storage-interface/players/player-repository.interface.ts` - Added new methods
- `src/storage-interface/players/mikro-orm-player-repository.ts` - Implemented new methods
- `src/testing/mockRepositories.ts` - Updated mocks
- `src/functions/league/logic/PlayerRetirement.ts` - New retirement logic
- `src/services/notifications/NotificationManager.ts` - Added retirement notification
- `src/functions/league/processEndOfSeason.ts` - Integrated retirement processing
- `src/functions/league/logic/PlayerRetirement.test.ts` - New comprehensive tests
- `src/functions/league/processEndOfSeason.test.ts` - Updated existing tests

## Future Enhancements
- Could add retirement ceremonies or special events
- Could implement early retirement for injured players
- Could add retirement benefits or pension systems
- Could track retirement statistics and hall of fame
