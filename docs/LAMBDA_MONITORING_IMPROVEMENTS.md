# Lambda Monitoring Improvements

## Summary of Changes

This document outlines the improvements made to Lambda function monitoring in the infrastructure.

## 1. AWS Lambda Insights Enabled

### Changes Made:
- **Added Lambda Insights layer** to `getCommonLambdaSettings()` in `infrastructure/config.ts`
  - Layer ARN: `arn:aws:lambda:us-east-2:580247275435:layer:LambdaInsightsExtension:53`
  - This layer provides enhanced monitoring metrics for all Lambda functions

- **Added CloudWatch Lambda Insights permissions** to `createBasicLambdaRole()` in `infrastructure/config.ts`
  - Policy: `arn:aws:iam::aws:policy/CloudWatchLambdaInsightsExecutionRolePolicy`

### Benefits:
- Enhanced performance monitoring for all Lambda functions
- Memory utilization metrics
- Cold start detection
- Detailed execution environment metrics
- Integration with CloudWatch dashboards

## 2. X-Ray Permissions Fixed

### Changes Made:
- **Added X-Ray permissions** to `createBasicLambdaRole()` in `infrastructure/config.ts`
  - Policy: `AWSXRayDaemonWriteAccess`

### Benefits:
- Proper X-Ray tracing permissions for all Lambda functions
- Distributed tracing across the application
- Service map visualization
- Performance bottleneck identification

## 3. Global Lambda Timeout Alarm

### Changes Made:
- **Created `createGlobalLambdaTimeoutAlarm()`** function in `infrastructure/cloudwatch.ts`
  - Monitors the `AWS/Lambda Duration` metric across all functions
  - Threshold: 25 seconds (close to the 30-second default timeout)
  - Period: 5 minutes
  - Statistic: Maximum

- **Added global timeout alarm** in `infrastructure/index.ts`
  - Creates a single alarm for all Lambda functions
  - Sends notifications to a dedicated SNS topic
  - Subscribes to email notifications if `alarmEmail` is configured

### Benefits:
- Single alarm monitors timeouts across all Lambda functions
- Avoids creating individual timeout alarms for each function
- Centralized timeout monitoring and alerting

## 4. Existing Error Log Alarms Preserved

### What Was NOT Changed:
- **Existing metric filter-based error alarms** remain unchanged
- Individual error log alarms for SQS-triggered Lambda functions
- Custom error log patterns: `{ ($.level = "ERROR") || ("Status: timeout") }`

### Benefits:
- Maintains granular error monitoring per function
- Preserves existing alerting patterns
- Continues to catch application-level errors

## 5. CORS Handler Updated

### Changes Made:
- **Updated CORS handler** in `infrastructure/apiGateway.ts` to use `getCommonLambdaSettings()`
- Ensures the CORS handler also gets Lambda Insights and X-Ray permissions

## Implementation Details

### IAM Policy Optimization
To avoid hitting AWS's 10 managed policies per role limit, the implementation was updated to use inline policies instead of managed policy attachments for:
- X-Ray permissions (moved to inline policy)
- Lambda Insights permissions (moved to inline policy)
- DynamoDB access policies (converted to inline policies)
- SQS queue policies (converted to inline policies)
- Lambda invoke policies (converted to inline policies)
- SNS topic policies (converted to inline policies)

Only the `AWSLambdaBasicExecutionRole` remains as a managed policy attachment.

### Lambda Insights Layer Version
The Lambda Insights layer version (53) is specific to the `us-east-2` region and Node.js 20.x runtime. This should be updated if:
- The region changes
- The Node.js runtime version changes
- AWS releases newer layer versions

### Global Timeout Alarm Logic
The global timeout alarm uses the `AWS/Lambda Duration` metric which:
- Measures the elapsed wall clock time from when the function code starts executing
- Is available for all Lambda functions automatically
- Triggers when any function approaches its timeout limit

### X-Ray Integration
With proper permissions now in place, X-Ray tracing will work correctly with:
- Lambda function execution traces
- API Gateway integration traces
- DynamoDB operation traces
- SQS message processing traces

## Monitoring Dashboard Recommendations

With Lambda Insights enabled, you can now create CloudWatch dashboards that include:
- Memory utilization across all functions
- Cold start frequency and duration
- Function duration trends
- Error rates and timeout occurrences
- Cost optimization insights

## Next Steps

1. **Deploy the changes** to see Lambda Insights data in CloudWatch
2. **Create CloudWatch dashboards** using the new Lambda Insights metrics
3. **Monitor the global timeout alarm** to identify functions that need timeout adjustments
4. **Review X-Ray service maps** to optimize application performance
5. **Consider setting up automated responses** to timeout alarms (e.g., auto-scaling, notifications)

## Cost Considerations

- **Lambda Insights**: Small additional cost per function invocation
- **X-Ray**: Pay-per-trace pricing
- **CloudWatch Alarms**: Minimal cost for the global timeout alarm

The monitoring improvements provide significant operational benefits that typically outweigh the small additional costs.
