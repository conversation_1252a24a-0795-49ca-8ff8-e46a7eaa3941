# Guest Users and Data Migration

This document describes the guest user functionality and data migration system implemented for the Jumpers for Goalposts backend.

## Overview

The system allows users to:
1. Play as guest users without creating an account
2. Sign up or log in later and migrate their guest data
3. Resolve conflicts when both guest and authenticated accounts have data

## Architecture

### Cognito Setup

- **User Pool**: Handles authenticated users with email/password and Google OAuth
- **Identity Pool**: Provides temporary credentials for guest users
- **IAM Roles**: Separate roles for authenticated and unauthenticated (guest) users

### Database Schema

New fields added to the `Manager` entity:
- `isGuest`: Boolean flag indicating if this is a guest account
- `guestDeviceId`: Device identifier for guest users
- `migratedFromGuestId`: ID of the guest account that was migrated (for audit trail)
- `migrationCompleted`: Flag indicating if migration has been completed

## API Endpoints

### 1. Create Guest User
```
POST /auth/guest
```

**Request Body:**
```json
{
  "deviceId": "unique-device-identifier"
}
```

**Response:**
```json
{
  "guestUserId": "uuid",
  "identityId": "cognito-identity-id",
  "team": {
    "teamId": "uuid",
    "teamName": "Team Name",
    "gameworldId": "uuid"
  }
}
```

**Behavior:**
- If a guest user already exists for the device ID, returns existing user
- If no guest user exists, creates a new one and assigns an available team
- Returns 503 if no teams are available

### 2. Check Migration Conflicts
```
GET /auth/migration/conflicts?guestUserId=uuid
Authorization: Bearer <jwt-token>
```

**Response:**
```json
{
  "hasConflicts": true,
  "guestData": {
    "teamId": "uuid",
    "teamName": "Guest Team",
    "balance": 300000,
    "played": 5,
    "points": 12,
    "managerStats": {
      "scoutTokens": 3,
      "wins": 4,
      "defeats": 1,
      "trophies": 0
    }
  },
  "authenticatedData": {
    "teamId": "uuid",
    "teamName": "Main Team",
    "balance": 250000,
    "played": 8,
    "points": 18,
    "managerStats": {
      "scoutTokens": 2,
      "wins": 6,
      "defeats": 2,
      "trophies": 1
    }
  }
}
```

**Behavior:**
- Returns `hasConflicts: false` if authenticated user has no team
- Returns detailed comparison if both accounts have teams

### 3. Resolve Migration
```
POST /auth/migration/resolve
Authorization: Bearer <jwt-token>
```

**Request Body:**
```json
{
  "guestUserId": "uuid",
  "keepGuestData": true
}
```

**Response:**
```json
{
  "success": true,
  "keptTeamId": "uuid",
  "message": "Guest team data has been migrated to your account"
}
```

**Behavior:**
- If `keepGuestData: true`: Migrates guest team to authenticated account, merges stats
- If `keepGuestData: false`: Keeps authenticated account data, releases guest team
- Deletes guest account after successful migration

## Data Migration Logic

### When keepGuestData = true:
1. Release authenticated user's current team (if any) back to available teams
2. Transfer guest team to authenticated user
3. Merge manager stats (take maximum values for resources)
4. Add cumulative stats (wins, defeats, trophies)
5. Mark migration as completed
6. Delete guest account

### When keepGuestData = false:
1. Release guest team back to available teams
2. Keep authenticated user's existing team and stats
3. Mark migration as completed
4. Delete guest account

### Stat Merging Rules:
- **Resources** (tokens, sponges, appeals): Take maximum value
- **Cumulative stats** (wins, defeats, trophies): Add together
- **Streaks** (login streak): Take maximum value

## Client Integration with AWS Amplify v6

### Amplify Configuration
```typescript
import { Amplify } from 'aws-amplify';

Amplify.configure({
  Auth: {
    Cognito: {
      userPoolId: 'your-user-pool-id',
      userPoolClientId: 'your-user-pool-client-id',
      identityPoolId: 'your-identity-pool-id',
    }
  },
  API: {
    REST: {
      'your-api-name': {
        endpoint: 'https://your-api-gateway-url',
        region: 'your-region'
      }
    }
  }
});
```

### For Guest Users (Your Current Approach):
```typescript
import { fetchAuthSession } from 'aws-amplify/auth';
import { post, get } from 'aws-amplify/api';

// Get guest session (this is what you're already doing)
const session = await fetchAuthSession();
const token = session.tokens?.accessToken?.toString();

// Create guest user
const createGuestUser = async (deviceId: string) => {
  const response = await post({
    apiName: 'your-api-name',
    path: '/auth/guest',
    options: {
      headers: {
        'Authorization': `Bearer ${token}`,
        'X-Device-Id': deviceId
      },
      body: { deviceId }
    }
  });

  return response.response;
};

// Use for all API calls
const makeApiCall = async (path: string, options = {}) => {
  const session = await fetchAuthSession();
  const token = session.tokens?.accessToken?.toString();

  return await get({
    apiName: 'your-api-name',
    path,
    options: {
      ...options,
      headers: {
        'Authorization': `Bearer ${token}`,
        ...options.headers
      }
    }
  });
};
```

### For Authentication and Migration:
```typescript
import { signIn, signUp } from 'aws-amplify/auth';

// When user signs up/logs in
const handleAuthentication = async (email: string, password: string) => {
  // Get current guest session before authentication
  const guestSession = await fetchAuthSession();
  const guestToken = guestSession.tokens?.accessToken?.toString();

  // Sign in/up user
  await signIn({ username: email, password });

  // Get new authenticated session
  const authSession = await fetchAuthSession();
  const authToken = authSession.tokens?.accessToken?.toString();

  // Check for migration conflicts
  const conflicts = await get({
    apiName: 'your-api-name',
    path: '/auth/migration/conflicts',
    options: {
      headers: {
        'Authorization': `Bearer ${authToken}`
      },
      queryStringParameters: {
        guestUserId: guestSession.identityId
      }
    }
  });

  if (conflicts.hasConflicts) {
    // Show UI for user to choose which data to keep
    const keepGuestData = await showMigrationDialog(conflicts);

    // Resolve migration
    await post({
      apiName: 'your-api-name',
      path: '/auth/migration/resolve',
      options: {
        headers: {
          'Authorization': `Bearer ${authToken}`
        },
        body: {
          guestUserId: guestSession.identityId,
          keepGuestData
        }
      }
    });
  }
};
```

### Device ID Generation:
```typescript
import { getUniqueId } from 'react-native-device-info';

// Generate persistent device ID
const getDeviceId = async () => {
  try {
    return await getUniqueId();
  } catch (error) {
    // Fallback to stored UUID
    const stored = await AsyncStorage.getItem('deviceId');
    if (stored) return stored;

    const newId = uuid.v4();
    await AsyncStorage.setItem('deviceId', newId);
    return newId;
  }
};
```

## Error Handling

- **400**: Missing required parameters
- **404**: Guest user not found or not a guest account
- **409**: Migration already completed
- **503**: No available teams for guest user creation
- **500**: Internal server error

## Security Considerations

1. Guest users have limited permissions through IAM roles
2. Device IDs should be unique and persistent per device
3. Guest accounts are automatically cleaned up after migration
4. Migration can only be performed once per authenticated user

## Testing

Run the test suite:
```bash
npm test src/functions/auth/
```

Tests cover:
- Guest user creation (new and existing)
- Migration conflict detection
- Migration resolution (both directions)
- Error handling scenarios

## Deployment

The infrastructure includes:
- Cognito Identity Pool with guest access enabled
- IAM roles for authenticated and unauthenticated users
- Lambda functions for guest user management
- API Gateway routes with appropriate authorization

Deploy with:
```bash
cd infrastructure
pulumi up
```
