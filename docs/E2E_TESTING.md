# End-to-End Testing

This document explains how to run the comprehensive end-to-end tests for critical lambda functions.

## Overview

We have comprehensive E2E tests for critical lambda functions that create real database environments and test complete workflows. These tests are designed to catch issues that unit tests might miss, particularly around database transactions, entity relationships, and external service integrations.

## Available E2E Tests

### 1. Complete End of Season Flow E2E Test (`src/functions/gameworld/endOfSeasonFlow.e2e.test.ts`)

**🎯 COMPREHENSIVE FLOW TEST** - Tests the entire end-of-season workflow from EventBridge trigger to completion:

#### **What the Complete Flow Test Verifies:**
- **Check for Season End**: EventBridge trigger detection of completed gameworlds
- **End of Season Processing**: Player aging, retirement, energy reset
- **League Movement**: Team promotions/relegations and standings reset
- **Fixture Generation**: Creation of new season fixtures and gameworld end date updates
- **Youth Player Generation**: Creation of youth players for all teams
- **Duplicate Detection**: **IDENTIFIES THE DUPLICATE YOUTH PLAYER ISSUE** you mentioned
- **Complete Integration**: All lambdas working together in the correct sequence

#### **Key Features:**
- **No LocalStack Required**: Mocks SQS calls and directly invokes lambda logic for speed
- **Real Database**: Uses actual PostgreSQL to catch transaction and constraint issues
- **Issue Detection**: Specifically designed to catch the duplicate youth player generation bug
- **Complete Workflow**: Tests the entire flow from your diagram end-to-end

### 2. simulateFixtures E2E Test (`src/functions/fixtures/simulateFixtures.e2e.test.ts`)

Tests the complete fixture simulation workflow including:

#### **What the simulateFixtures Test Verifies**

The E2E test creates a complete database schema and test data, then runs the actual `simulateFixtures` function to verify:

- Fixture result updates
- Team standings updates
- Manager statistics updates
- Player match history creation
- Player overall statistics updates
- Team balance and transaction updates
- Player attribute improvements
- Suspension and injury handling

### 3. processEndOfSeason E2E Test (`src/functions/league/processEndOfSeason.e2e.test.ts`)

Tests the complete end-of-season processing workflow including:

#### **What the processEndOfSeason Test Verifies**

The E2E test creates a complete database schema with teams, players, and managers, then runs the actual `processEndOfSeason` function to verify:

- **Player aging**: All players age by 1 year
- **Player retirement**: Players aged 37+ have correct retirement probability and are marked for retirement
- **Player energy reset**: All players have energy reset to 100
- **Youth player generation**: SQS messages are sent to generate youth players for each team
- **Fixture cleanup**: Old fixture details are removed from DynamoDB
- **Notification handling**: Retirement notifications are processed (mocked)
- **Database transactions**: All operations complete successfully within transactions
- **Unmanaged team handling**: Teams without managers are processed correctly

### AWS Service Mocking

The processEndOfSeason test uses `aws-sdk-client-mock` to mock:
- **SQS**: Verifies youth player generation and fixture generation requests are sent to correct queues
- **DynamoDB**: Mocks fixture detail cleanup operations
- **Notifications**: Ensures retirement notifications are triggered

### 3. processFixtureGenerationQueue E2E Test (`src/functions/generate/processFixtureGenerationQueue.e2e.test.ts`)

Tests the complete fixture generation workflow including:

### What the processFixtureGenerationQueue Test Verifies

The E2E test creates a complete database schema with gameworld, league, and teams, then runs the actual `processFixtureGenerationQueue` function to verify:

- **Fixture Creation**: New fixtures are generated for all team combinations
- **Fixture Properties**: All fixtures have correct gameworld, league, teams, and dates
- **Season End Date**: Gameworld end date is updated based on latest fixture
- **Database Transactions**: All operations complete successfully within transactions
- **Fixture Cleanup**: Old fixture details are removed from DynamoDB
- **Error Handling**: Insufficient teams and missing leagues are handled gracefully
- **Batch Processing**: Multiple fixture generation requests are processed correctly

### AWS Service Mocking

The processFixtureGenerationQueue test uses `aws-sdk-client-mock` to mock:
- **DynamoDB**: Mocks fixture detail cleanup operations to verify old data is removed

## Prerequisites

- Docker and Docker Compose installed
- Node.js and npm installed
- All project dependencies installed (`npm install`)

## Current Status

⚠️ **Note**: The E2E test is currently set up but requires some additional configuration to run properly due to TypeScript path resolution issues. The test file and infrastructure are complete, but you'll need to run it manually with the steps below.

## Manual Setup and Execution

### Step 1: Start PostgreSQL Test Database

```bash
# Start the test database
docker-compose -f docker-compose.test.yml up -d postgres-test

# Wait for it to be ready (check with):
docker-compose -f docker-compose.test.yml exec postgres-test pg_isready -U postgres -d jfg_test
```

### Step 2: Set Environment Variables

```bash
# For Unix/Linux/macOS:
export TEST_DATABASE_NAME=jfg_test
export TEST_DATABASE_HOST=localhost
export TEST_DATABASE_PORT=5433
export TEST_DATABASE_USER=postgres
export TEST_DATABASE_PASSWORD=postgres

# For Windows (PowerShell):
$env:TEST_DATABASE_NAME="jfg_test"
$env:TEST_DATABASE_HOST="localhost"
$env:TEST_DATABASE_PORT="5433"
$env:TEST_DATABASE_USER="postgres"
$env:TEST_DATABASE_PASSWORD="postgres"
```

### Step 3: Run the Test

```bash
# Run the specific E2E test
npm run test -- src/functions/fixtures/simulateFixtures.e2e.test.ts --run
```

### Step 4: Cleanup

```bash
# Stop and remove the test database
docker-compose -f docker-compose.test.yml down -v
```

### Option 2: Manual execution

1. Start the PostgreSQL test container:
```bash
docker-compose -f docker-compose.test.yml up -d postgres-test
```

2. Wait for PostgreSQL to be ready (check with):
```bash
docker-compose -f docker-compose.test.yml exec postgres-test pg_isready -U postgres -d jfg_test
```

3. Set environment variables:
```bash
export TEST_DATABASE_NAME=jfg_test
export TEST_DATABASE_HOST=localhost
export TEST_DATABASE_PORT=5433
export TEST_DATABASE_USER=postgres
export TEST_DATABASE_PASSWORD=postgres
```

4. Run the specific test:
```bash
npm run test -- src/functions/fixtures/simulateFixtures.e2e.test.ts
```

5. Clean up:
```bash
docker-compose -f docker-compose.test.yml down -v
```

## What the Test Verifies

### Database Schema
The test creates a complete database schema with all necessary tables and relationships.

### Test Data Creation
- Creates a gameworld, league, two teams with managers
- Creates 11 players per team with attributes
- Creates an unplayed fixture between the teams

### Simulation Verification
After running the simulation, the test verifies:

1. **Fixture Updates:**
   - `played` flag set to true
   - `score` array populated with match result
   - `simulatedAt` timestamp set
   - `seed` value set for reproducibility

2. **Team Standings:**
   - `played` count incremented for both teams
   - `goalsFor` and `goalsAgainst` updated based on score
   - `wins`, `draws`, `losses` updated correctly
   - `points` calculated correctly (3 for win, 1 for draw, 0 for loss)

3. **Manager Statistics:**
   - `wins`, `draws`, `defeats` updated based on result
   - `goalsScored` and `goalsConceded` updated with match score

4. **Player Data:**
   - `PlayerMatchHistory` records created for players who participated
   - `PlayerOverallStats` updated with match statistics
   - `lastMatchPlayed` timestamp updated
   - Player attributes potentially improved based on performance

5. **Financial Transactions:**
   - `Transactions` records created for match income/expenses
   - Team balances updated accordingly

## Why This Test Is Important

The `simulateFixtures` function is critical to the game's operation and involves complex database transactions across multiple entities. This E2E test is specifically designed to catch issues that unit tests might miss:

### Database Transaction Issues
- **Manager Stats Not Updating**: The test verifies that manager win/loss/goal statistics are properly updated
- **Team Standings Corruption**: Ensures team points, goals, and standings are correctly calculated
- **Player Data Inconsistencies**: Checks that player match history and overall stats are properly recorded
- **Financial Transaction Failures**: Verifies that team balances and transaction records are correctly updated

### Entity Relationship Problems
- **Foreign Key Violations**: Real database constraints catch relationship issues
- **Cascade Delete Problems**: Ensures entity relationships work correctly
- **Transaction Rollback Issues**: Verifies that failed operations don't leave partial data

### Performance and Concurrency Issues
- **Deadlocks**: Real database can reveal transaction deadlock scenarios
- **Connection Pool Exhaustion**: Tests with actual database connections
- **Memory Leaks**: Long-running tests can reveal memory issues

## Troubleshooting

### TypeScript Path Resolution Issues
If you encounter import errors:
```bash
# Try running with ts-node directly
npx ts-node --esm src/functions/fixtures/simulateFixtures.e2e.test.ts
```

### PostgreSQL Connection Issues
- Ensure Docker is running
- Check if port 5433 is available: `netstat -an | grep 5433`
- Verify PostgreSQL container is healthy: `docker-compose -f docker-compose.test.yml ps`
- Check container logs: `docker-compose -f docker-compose.test.yml logs postgres-test`

### Test Failures
- **Manager stats not updating**: This is the specific issue you mentioned - the test will fail if manager statistics aren't being properly updated
- **Database constraint violations**: Check for foreign key or unique constraint errors
- **Transaction timeout**: Increase the test timeout if operations are slow

### Database Schema Issues
- The test automatically creates and drops the schema
- If you encounter schema issues, try cleaning up: `docker-compose -f docker-compose.test.yml down -v`
- Check if all required entities are included in the test setup

## Extending the Tests

To add more test cases:

1. Add new test methods to the existing describe block
2. Use the `createTestData()` helper to set up different scenarios
3. Verify specific edge cases like:
   - Fixtures with no managers
   - Player injuries and suspensions
   - Different match outcomes
   - Financial calculations

## Debugging Failed Tests

When the E2E test fails, it usually indicates a real issue in production. Here's how to debug:

### 1. Check Database State
Add debugging code to inspect the database state:
```typescript
// Add this after the simulation runs
console.log('Fixture after simulation:', await orm.em.findOne(Fixture, { fixtureId: fixture.fixtureId }));
console.log('Home manager after simulation:', await orm.em.findOne(Manager, { managerId: homeManager.managerId }));
```

### 2. Enable Database Logging
Modify the test to enable SQL logging:
```typescript
orm = await MikroORM.init({
  // ... other config
  debug: true, // Enable SQL logging
  logger: (msg) => console.log(msg),
});
```

### 3. Check Transaction Boundaries
If manager stats aren't updating, the issue might be:
- Transaction not being committed properly
- Entity not being persisted within the transaction
- Entity manager context issues

### 4. Verify Service Dependencies
Ensure all required services are properly initialized:
- `TransactionService` has correct MikroORM instance
- `FixtureDatabaseService` has access to repositories
- `FixtureSimulationService` is working correctly

## Performance Considerations

The E2E test is slower than unit tests because it:
- Starts a real PostgreSQL container
- Creates actual database schema
- Performs real database operations

Run these tests separately from your regular test suite for faster development cycles.

## Next Steps

1. **Fix TypeScript Issues**: Update tsconfig.json or use a different test runner to resolve path issues
2. **Add More Test Cases**: Create tests for edge cases like failed transactions, concurrent updates
3. **Integration with CI/CD**: Set up the test to run in your continuous integration pipeline
4. **Performance Monitoring**: Add timing assertions to catch performance regressions
