# EC2 Monolith IAM Permissions ✅

## Overview

The EC2 instance has been configured with the same IAM permissions as your Lambda functions, allowing it to access Cognito and DynamoDB services seamlessly.

## Permissions Added

### 1. Cognito Identity Provider Permissions
```json
{
  "Effect": "Allow",
  "Action": [
    "cognito-idp:InitiateAuth",           // For login authentication
    "cognito-idp:RespondToAuthChallenge", // For MFA/challenges
    "cognito-idp:GetUser",                // Get user details
    "cognito-idp:AdminGetUser",           // Admin get user
    "cognito-idp:AdminCreateUser",        // Create new users
    "cognito-idp:AdminSetUserPassword",   // Set passwords
    "cognito-idp:AdminUpdateUserAttributes", // Update user attributes
    "cognito-idp:ListUsers",              // List users
    "cognito-identity:GetId",             // Get identity ID
    "cognito-identity:GetCredentialsForIdentity" // Get credentials
  ],
  "Resource": "*"
}
```

### 2. DynamoDB Permissions (Global Tables)
The EC2 instance automatically gets access to all DynamoDB tables that Lambda functions can access:

- **Inbox Table**: Read/Write access for notifications
- **All Game Tables**: Same permissions as Lambda functions
  - Players, Teams, Managers, Leagues
  - Fixtures, Transfers, Training
  - Scouting, Purchases, etc.

**Actions Granted**:
```json
{
  "Effect": "Allow",
  "Action": [
    "dynamodb:GetItem",
    "dynamodb:Query", 
    "dynamodb:Scan",
    "dynamodb:BatchGetItem",
    "dynamodb:PutItem",
    "dynamodb:UpdateItem",
    "dynamodb:DeleteItem",
    "dynamodb:BatchWriteItem"
  ],
  "Resource": [
    "arn:aws:dynamodb:*:*:table/table-name",
    "arn:aws:dynamodb:*:*:table/table-name/index/*"
  ]
}
```

### 3. SQS Permissions (Global Queues)
Access to the same SQS queues as Lambda functions:

- **Email Queue**: Send permissions for notifications
- **Other Queues**: As configured in your infrastructure

**Actions Granted**:
```json
{
  "Effect": "Allow", 
  "Action": [
    "sqs:SendMessage",
    "sqs:GetQueueAttributes"
  ],
  "Resource": "arn:aws:sqs:*:*:queue-name"
}
```

### 4. CloudWatch & X-Ray Permissions
```json
{
  "Effect": "Allow",
  "Action": [
    "xray:PutTraceSegments",
    "xray:PutTelemetryRecords", 
    "xray:GetSamplingRules",
    "xray:GetSamplingTargets",
    "xray:GetSamplingStatisticSummaries",
    "logs:CreateLogGroup",
    "logs:CreateLogStream",
    "logs:PutLogEvents",
    "logs:DescribeLogStreams",
    "logs:DescribeLogGroups"
  ],
  "Resource": "*"
}
```

### 5. CloudWatch Agent Permissions
```json
{
  "PolicyArn": "arn:aws:iam::aws:policy/CloudWatchAgentServerPolicy"
}
```

## Your Login Route Support

Your `/auth/login` route in `src/monolith/routes/auth.ts` will now work perfectly because the EC2 instance has:

✅ **Cognito InitiateAuth** permissions for user authentication  
✅ **AWS SDK packages** installed (`@aws-sdk/client-cognito-identity-provider`)  
✅ **Environment variables** for Cognito configuration  
✅ **Same error handling** as your Lambda functions  

## Database Access

All your existing Lambda function business logic will work unchanged because:

✅ **Same DynamoDB permissions** as Lambda functions  
✅ **Same table access patterns** (read/write to all game tables)  
✅ **Same MikroORM configuration** and entity access  
✅ **Same repository patterns** for data access  

## Queue Access

Background job integration works seamlessly:

✅ **Email queue access** for sending notifications  
✅ **Same SQS permissions** as Lambda functions  
✅ **Consistent message formats** between monolith and background jobs  

## Security Notes

- **Principle of Least Privilege**: Only the permissions actually used by Lambda functions
- **Resource-Specific**: DynamoDB permissions are table-specific, not wildcard
- **Audit Trail**: All actions are logged in CloudTrail
- **IAM Role**: Uses EC2 instance role, not embedded credentials

## Testing Your Login Route

Once deployed, test the login functionality:

```bash
# Test login endpoint
curl -X POST https://{cloudfront-domain}/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123"}'

# Expected response:
{
  "accessToken": "eyJ...",
  "idToken": "eyJ...", 
  "refreshToken": "eyJ...",
  "expiresIn": 3600,
  "tokenType": "Bearer"
}
```

## Environment Variables Required

Make sure these are set in your deployment:

```bash
COGNITO_USER_POOL_ID=your-user-pool-id
COGNITO_USER_POOL_CLIENT_ID=your-client-id
AWS_REGION=us-east-1
```

The EC2 instance is now fully equipped to handle all the same operations as your Lambda functions! 🚀
