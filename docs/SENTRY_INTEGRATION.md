# Sentry Error Monitoring Integration ✅

## Overview

Sentry has been integrated into your monolith to capture **unhandled errors** while filtering out expected HTTP error responses. This gives you visibility into genuine issues without noise from normal API validation errors.

## What Gets Monitored

### ✅ **Captured by Sentry**
- **Unhandled exceptions** (database connection errors, null pointer exceptions, etc.)
- **Server errors (5xx)** that aren't explicitly handled
- **Application crashes** and unexpected failures
- **Performance issues** (slow database queries, memory leaks)
- **Database connection failures**
- **Third-party service failures** (AWS SDK errors, etc.)

### ❌ **Filtered Out (Not Sent to Sentry)**
- **Client errors (4xx)** - validation failures, auth errors, not found, etc.
- **Expected business logic errors** - "user not found", "invalid credentials"
- **Health check requests** - ELB health checks are already filtered from logs
- **Handled server errors** - errors with proper error handling

## Smart Error Filtering

### Automatic Filtering
```typescript
// These errors WON'T be sent to Sentry:
throw new Error('Validation failed');           // ❌ Expected
throw new Error('Unauthorized');                // ❌ Expected  
throw new Error('User not found');              // ❌ Expected
res.status(400).json({error: 'Bad request'});   // ❌ 4xx errors

// These errors WILL be sent to Sentry:
throw new Error('Database connection failed');   // ✅ Unexpected
throw new Error('Cannot read property of null'); // ✅ Code bug
// Unhandled promise rejections                  // ✅ Unexpected
```

### Custom Error Handling
```typescript
// Mark errors as handled to prevent Sentry capture
const error = new Error('Expected business error');
error.handled = true;
error.statusCode = 400;
throw error; // Won't be sent to Sentry
```

## Sentry Configuration

### Environment-Based Settings
```typescript
// Development: Full monitoring
tracesSampleRate: 1.0,     // 100% performance monitoring
profilesSampleRate: 1.0,   // 100% profiling

// Production: Optimized sampling  
tracesSampleRate: 0.1,     // 10% performance monitoring
profilesSampleRate: 0.1,   // 10% profiling
```

### Context Enrichment
Every error includes:
- **Request details**: Method, path, query parameters, headers
- **User context**: User ID, email (when authenticated)
- **Request ID**: For correlation with logs
- **Environment**: Development/production
- **Performance data**: Response times, database query times

## Error Context Example

When an error occurs, Sentry receives rich context:

```json
{
  "error": "Cannot read property 'id' of undefined",
  "environment": "production",
  "user": {
    "id": "user-123",
    "email": "<EMAIL>"
  },
  "request": {
    "method": "POST",
    "path": "/transfer/offer",
    "query": {"gameworld": "test"},
    "requestId": "uuid-here",
    "headers": {
      "user-agent": "Mozilla/5.0...",
      "authorization": "[REDACTED]"
    }
  },
  "tags": {
    "component": "monolith",
    "service": "jumpers-for-goalposts-api"
  }
}
```

## Integration Points

### 1. Application Initialization
```typescript
// src/monolith/app.ts
import { initSentry } from './utils/sentry.js';
initSentry(); // Must be first import
```

### 2. Request Middleware
```typescript
// Automatic request tracking
app.use(sentryRequestHandler());
```

### 3. Error Handling
```typescript
// Captures unhandled errors
app.use(sentryErrorHandler());
app.use(errorHandler); // Your custom handler
```

### 4. User Context
```typescript
// Automatically sets user context when authenticated
if (req.user) {
  setUserContext({
    id: req.user.userId,
    email: req.user.email,
  });
}
```

## Manual Error Reporting

### Capture Exceptions with Context
```typescript
import { captureException } from './utils/sentry.js';

try {
  await riskyOperation();
} catch (error) {
  captureException(error, {
    operation: 'transfer-processing',
    playerId: player.id,
    teamId: team.id,
  });
  throw error; // Re-throw if needed
}
```

### Capture Messages
```typescript
import { captureMessage } from './utils/sentry.js';

captureMessage('Unusual activity detected', 'warning', {
  userId: user.id,
  action: 'multiple-login-attempts',
});
```

## Sentry Dashboard Features

### Error Grouping
- **Automatic grouping** by error type and stack trace
- **Smart deduplication** prevents spam from repeated errors
- **Release tracking** shows which deployment introduced issues

### Performance Monitoring
- **API endpoint performance** - response times, throughput
- **Database query performance** - slow queries, connection issues
- **Memory usage patterns** - potential memory leaks

### Alerting
- **Email notifications** for new error types
- **Slack integration** for critical errors
- **Custom alert rules** based on error frequency

## Deployment Integration

### Environment Variables
```bash
# Automatically set during deployment
SENTRY_DSN=https://c63dcc9af7c69abf692865500b2dcb68@...
NODE_ENV=production
```

### Release Tracking
```bash
# Future enhancement: Track deployments
SENTRY_RELEASE=v1.2.3
SENTRY_ENVIRONMENT=stage
```

## Monitoring Best Practices

### 1. Error Triage
- **High Priority**: Database errors, authentication failures
- **Medium Priority**: Third-party service errors, performance issues  
- **Low Priority**: Validation errors (should be filtered anyway)

### 2. Performance Monitoring
- **Watch for**: Slow database queries (>1s), memory leaks, high CPU
- **Alert on**: Error rate >5%, response time >2s average

### 3. User Impact
- **Track**: Which users are affected by errors
- **Correlate**: Error patterns with specific user actions

## Testing Sentry Integration

### Test Error Capture
```bash
# Create a test endpoint that throws an error
curl -X POST https://your-domain/test-error
```

### Verify Filtering
```bash
# These should NOT appear in Sentry
curl https://your-domain/nonexistent-endpoint  # 404
curl -X POST https://your-domain/auth/login    # 401 with bad creds
```

### Check Context
- Verify user information appears in Sentry events
- Confirm request IDs match between logs and Sentry
- Test performance monitoring data

## Benefits Achieved

### 🔍 **Better Debugging**
- **Rich context** for every error
- **Request correlation** between logs and Sentry
- **User impact assessment** - which users affected

### 🚨 **Proactive Monitoring**
- **Real-time error alerts** via email/Slack
- **Performance degradation detection**
- **Release impact tracking**

### 📊 **Data-Driven Improvements**
- **Error frequency trends** over time
- **Performance bottleneck identification**
- **User experience impact metrics**

### 🎯 **Noise Reduction**
- **Smart filtering** eliminates expected errors
- **Automatic grouping** prevents duplicate alerts
- **Severity-based prioritization**

## Ready for Production

Your Sentry integration is now:
- ✅ **Configured** with smart error filtering
- ✅ **Integrated** with request/response lifecycle
- ✅ **Enriched** with user and request context
- ✅ **Deployed** with environment variables
- ✅ **Optimized** for production performance

Deploy and start monitoring your monolith's health in real-time! 🚀

## Sentry Dashboard Access

Visit your Sentry project:
- **URL**: https://sentry.io/organizations/your-org/projects/
- **Project**: jumpers-for-goalposts-monolith
- **Environment**: production/development

You'll see errors, performance data, and user impact metrics as they occur.
