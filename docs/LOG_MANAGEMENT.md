# Monolith Log Management 📋

## Problem Solved ✅

Your monolith logs were getting too large due to health check noise (every 30 seconds). This has been fixed with:

1. **Health Check Filtering**: ELB health checks no longer spam the logs
2. **Automatic Log Rotation**: Daily rotation with 7-day retention
3. **Disk Space Protection**: Automatic cleanup when logs get too large
4. **CloudWatch Integration**: Structured logs sent to CloudWatch with retention

## Log Filtering

### Health Check Noise Eliminated
```typescript
// Before: Every 30 seconds
{"timestamp":"2025-08-26T15:38:21.442Z","level":"INFO","message":"Incoming request","requestId":"c3d10618-5b4c-4694-864c-21376b65d688","method":"GET","path":"/ping","userAgent":"ELB-HealthChecker/2.0","ip":"***********"}

// After: Health checks are silent (unless they fail)
// Only real API requests are logged
```

### What Gets Logged Now
- ✅ **Real API requests**: All non-health-check requests
- ✅ **Failed health checks**: If health check returns 4xx/5xx
- ✅ **Error conditions**: All errors and warnings
- ✅ **Authentication events**: Login attempts, token validation
- ❌ **Successful health checks**: Filtered out to reduce noise

## Log Rotation Setup

### Daily Rotation
```bash
# /etc/logrotate.d/monolith
/var/log/monolith.log {
    daily           # Rotate daily
    rotate 7        # Keep 7 days of backups
    compress        # Compress old logs
    delaycompress   # Don't compress today's log
    missingok       # Don't error if log missing
    notifempty      # Don't rotate empty logs
    create 644 ec2-user ec2-user
}
```

### File Structure
```
/var/log/
├── monolith.log              # Current log
├── monolith.log.1            # Yesterday (uncompressed)
├── monolith.log.2.gz         # 2 days ago (compressed)
├── monolith.log.3.gz         # 3 days ago (compressed)
├── ...
└── monolith.log.7.gz         # 7 days ago (compressed)
```

## Automatic Cleanup

### Size-Based Rotation
- **Trigger**: When log file exceeds 100MB
- **Action**: Force rotation immediately
- **Frequency**: Checked every hour via cron

### Cleanup Script
```bash
# /usr/local/bin/check-log-size.sh
# Runs hourly to prevent disk space issues
```

### Cron Schedule
```bash
# Check log size every hour
0 * * * * root /usr/local/bin/check-log-size.sh
```

## CloudWatch Integration

### Log Streams
- **Application Logs**: `/aws/ec2/stage-monolith` → `{instance-id}/application`
- **System Logs**: `/aws/ec2/stage-monolith` → `{instance-id}/system`
- **Retention**: 7 days in CloudWatch

### Benefits
- **Centralized Logging**: View logs in AWS Console
- **Search & Filter**: Query logs by request ID, user, error type
- **Alerts**: Set up alarms on error patterns
- **Backup**: CloudWatch provides additional log backup

## Log Levels & Filtering

### Production Log Levels
```bash
# Set in environment variables
LOG_LEVEL=INFO  # Default: INFO, DEBUG, WARN, ERROR
```

### Request Correlation
Every request gets a unique ID for tracing:
```json
{
  "timestamp": "2025-08-26T15:38:21.442Z",
  "level": "INFO",
  "message": "Request completed",
  "requestId": "c3d10618-5b4c-4694-864c-21376b65d688",
  "method": "POST",
  "path": "/auth/login",
  "statusCode": 200,
  "duration": "45ms",
  "userId": "user-123"
}
```

## Monitoring & Alerts

### Disk Space Monitoring
- **CloudWatch Alarm**: Triggers at 90% disk usage
- **Email Alert**: Sent to your configured email
- **Auto-cleanup**: Removes old logs if disk gets full

### Log Error Patterns
Set up CloudWatch alarms for:
- **Error Rate**: More than 10 errors per minute
- **Failed Logins**: More than 5 failed login attempts
- **Database Errors**: Any DynamoDB connection issues

## Troubleshooting Commands

### View Current Logs
```bash
# Real-time log viewing
sudo tail -f /var/log/monolith.log

# View last 100 lines
sudo tail -n 100 /var/log/monolith.log

# Search for specific request ID
sudo grep "c3d10618-5b4c-4694-864c-21376b65d688" /var/log/monolith.log*
```

### Check Log Rotation Status
```bash
# Test log rotation
sudo logrotate -d /etc/logrotate.d/monolith

# Force log rotation
sudo logrotate -f /etc/logrotate.d/monolith

# Check rotation history
sudo cat /var/lib/logrotate/logrotate.state | grep monolith
```

### Disk Space Management
```bash
# Check log file sizes
sudo du -h /var/log/monolith.log*

# Check total disk usage
df -h

# Manual cleanup (if needed)
sudo find /var/log -name "monolith.log.*" -type f -mtime +7 -delete
```

### Service Management
```bash
# Reload service (reopens log files after rotation)
sudo systemctl reload monolith

# Check service status
sudo systemctl status monolith

# View service logs
sudo journalctl -u monolith -f
```

## CloudWatch Log Queries

### Useful Log Insights Queries
```sql
-- Find all errors in last hour
fields @timestamp, level, message, requestId
| filter level = "ERROR"
| sort @timestamp desc
| limit 100

-- Find slow requests (>1 second)
fields @timestamp, message, duration, path
| filter message = "Request completed"
| filter duration like /[0-9]{4,}ms/
| sort @timestamp desc

-- Find failed login attempts
fields @timestamp, message, path, statusCode
| filter path = "/auth/login" and statusCode >= 400
| sort @timestamp desc
```

## Benefits Achieved

- 🔇 **Reduced Log Noise**: 99% reduction in log volume (no health checks)
- 💾 **Disk Space Protected**: Automatic rotation prevents disk full
- 🔍 **Better Debugging**: Request correlation makes troubleshooting easier
- 📊 **Centralized Monitoring**: CloudWatch integration for alerts
- 🗂️ **Organized Retention**: 7 days local + 7 days CloudWatch backup
- ⚡ **Performance**: Smaller log files = faster searches and processing

Your monolith logs are now production-ready with proper rotation and filtering! 🚀
