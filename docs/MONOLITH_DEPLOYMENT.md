# Zero-Disruption Migration to Monolith

## Overview

This guide covers the zero-disruption migration from serverless Lambda functions to an Express.js monolith, allowing you to test the new architecture while keeping your existing API fully functional.

## Migration Strategy

### Current Setup (Unchanged)
- **Existing API**: `https://jfg-stage.rwscripts.com/{endpoint}` → API Gateway → Lambda functions
- **Client Impact**: None - existing clients continue to work normally

### New Monolith Setup
- **New API**: `https://jfg-stage.rwscripts.com/v2/{endpoint}` → CloudFront → ALB → EC2 Monolith
- **Testing**: You can test the new monolith using `/v2/` prefixed endpoints

### Architecture Overview
```
                    ┌─────────────────────────────────────┐
                    │        CloudFront CDN               │
                    │   jfg-stage.rwscripts.com          │
                    └─────────────┬───────────────────────┘
                                  │
                    ┌─────────────▼───────────────────────┐
                    │         Path Routing                │
                    │  /* → API Gateway (existing)        │
                    │  /v2/* → ALB (new monolith)        │
                    └─────────────┬───────────────────────┘
                                  │
        ┌─────────────────────────┼─────────────────────────┐
        │                         │                         │
        ▼                         ▼                         │
┌───────────────┐         ┌──────────────┐                 │
│  API Gateway  │         │     ALB      │                 │
│   (existing)  │         │ (new setup)  │                 │
└───────┬───────┘         └──────┬───────┘                 │
        │                        │                         │
        ▼                        ▼                         │
┌───────────────┐         ┌──────────────┐                 │
│    Lambda     │         │  EC2 t4g.micro│                │
│  Functions    │         │   Monolith    │                │
│  (existing)   │         │  (new setup)  │                │
└───────────────┘         └──────────────┘                 │
```

## Migration Steps

### Phase 1: Deploy Infrastructure (Zero Impact)

1. **Deploy CloudFront + EC2**: The infrastructure deployment adds CloudFront routing and EC2 monolith without affecting existing traffic
2. **Test v2 Endpoints**: Verify the monolith works using `/v2/` prefixed endpoints
3. **Monitor Performance**: Compare response times and error rates

### Phase 2: Gradual Migration (When Ready)

1. **Update Client Gradually**: Change client endpoints from `/endpoint` to `/v2/endpoint`
2. **Monitor Both Systems**: Keep both running during transition
3. **Complete Migration**: Once confident, update DNS to point directly to CloudFront

### Phase 3: Cleanup (Optional)

1. **Remove Old Infrastructure**: Decommission API Gateway and Lambda functions
2. **Update CloudFront**: Remove `/v2` prefix requirement
3. **Optimize**: Fine-tune EC2 instance size based on actual usage

## Testing Your Migration

### Endpoint Mapping

| Current Endpoint | New V2 Endpoint | Purpose |
|------------------|-----------------|---------|
| `GET /ping` | `GET /v2/ping` | Health check |
| `GET /manager` | `GET /v2/manager` | Get manager |
| `PUT /manager/name` | `PUT /v2/manager/name` | Update manager |
| `GET /{gameworld}/leagues` | `GET /v2/{gameworld}/leagues` | Get leagues |
| `GET /{gameworld}/team/{id}` | `GET /v2/{gameworld}/team/{id}` | Get team |
| `POST /transfer/offer` | `POST /v2/transfer/offer` | Submit offer |

### Testing Commands

```bash
# Test health check (no auth required)
curl https://jfg-stage.rwscripts.com/v2/ping

# Test authenticated endpoint (requires Bearer token)
curl -H "Authorization: Bearer YOUR_TOKEN" \
     https://jfg-stage.rwscripts.com/v2/manager

# Test gameworld endpoint
curl -H "Authorization: Bearer YOUR_TOKEN" \
     https://jfg-stage.rwscripts.com/v2/test-gameworld/leagues
```

## Infrastructure Components

### 1. CloudFront Distribution
- **Purpose**: Routes traffic based on path patterns
- **Routing**: `/*` → API Gateway, `/v2/*` → ALB
- **SSL**: Automatic HTTPS with ACM certificate
- **Caching**: Disabled for API responses

### 2. EC2 Instance
- **Type**: t4g.micro (ARM-based, cost-effective)
- **OS**: Amazon Linux 2023
- **Auto-scaling**: Ready for future implementation
- **Security**: Security groups restrict access to ALB and SSH

### 2. Application Load Balancer (ALB)
- **Health Checks**: `/ping` endpoint every 30 seconds
- **SSL**: Ready for certificate attachment
- **Multi-AZ**: Distributes traffic across availability zones

### 3. Monitoring & Alerting
- **CloudWatch Metrics**: CPU, Memory, Disk usage
- **Application Logs**: Structured JSON logs with request correlation
- **Alarms**: Email notifications for high resource usage
- **Dashboard**: Real-time monitoring of instance and application metrics

## Deployment Process

### Automated Deployment (GitHub Actions)

The deployment is fully automated through GitHub Actions:

1. **Trigger**: Runs after successful tests on `main` branch
2. **Infrastructure**: Pulumi deploys/updates AWS resources
3. **Application**: Builds and deploys monolith to EC2
4. **Verification**: Checks service health after deployment

### Required GitHub Secrets

Add these secrets to your GitHub repository:

```
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_REGION=us-east-1
PULUMI_ACCESS_TOKEN=your-pulumi-token
EC2_PRIVATE_KEY=your-ec2-private-key-content
DATABASE_URL=your-database-connection-string
COGNITO_USER_POOL_ID=your-cognito-user-pool-id
COGNITO_USER_POOL_CLIENT_ID=your-cognito-client-id
```

### Manual Deployment

If needed, you can deploy manually:

```bash
# 1. Deploy infrastructure
cd infrastructure
pulumi up --stack stage

# 2. Build and deploy application
npm run monolith:build
./scripts/deploy-monolith.sh stage <instance-ip>
```

## Monitoring & Logging

### Structured Logging

The monolith uses structured logging with request correlation:

```json
{
  "timestamp": "2024-01-01T12:00:00.000Z",
  "level": "INFO",
  "message": "Request completed",
  "requestId": "uuid-here",
  "functionName": "getManager",
  "method": "GET",
  "path": "/manager",
  "statusCode": 200,
  "duration": "45ms",
  "userId": "user-123"
}
```

### CloudWatch Alarms

Automatic alerts are configured for:

- **High CPU**: >80% for 10 minutes
- **High Memory**: >85% for 10 minutes  
- **Low Disk Space**: >90% usage
- **Application Health**: Unhealthy ALB targets

### Dashboard

Access the CloudWatch dashboard:
- Navigate to CloudWatch in AWS Console
- Find dashboard: `{stage}-monolith-monitoring`
- View real-time metrics and recent error logs

## Scaling Considerations

### Vertical Scaling
- Monitor CPU/Memory usage in CloudWatch
- Upgrade instance type if consistently high usage
- t4g.small → t4g.medium → t4g.large

### Horizontal Scaling
- Add Auto Scaling Group with multiple instances
- Update ALB to distribute traffic
- Consider database connection pooling limits

### Database Connections
- Current: Single persistent connection per instance
- Scaling: Implement connection pooling
- Monitor: Database connection count metrics

## Performance Optimization

### Current Optimizations
- Persistent database connections (no cold starts)
- ARM-based EC2 for cost efficiency
- Application Load Balancer for high availability
- Structured logging for debugging

### Future Optimizations
- Redis caching layer
- CDN for static assets
- Database read replicas
- Container deployment (ECS/EKS)

## Troubleshooting

### Service Not Starting
```bash
# SSH to instance
ssh -i ~/.ssh/key.pem ec2-user@<instance-ip>

# Check service status
sudo systemctl status monolith

# View logs
sudo journalctl -u monolith -f
tail -f /var/log/monolith.log
```

### High Resource Usage
1. Check CloudWatch dashboard for metrics
2. Review application logs for errors
3. Consider scaling up instance type
4. Optimize database queries if needed

### Deployment Failures
1. Check GitHub Actions logs
2. Verify all secrets are configured
3. Ensure EC2 instance is accessible
4. Check systemd service configuration

## Cost Optimization

### Current Setup
- t4g.micro: ~$6/month (ARM-based)
- ALB: ~$16/month + data transfer
- CloudWatch: ~$3/month for logs/metrics

### Optimization Tips
- Use Reserved Instances for 1-3 year commitments
- Monitor CloudWatch costs and adjust retention
- Consider Spot Instances for non-production
- Optimize log verbosity in production

## Security

### Current Security Measures
- Security groups restrict network access
- IAM roles with minimal permissions
- SSH key-based authentication
- Environment variables for secrets

### Additional Recommendations
- Enable AWS Systems Manager Session Manager
- Implement log encryption
- Regular security updates via automation
- Consider AWS Secrets Manager for sensitive data

## Migration from Serverless

### What Moved to Monolith
- All HTTP API endpoints
- Authentication middleware
- Database connections
- Request/response handling

### What Remains Serverless
- SQS queue processors
- Scheduled functions (cron jobs)
- Webhook handlers
- Background processing

This hybrid approach gives you the best of both worlds: fast API responses with persistent connections, while keeping event-driven processing as serverless functions.
