# League Movement Failure Handling Implementation

## Overview

This document describes the implementation of a robust failure handling mechanism for the `processLeagueMovement` lambda function. Previously, this lambda was invoked directly from `checkForSeasonEnd`, which meant that if the lambda failed, the event data would be lost with no way to retry.

## Problem

The original implementation had the following issues:
- Direct lambda invocation from `checkForSeasonEnd.ts`
- No retry mechanism for failed league movement processing
- Lost data when processing failed
- No visibility into failures beyond CloudWatch logs

## Solution

We implemented an SQS-based queue system with Dead Letter Queue (DLQ) support to provide:
- **Reliable message delivery** with automatic retries
- **Dead Letter Queue** for failed messages that can be manually reprocessed
- **Batch failure reporting** to ensure only failed items go to DLQ
- **CloudWatch monitoring** with alarms for DLQ messages

## Architecture Changes

### 1. New SQS Infrastructure

**Queue**: `leagueMovementQueue`
- Visibility timeout: 180 seconds (3 minutes)
- Max receive count: 3 (retry 3 times before DLQ)

**Dead Letter Queue**: `leagueMovementDLQ`
- Retention: 7 days
- CloudWatch alarms for monitoring

### 2. Event Type

Created `LeagueMovementEvent` type:
```typescript
interface LeagueMovementEvent {
  gameworldId: string;
}
```

### 3. Lambda Changes

#### checkForSeasonEnd.ts
- **Before**: Direct lambda invocation using `Lambda.eventInvoke()`
- **After**: Sends messages to `leagueMovementQueue` using SQS

#### processLeagueMovement.ts
- **Before**: Event-triggered lambda with `eventMiddify`
- **After**: SQS-triggered lambda with `sqsMiddify`
- **Added**: Batch failure reporting for reliable retry handling
- **Added**: Per-message error handling and logging

## Implementation Details

### Queue Configuration

```typescript
// Infrastructure setup
const leagueMovementDLQ = createDLQ('leagueMovement');
const leagueMovementQueue = createQueue(
  'leagueMovement',
  leagueMovementDLQ,
  3, // maxReceiveCount - retry 3 times before going to DLQ
  {
    visibilityTimeout: 180, // 3 minutes for league movement processing
  }
);
```

### Lambda Configuration

```typescript
// SQS Event Source Mapping
createMonitoredEventSourceMapping(
  'processLeagueMovement',
  processLeagueMovementLambda,
  leagueMovementQueue,
  leagueMovementDLQ,
  1, // batchSize - process one gameworld at a time
  0, // no batching window for immediate processing
  {
    functionResponseTypes: ['ReportBatchItemFailures'],
  },
  [errorAlarmTopic.arn]
);
```

### Error Handling

The lambda now returns `SQSBatchResponse` with failed message IDs:

```typescript
const batchItemFailures: { itemIdentifier: string }[] = [];

for (const record of event.Records) {
  try {
    // Process league movement...
  } catch (error) {
    // Log error and add to batch failures
    batchItemFailures.push({ itemIdentifier: record.messageId });
  }
}

return { batchItemFailures };
```

## Benefits

1. **Reliability**: Failed messages automatically retry up to 3 times
2. **Observability**: CloudWatch alarms notify when messages go to DLQ
3. **Recoverability**: Failed messages in DLQ can be manually reprocessed
4. **Isolation**: Each gameworld is processed independently
5. **Monitoring**: Detailed logging with correlation IDs for debugging

## Monitoring

- **CloudWatch Alarms**: Triggered when messages appear in DLQ
- **Detailed Logging**: Each message processing includes correlation ID
- **Batch Failure Reporting**: Only failed messages are retried/sent to DLQ

## Manual Recovery

If messages end up in the DLQ, they can be:
1. Inspected in the AWS Console
2. Manually moved back to the main queue for reprocessing
3. Processed individually for debugging

## Testing

The implementation includes comprehensive tests covering:
- Successful message processing
- Individual message failures
- Batch processing with partial failures
- Service initialization and error handling

## Environment Variables

- `LEAGUE_MOVEMENT_QUEUE_URL`: URL of the league movement queue (set in checkForSeasonEnd)
- `FIXTURE_GENERATION_QUEUE_URL`: URL of the fixture generation queue (used by processLeagueMovement)
