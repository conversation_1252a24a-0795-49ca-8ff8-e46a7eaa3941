services:
  localstack:
    image: localstack/localstack
    environment:
      - SERVICES=lambda,dynamodb,apigateway,iam,s3,sns,sqs,logs,events,scheduler,cloudwatch
      - DEBUG=1
      - PORT_WEB_UI=8090
      - AWS_ACCESS_KEY_ID=local
      - AWS_SECRET_ACCESS_KEY=local
      - AWS_REGION=us-east-2
      - AWS_DEFAULT_REGION=us-east-2
    ports:
      - '4566:4566'
      - '4510-4559:4510-4559'
    volumes:
      - '/var/run/docker.sock:/var/run/docker.sock' # Required for Lambda execution
      - './localstack:/var/lib/localstack'
    networks:
      - local

  dynamodb-admin:
    image: "aaronshaf/dynamodb-admin:latest"
    ports:
      - '8001:8001'
    environment:
      - DYNAMO_ENDPOINT=http://localstack:4566
      - AWS_ACCESS_KEY_ID=local
      - AWS_SECRET_ACCESS_KEY=local
      - AWS_REGION=us-east-2
      - AWS_DEFAULT_REGION=us-east-2
    depends_on:
      - 'localstack'
    networks:
      - local
networks:
  local:
    driver: 'bridge'
