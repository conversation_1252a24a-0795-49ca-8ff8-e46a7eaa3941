/* eslint-disable @typescript-eslint/no-unsafe-call */
import { describe, expect, it } from 'vitest';
import {
  seededRandomIntInRange,
  setAndReturnSeededRandom,
  setRandomSeed,
} from './seeded-random.js';

describe('setAndReturnSeededRandom', () => {
  it('returns a valid seed within the safe integer range', () => {
    const seed = setAndReturnSeededRandom();
    expect(seed).toBeGreaterThanOrEqual(0);
    expect(seed).toBeLessThanOrEqual(Number.MAX_SAFE_INTEGER);
  });

  it('sets the seeded random generator to produce reproducible results', () => {
    const seed = setAndReturnSeededRandom();
    const firstValue = seededRandomIntInRange(1, 100);
    setRandomSeed(seed);
    const secondValue = seededRandomIntInRange(1, 100);
    expect(firstValue).toBe(secondValue);
  });
});

describe('seededRandomIntInRange', () => {
  it('generates a random integer within the specified range', () => {
    setRandomSeed(12345);
    const randomValue = seededRandomIntInRange(10, 20);
    expect(randomValue).toBeGreaterThanOrEqual(10);
    expect(randomValue).toBeLessThanOrEqual(20);
  });

  it('returns the same value when min and max are equal', () => {
    setRandomSeed(12345);
    const randomValue = seededRandomIntInRange(15, 15);
    expect(randomValue).toBe(15);
  });

  it('handles large ranges correctly', () => {
    setRandomSeed(12345);
    const randomValue = seededRandomIntInRange(1, 1_000_000);
    expect(randomValue).toBeGreaterThanOrEqual(1);
    expect(randomValue).toBeLessThanOrEqual(1_000_000);
  });

  it('should return at least 1 of each number in the range when called multiple times', () => {
    setRandomSeed(12345);
    const iterations = 1000;
    const counts: Record<number, number> = {};

    for (let i = 0; i < iterations; i++) {
      const randomValue = seededRandomIntInRange(1, 2);
      counts[randomValue] = (counts[randomValue] || 0) + 1;
    }

    for (let i = 1; i <= 2; i++) {
      expect(counts[i]).toBeGreaterThan(0);
    }
  });
});
