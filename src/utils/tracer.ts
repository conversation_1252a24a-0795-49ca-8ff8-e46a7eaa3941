import { logger } from '@/utils/logger.js';
import { Tracer } from '@aws-lambda-powertools/tracer';

if (!process.env.SERVICE_NAME) {
  logger.warn(
    'SERVICE_NAME environment variable not set.',
    'The tracer will be created without this metadata'
  );
}

if (process.env.TRACER_ENABLED !== 'true') {
  logger.warn(
    'TRACER_ENABLED environment variable not set to true.',
    'Tracing is disabled for this lambda'
  );
}

export const tracer = new Tracer({
  serviceName: process.env.SERVICE_NAME!,
  enabled: process.env.TRACER_ENABLED === 'true',
});
