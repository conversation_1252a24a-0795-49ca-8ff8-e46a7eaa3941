import { HttpEvent } from '@/middleware/rest/types.js';

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function getUser(event: HttpEvent<any, any, any>) {
  if (process.env.STAGE === 'dev') {
    return process.env.DEBUG_USER_ID!;
  }
  return event.requestContext.authorizer!.userId as string;
}

export function getUserEmail(event: HttpEvent<any, any, any>): string | undefined {
  if (process.env.STAGE === 'dev') {
    return process.env.DEBUG_USER_EMAIL;
  }
  return event.requestContext.authorizer!.email;
}
