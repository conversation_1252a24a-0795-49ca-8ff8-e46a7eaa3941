import { Logger as PowertoolsLogger } from '@aws-lambda-powertools/logger';

/**
 * Extended Logger class that adds a local function for development-only logging
 */
class Logger extends PowertoolsLogger {
  /**
   * Logs a debug message only when NODE_ENV is set to development
   * @param message - The message to log
   * @param args - Additional arguments to include in the log
   */
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  local(message: string, ...args: any[]): void {
    if (process.env.NODE_ENV === 'development') {
      // eslint-disable-next-line @typescript-eslint/no-unsafe-argument
      this.debug(message, ...args);
    }
  }
}

// Create an instance of the extended logger
export const logger = new Logger({
  serviceName: process.env.SERVICE_NAME!,
});

if (!process.env.SERVICE_NAME) {
  logger.warn(
    'SERVICE_NAME environment variable not set.',
    'The logger created without this metadata'
  );
}
