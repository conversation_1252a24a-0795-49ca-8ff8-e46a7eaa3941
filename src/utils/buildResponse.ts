import type { APIGatewayProxyResult } from 'aws-lambda';

const headers = {
  'Access-Control-Allow-Headers': '*',
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS,PATCH',
  'Access-Control-Allow-Credentials': true,
  'Strict-Transport-Security': 'max-age=31536000;includeSubDomains',
  'X-XSS-Protection': '0',
  'X-Content-Type-Options': 'nosniff',
  'X-Frame-Options': 'Deny',
  'Cache-Control': 'no-store',
  Pragma: 'no-cache',
  'Content-Security-Policy': "frame-ancestors 'none'; default-src 'self'",
  'Referrer-Policy': 'no-referrer',
  'Feature-Policy': 'none',
  'Content-Type': 'application/json',
  'X-Permitted-Cross-Domain-Policies': 'none',
};

export const buildResponse = (statusCode: number, body: string): APIGatewayProxyResult => ({
  isBase64Encoded: false,
  statusCode,
  headers,
  body,
});

export function buildHtmlResponse(
  title: string,
  body: string,
  statusCode = 200
): APIGatewayProxyResult {
  const html = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Jumpers for Goalposts - ${title}</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link href="https://fonts.googleapis.com/css?family=Nunito:400,700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Nunito', sans-serif; background: #fafbfc; color: #222; margin: 0; padding: 0; }
        .container { max-width: 700px; margin: 40px auto; background: #fff; border-radius: 10px; box-shadow: 0 2px 8px rgba(0,0,0,0.05); padding: 32px 24px 24px 24px; }
        .logo { display: flex; justify-content: center; margin-bottom: 24px; }
        .logo img { max-width: 180px; height: auto; }
        h1 { text-align: center; font-weight: 700; margin-bottom: 24px; }
        h2 { font-weight: 700; margin-top: 32px; }
        p, li { font-size: 1.08em; line-height: 1.7; }
        ul { margin-left: 20px; }
    </style>
</head>
<body>
<div class="container">
    <div class="logo">
        <img src="https://rwscripts.com/jfg/logo.png" alt="Logo">
    </div>
    <h1>${title}</h1>
    <p>${body}</p>
</div>
</body>
</html>`;
  return {
    isBase64Encoded: false,
    statusCode,
    headers: {
      'Content-Type': 'text/html; charset=utf-8',
      'Cache-Control': 'no-store',
      'Content-Security-Policy':
        "default-src 'self' https:; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' https: data:",
      'X-Content-Type-Options': 'nosniff',
      'X-Frame-Options': 'SAMEORIGIN',
    },
    body: html,
  };
}
