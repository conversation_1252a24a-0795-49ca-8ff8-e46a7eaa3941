import { logger } from './logger.js';

/**
 * Performance monitoring utilities for Lambda functions
 */

export interface PerformanceMetrics {
  coldStart: boolean;
  initDuration?: number;
  dbInitDuration?: number;
  repositoryCreationDuration?: number;
  totalDuration?: number;
}

let isColdStart = true;
const performanceMetrics: PerformanceMetrics = {
  coldStart: true,
};

/**
 * Marks the end of cold start and beginning of warm execution
 */
export function markWarmStart(): void {
  isColdStart = false;
  performanceMetrics.coldStart = false;
}

/**
 * Checks if this is a cold start
 */
export function isColdStartExecution(): boolean {
  return isColdStart;
}

/**
 * Times a function execution and logs the result
 */
export async function timeExecution<T>(
  name: string,
  fn: () => Promise<T>,
  logLevel: 'debug' | 'info' = 'debug'
): Promise<T> {
  const start = performance.now();
  try {
    const result = await fn();
    const duration = performance.now() - start;
    
    if (logLevel === 'info') {
      logger.info(`${name} completed in ${Math.round(duration)}ms`);
    } else {
      logger.debug(`${name} completed in ${Math.round(duration)}ms`);
    }
    
    return result;
  } catch (error) {
    const duration = performance.now() - start;
    logger.error(`${name} failed after ${Math.round(duration)}ms`, { error });
    throw error;
  }
}

/**
 * Records performance metrics for analysis
 */
export function recordMetric(key: keyof PerformanceMetrics, value: number): void {
  performanceMetrics[key] = value;
}

/**
 * Gets current performance metrics
 */
export function getPerformanceMetrics(): PerformanceMetrics {
  return { ...performanceMetrics };
}

/**
 * Logs performance summary
 */
export function logPerformanceSummary(): void {
  const metrics = getPerformanceMetrics();
  
  if (metrics.coldStart) {
    logger.info('Cold start performance metrics', {
      coldStart: metrics.coldStart,
      initDuration: metrics.initDuration,
      dbInitDuration: metrics.dbInitDuration,
      repositoryCreationDuration: metrics.repositoryCreationDuration,
      totalDuration: metrics.totalDuration,
    });
  } else {
    logger.debug('Warm start execution', {
      coldStart: metrics.coldStart,
      totalDuration: metrics.totalDuration,
    });
  }
}

/**
 * Optimizes Node.js garbage collection for Lambda
 */
export function optimizeGarbageCollection(): void {
  // Force garbage collection if available (requires --expose-gc flag)
  if (global.gc) {
    global.gc();
  }
  
  // Set Node.js memory optimization flags for Lambda
  if (process.env.NODE_OPTIONS) {
    process.env.NODE_OPTIONS += ' --max-old-space-size=256 --optimize-for-size';
  } else {
    process.env.NODE_OPTIONS = '--max-old-space-size=256 --optimize-for-size';
  }
}
