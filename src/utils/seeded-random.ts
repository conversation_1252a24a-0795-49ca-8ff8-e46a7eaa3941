import crypto from 'crypto';

import seedrandom from 'seedrandom';

let rng = seedrandom();

export let seededRandom: () => number = () => {
  return rng();
};

/**
 * Generates a random integer between min (inclusive) and max (inclusive)
 * using the seeded random number generator.
 *
 * @param min - The minimum integer value (inclusive).
 * @param max - The maximum integer value (inclusive).
 * @returns A random integer between min and max.
 */
export function seededRandomIntInRange(min: number, max: number): number {
  return Math.floor(rng() * (max - min + 1)) + min;
}

/**
 * Generates a random floating point number between min (inclusive) and max (exclusive)
 * using the seeded random number generator.
 *
 * @param min - The minimum value (inclusive).
 * @param max - The maximum value (exclusive).
 * @returns A random floating point number between min (inclusive) and max (exclusive).
 */
export function seededRandomFloatInRange(min: number, max: number): number {
  return rng() * (max - min) + min;
}

export function setAndReturnSeededRandom() {
  const seed = Number(crypto.randomBytes(8).readBigUInt64BE() % BigInt(Number.MAX_SAFE_INTEGER));
  setRandomSeed(seed);
  return seed;
}

// Generates a seeded random number generator function to ensure reproducible results
export function setRandomSeed(seed: number): void {
  // Use seedrandom to create a seeded RNG
  rng = seedrandom(seed.toString());
}
