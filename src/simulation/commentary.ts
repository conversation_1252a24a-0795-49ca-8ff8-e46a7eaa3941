import { readFileSync } from 'fs';
import { join } from 'path';
//import { fileURLToPath } from 'url';
//import { dirname } from 'path';

//const __filename = fileURLToPath(import.meta.url);
//const __dirname = dirname(__filename);

export class CommentaryManager {
  private static instance: CommentaryManager;
  private commentaryMap: Map<string, string>;

  private constructor() {
    this.commentaryMap = new Map();
    this.loadCommentary();
  }

  public static getInstance(): CommentaryManager {
    if (!CommentaryManager.instance) {
      CommentaryManager.instance = new CommentaryManager();
    }
    return CommentaryManager.instance;
  }

  private loadCommentary() {
    const csv = readFileSync(join(__dirname, 'commentary.csv'), 'utf8');
    const lines = csv.split('\n');

    // Skip header row
    for (let i = 1; i < lines.length; i++) {
      const line = lines[i]!.trim();
      if (line) {
        // Split on first comma only, as commentary text might contain commas
        const [id, text] = line.split(',', 2);
        if (id && text) {
          this.commentaryMap.set(id.trim(), text.trim());
        }
      }
    }
  }

  public getText(id: string, substitutions?: Record<string, string | undefined>): string {
    const text = this.commentaryMap.get(id);
    if (!text) {
      return id; // Return ID if text not found
    }

    if (!substitutions) {
      return text;
    }

    // Replace all placeholders with their values
    return text.replace(/\{(\w+)\}/g, (match: string, key: string) =>
      substitutions[key] !== undefined ? substitutions[key] : match
    );
  }
}

// Usage example:
// const commentary = CommentaryManager.getInstance();
// const text = commentary.getText('PASS_FORWARD_1', { player: 'John', nextPlayer: 'Mike' });
