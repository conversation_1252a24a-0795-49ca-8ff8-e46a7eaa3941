import { InjuryManager } from '@/simulation/managers/injury-manager.js';
import { MatchStateManager } from '@/simulation/managers/match-state-manager.js';
import { Possibility } from '@/simulation/match-events.js';
import { EventProcessor } from '@/simulation/processors/event-processor.js';
import { StatsCalculator } from '@/simulation/stats-calculator.js';
import { MatchArea } from '@/simulation/types.js';
import { TeamBuilder } from '@/simulation/utils/team-builder.js';
import { setRandomSeed } from '@/utils/seeded-random.js';
import { beforeEach, describe, expect, it, vi } from 'vitest';

describe('EventProcessor', () => {
  let eventProcessor: EventProcessor;
  let matchStateManager: MatchStateManager;
  let injuryManager: InjuryManager;
  let statsCalculator: StatsCalculator;
  let homeTeam: any;
  let awayTeam: any;
  let teams: any[];

  beforeEach(() => {
    setRandomSeed(12345);
    homeTeam = TeamBuilder.createStrongTeam('home-1', 'Home FC', 'gameworld-1');
    awayTeam = TeamBuilder.createStrongTeam('away-1', 'Away United', 'gameworld-1');
    teams = [homeTeam, awayTeam];

    matchStateManager = new MatchStateManager(homeTeam, awayTeam);
    statsCalculator = new StatsCalculator();
    injuryManager = new InjuryManager(matchStateManager, {} as any);
    eventProcessor = new EventProcessor(matchStateManager, injuryManager, statsCalculator);

    // Mock dependencies
    vi.spyOn(matchStateManager, 'getCurrentMinute').mockReturnValue(30);
    vi.spyOn(matchStateManager, 'getCurrentHalf').mockReturnValue(1);
    vi.spyOn(matchStateManager, 'addMatchEvent').mockImplementation(() => {});
    vi.spyOn(matchStateManager, 'getKickOffTaker').mockReturnValue(homeTeam.players[5]);
    vi.spyOn(injuryManager, 'checkForInjury').mockReturnValue(null);
    vi.spyOn(injuryManager, 'handleInjury').mockImplementation(() => {});
    vi.spyOn(statsCalculator, 'goalScored').mockReturnValue([1, 0]);
  });

  describe('success threshold calculation', () => {
    it('should calculate base threshold of 0.5 for equal players', () => {
      const event: Possibility = {
        description: 'Pass',
        commentaryId: 'PASS',
        targetArea: MatchArea.MIDFIELD,
        probability: 0.5,
        positiveAttributes: ['passing'],
        negativeAttributes: ['marking'],
      };

      // Create players with equal attributes
      const player1 = homeTeam.players[5];
      const player2 = awayTeam.players[5];
      player1.player.attributes.passingCurrent = 20;
      player2.player.attributes.markingCurrent = 20;

      const threshold = eventProcessor.calculateSuccessThreshold(event, player1, player2);
      expect(threshold).toBeCloseTo(0.5, 1);
    });

    it('should make shots harder to convert', () => {
      const shotEvent: Possibility = {
        description: 'Shot',
        commentaryId: 'SHOT',
        targetArea: MatchArea.SHOT,
        probability: 0.3,
        positiveAttributes: ['finishing'],
        negativeAttributes: ['positioning'],
      };

      const player1 = homeTeam.players[9];
      const player2 = awayTeam.players[0];

      const threshold = eventProcessor.calculateSuccessThreshold(shotEvent, player1, player2);
      expect(threshold).toBeGreaterThan(0.5); // Should be harder than base
    });

    it('should favor better players', () => {
      setRandomSeed(12345);
      const homeTeam = TeamBuilder.createStrongTeam('home-1', 'Home FC', 'gameworld-1');
      const awayTeam = TeamBuilder.createWeakTeam('away-1', 'Away United', 'gameworld-1');

      const event: Possibility = {
        description: 'Pass',
        commentaryId: 'PASS',
        targetArea: MatchArea.MIDFIELD,
        probability: 0.5,
        positiveAttributes: ['passing'],
        negativeAttributes: ['marking'],
      };

      const strongPlayer = homeTeam.players[5]!;
      const weakPlayer = awayTeam.players[5]!;
      strongPlayer.player.attributes.passingCurrent = 35;
      weakPlayer.player.attributes.markingCurrent = 10;

      const threshold = eventProcessor.calculateSuccessThreshold(event, strongPlayer, weakPlayer);
      expect(threshold).toBeLessThan(0.5); // Should be easier for strong player
    });
  });

  describe('event success processing', () => {
    it('should handle goal scoring correctly', () => {
      const shotEvent: Possibility = {
        description: 'Shot',
        commentaryId: 'SHOT',
        targetArea: MatchArea.SHOT,
        probability: 0.3,
        positiveAttributes: ['finishing'],
        negativeAttributes: ['positioning'],
      };

      const scorer = homeTeam.players[9];
      const goalkeeper = awayTeam.players[0];
      const possibleTargetPlayers = [scorer];

      const result = eventProcessor.processEventSuccess(
        shotEvent,
        teams,
        scorer,
        goalkeeper,
        0, // home team
        possibleTargetPlayers
      );

      expect(result.matchArea).toBe(MatchArea.KICK_OFF);
      expect(result.teamInPossession).toBe(1); // Away team kicks off after goal
      expect(scorer.goals).toContain(30); // Goal scored at minute 30
      expect(matchStateManager.addMatchEvent).toHaveBeenCalledWith('GOAL', expect.any(Object));
    });

    it('should handle non-goal events correctly', () => {
      const passEvent: Possibility = {
        description: 'Pass',
        commentaryId: 'PASS',
        targetArea: MatchArea.ATTACK,
        probability: 0.6,
        positiveAttributes: ['passing'],
        negativeAttributes: ['marking'],
      };

      const passer = homeTeam.players[5];
      const defender = awayTeam.players[3];
      const possibleTargetPlayers = [homeTeam.players[9]];

      const result = eventProcessor.processEventSuccess(
        passEvent,
        teams,
        passer,
        defender,
        0,
        possibleTargetPlayers
      );

      expect(result.matchArea).toBe(MatchArea.ATTACK);
      expect(result.teamInPossession).toBe(0); // Same team keeps possession
      expect(result.playerInPossession).toBe(homeTeam.players[9]);
    });

    it('should adjust player ratings on success', () => {
      const event: Possibility = {
        description: 'Pass',
        commentaryId: 'PASS',
        targetArea: MatchArea.ATTACK,
        probability: 0.6,
        positiveAttributes: ['passing'],
        negativeAttributes: ['marking'],
      };

      const player1 = homeTeam.players[5];
      const player2 = awayTeam.players[3];
      const initialRating1 = player1.rating || 6;
      const initialRating2 = player2.rating || 6;

      eventProcessor.processEventSuccess(event, teams, player1, player2, 0, [homeTeam.players[9]]);

      expect(player1.rating).toBeGreaterThan(initialRating1);
      expect(player2.rating).toBeLessThan(initialRating2);
    });
  });

  describe('event failure processing', () => {
    it('should handle basic event failure', () => {
      const event: Possibility = {
        description: 'Pass',
        commentaryId: 'PASS',
        targetArea: MatchArea.ATTACK,
        probability: 0.6,
        positiveAttributes: ['passing'],
        negativeAttributes: ['marking'],
        negativeOutcomes: [
          {
            description: 'Intercepted',
            commentaryId: 'INTERCEPTED',
            probability: 1.0,
          },
        ],
      };

      const passer = homeTeam.players[5];
      const defender = awayTeam.players[3];
      const targetPlayer = homeTeam.players[9];

      const result = eventProcessor.processEventFailure(
        passer,
        defender,
        targetPlayer,
        event,
        0,
        teams,
        [targetPlayer]
      );

      expect(result.matchEvent.teamInPossession).toBe(1); // Possession changes
      expect(result.matchEvent.playerInPossession).toBe(defender);
      expect(result.negativeOutcome.description).toBe('Intercepted');
    });

    it('should handle fouls with correct target areas', () => {
      const event: Possibility = {
        description: 'Tackle',
        commentaryId: 'TACKLE',
        targetArea: MatchArea.DEFENCE,
        probability: 0.5,
        positiveAttributes: ['tackling'],
        negativeAttributes: ['ballControl'],
        negativeOutcomes: [
          {
            description: 'Foul committed',
            commentaryId: 'FOUL',
            probability: 1.0,
          },
        ],
      };

      const attacker = homeTeam.players[9];
      const defender = awayTeam.players[2];
      const targetPlayer = homeTeam.players[5];

      const result = eventProcessor.processEventFailure(
        attacker,
        defender,
        targetPlayer,
        event,
        0,
        teams,
        [targetPlayer]
      );

      expect(result.negativeOutcome.targetArea).toBe(MatchArea.DEFENDING_FREE_KICK);
    });

    it('should handle injury checks', () => {
      vi.mocked(injuryManager.checkForInjury).mockReturnValue(homeTeam.players[5]);

      const event: Possibility = {
        description: 'Tackle',
        commentaryId: 'TACKLE',
        targetArea: MatchArea.MIDFIELD,
        probability: 0.5,
        positiveAttributes: ['tackling'],
        negativeAttributes: ['ballControl'],
        negativeOutcomes: [
          {
            description: 'Hard tackle',
            commentaryId: 'HARD_TACKLE',
            probability: 1.0,
          },
        ],
      };

      const player1 = homeTeam.players[5];
      const player2 = awayTeam.players[3];

      eventProcessor.processEventFailure(player1, player2, player1, event, 0, teams, [player1]);

      expect(injuryManager.handleInjury).toHaveBeenCalledWith(homeTeam, homeTeam.players[5]);
    });

    it('should handle red card situations', () => {
      const event: Possibility = {
        description: 'Tackle',
        commentaryId: 'TACKLE',
        targetArea: MatchArea.MIDFIELD,
        probability: 0.5,
        positiveAttributes: ['tackling'],
        negativeAttributes: ['ballControl'],
        negativeOutcomes: [
          {
            description: 'Foul',
            commentaryId: 'FOUL',
            probability: 1.0,
          },
        ],
      };

      const player1 = homeTeam.players[5];
      const player2 = awayTeam.players[3];
      player2.stats.yellowCards = 2; // Should trigger red card

      eventProcessor.processEventFailure(player1, player2, player1, event, 0, teams, [player1]);

      expect(player2.stats.redCards).toBe(1);
      expect(player2.sentOff).toBe(30); // Current minute
    });

    it('should handle possession retention', () => {
      const event: Possibility = {
        description: 'Shot',
        commentaryId: 'SHOT',
        targetArea: MatchArea.SHOT,
        probability: 0.3,
        positiveAttributes: ['finishing'],
        negativeAttributes: ['positioning'],
        negativeOutcomes: [
          {
            description: 'Saved',
            commentaryId: 'SAVED',
            probability: 1.0,
            noPossessionLost: true,
          },
        ],
      };

      const shooter = homeTeam.players[9];
      const goalkeeper = awayTeam.players[0];

      const result = eventProcessor.processEventFailure(
        shooter,
        goalkeeper,
        shooter,
        event,
        0,
        teams,
        [shooter]
      );

      expect(result.matchEvent.teamInPossession).toBe(0); // Same team keeps possession
      expect(result.matchEvent.playerInPossession).toBe(null);
    });
  });
});
