import { NegativeOutcome } from '@/simulation/event-outcomes.js';
import { Possibility } from '@/simulation/match-events.js';
import { calculatePlayerAdvantage } from '@/simulation/player-attributes.js';
import { StatsCalculator } from '@/simulation/stats-calculator.js';
import {
  seededRandom,
  seededRandomFloatInRange,
  seededRandomIntInRange,
} from '@/utils/seeded-random.js';
import { InjuryManager } from '../managers/injury-manager.js';
import { MatchStateManager } from '../managers/match-state-manager.js';
import { GamePlayer, MatchArea, NextEvent, Team } from '../types.js';

/**
 * Processes individual match events and determines outcomes
 */
export class EventProcessor {
  constructor(
    private matchStateManager: MatchStateManager,
    private injuryManager: InjuryManager,
    private statsCalculator: StatsCalculator
  ) {}

  /**
   * Process a successful event
   */
  processEventSuccess(
    event: Possibility,
    teams: Team[],
    playerInPossession: GamePlayer,
    oppositionPlayer: GamePlayer,
    teamInPossession: number,
    possibleTargetPlayers: GamePlayer[]
  ): NextEvent {
    if (event.targetArea === MatchArea.SHOT) {
      return this.processGoal(event, teams, playerInPossession, oppositionPlayer, teamInPossession);
    }

    // For non-shot events, pick a new player in the target area
    this.adjustPlayerRating(playerInPossession, 0.2);
    this.adjustPlayerRating(oppositionPlayer, -0.1);

    return {
      matchArea: event.targetArea,
      teamInPossession: teamInPossession,
      playerInPossession:
        possibleTargetPlayers[seededRandomIntInRange(0, possibleTargetPlayers.length - 1)]!,
    };
  }

  /**
   * Process a goal being scored
   */
  private processGoal(
    event: Possibility,
    teams: Team[],
    playerInPossession: GamePlayer,
    oppositionPlayer: GamePlayer,
    teamInPossession: number
  ): NextEvent {
    const score = this.statsCalculator.goalScored(
      teamInPossession,
      playerInPossession.player.playerId,
      playerInPossession.player.surname,
      this.matchStateManager.getCurrentMinute(),
      this.matchStateManager.getCurrentHalf()
    );

    playerInPossession.goals ??= [];
    playerInPossession.goals.push(
      this.matchStateManager.getCurrentMinute() + (this.matchStateManager.getCurrentHalf() - 1) * 45
    );

    // Goals have a disproportionate effect on player rating
    this.adjustPlayerRating(playerInPossession, 1);
    this.adjustPlayerRating(oppositionPlayer, -1);

    this.matchStateManager.addMatchEvent('GOAL', {
      team: teams[teamInPossession]!.teamId,
      oppTeam: teams[1 - teamInPossession]!.teamId,
      homeTeam: teams[0]!.teamId,
      awayTeam: teams[1]!.teamId,
      player: playerInPossession.player.playerId,
      homeScore: score[0]?.toString(),
      awayScore: score[1]?.toString(),
    });

    return {
      matchArea: MatchArea.KICK_OFF,
      teamInPossession: 1 - teamInPossession, // Other team kicks off after a goal
      playerInPossession: this.matchStateManager.getKickOffTaker(1 - teamInPossession),
    };
  }

  /**
   * Process a failed event
   */
  processEventFailure(
    playerInPossession: GamePlayer,
    oppositionPlayer: GamePlayer,
    nextPlayer: GamePlayer,
    event: Possibility,
    teamInPossession: number,
    teams: Team[],
    possibleTargetPlayers: GamePlayer[]
  ): { matchEvent: NextEvent; negativeOutcome: NegativeOutcome } {
    // Calculate a random negative outcome
    const randomValue = seededRandom();
    const negativeOutcomes = event.negativeOutcomes || [];
    const negativeOutcome = negativeOutcomes.find((_o, index, arr) => {
      const cumulativeProbability = arr
        .slice(0, index + 1)
        .reduce((acc, curr) => acc + curr.probability, 0);
      return randomValue < cumulativeProbability;
    });

    if (!negativeOutcome) {
      throw new Error('Negative outcome not found');
    }

    // Handle fouls and set appropriate target areas
    if (negativeOutcome.description.toLowerCase().includes('foul')) {
      this.setFoulTargetArea(negativeOutcome, event.targetArea);
    }

    this.matchStateManager.addMatchEvent(negativeOutcome.commentaryId, {
      player: playerInPossession.player.playerId,
      oppPlayer: oppositionPlayer.player.playerId,
      nextPlayer: nextPlayer ? nextPlayer.player.playerId : undefined,
      team: teamInPossession === 0 ? teams[0]!.teamId : teams[1]!.teamId,
      oppTeam: teamInPossession === 0 ? teams[1]!.teamId : teams[0]!.teamId,
    });

    // Check for potential injury
    const injuredPlayer = this.injuryManager.checkForInjury(
      playerInPossession,
      oppositionPlayer,
      negativeOutcome
    );

    if (injuredPlayer) {
      const injuredTeamIndex =
        injuredPlayer === playerInPossession ? teamInPossession : 1 - teamInPossession;
      const injuredTeam = injuredTeamIndex === 0 ? teams[0]! : teams[1]!;
      this.injuryManager.handleInjury(injuredTeam, injuredPlayer);
    }

    // Handle red card situations
    if (oppositionPlayer.stats.yellowCards === 2) {
      oppositionPlayer.stats.redCards++;
      this.adjustPlayerRating(oppositionPlayer, -1);
      oppositionPlayer.sentOff =
        this.matchStateManager.getCurrentMinute() +
        (this.matchStateManager.getCurrentHalf() - 1) * 45;
      this.statsCalculator.getStats().redCards[1 - teamInPossession]!++;
    }

    if (negativeOutcome?.noPossessionLost) {
      return {
        matchEvent: {
          matchArea: negativeOutcome?.targetArea ?? event.targetArea,
          teamInPossession: teamInPossession,
          playerInPossession: null,
        },
        negativeOutcome: negativeOutcome,
      };
    }

    this.adjustPlayerRating(playerInPossession, -0.1);
    if (event.targetArea === MatchArea.SHOT) {
      this.adjustPlayerRating(oppositionPlayer, 0.5);
    } else {
      this.adjustPlayerRating(oppositionPlayer, 0.2);
    }

    return {
      matchEvent: {
        matchArea:
          negativeOutcome?.targetArea ??
          this.matchStateManager.getOppositionAreaFromMatchArea(event.targetArea),
        teamInPossession: 1 - teamInPossession,
        playerInPossession: oppositionPlayer,
      },
      negativeOutcome: negativeOutcome,
    };
  }

  /**
   * Calculate success threshold for an event
   */
  calculateSuccessThreshold(
    event: Possibility,
    playerInPossession: GamePlayer,
    oppositionPlayer: GamePlayer
  ): number {
    const playerAdvantage = calculatePlayerAdvantage(event, playerInPossession, oppositionPlayer);

    // Base threshold starts at 0.5 (50% chance)
    // Player advantage can only influence up to ±0.7 (70% of the outcome)
    const PLAYER_INFLUENCE = 0.7;
    const maxAttributeDiff = 40;
    let successThreshold = 0.5 - (playerAdvantage / maxAttributeDiff) * PLAYER_INFLUENCE;

    // Make shots harder to convert
    if (event.targetArea === MatchArea.SHOT) {
      successThreshold += seededRandomFloatInRange(0.15, 0.3); // Add 15-30% more difficulty for shots
    }

    return Math.max(0, Math.min(0.995, successThreshold));
  }

  /**
   * Set the target area for fouls based on the current match area
   */
  private setFoulTargetArea(negativeOutcome: NegativeOutcome, currentArea: MatchArea): void {
    switch (currentArea) {
      case MatchArea.DEFENCE:
        negativeOutcome.targetArea = MatchArea.DEFENDING_FREE_KICK;
        break;
      case MatchArea.MIDFIELD:
        negativeOutcome.targetArea = MatchArea.MIDFIELD_FREE_KICK;
        break;
      case MatchArea.ATTACK:
        negativeOutcome.targetArea = MatchArea.ATTACKING_FREE_KICK;
        break;
    }
  }

  /**
   * Adjust a player's match rating
   */
  private adjustPlayerRating(player: GamePlayer, amount: number): void {
    player.rating = Math.min(10, Math.max(1, (player.rating || 6) + amount));
  }
}
