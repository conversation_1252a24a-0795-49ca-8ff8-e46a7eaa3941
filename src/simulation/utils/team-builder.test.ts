import { TeamBuilder } from '@/simulation/utils/team-builder.js';
import { PlayerFactory } from '@/testing/factories/playerFactory.js';
import { TeamsFactory } from '@/testing/factories/teamFactory.js';
import { beforeEach, describe, expect, it } from 'vitest';

describe('TeamBuilder', () => {
  describe('buildGameTeam', () => {
    it('should convert entity team to game team', () => {
      const entityTeam = TeamsFactory.build();
      const players = Array.from({ length: 16 }, () => PlayerFactory.build());

      const gameTeam = TeamBuilder.buildGameTeam(entityTeam, players);

      expect(gameTeam.teamId).toBe(entityTeam.teamId);
      expect(gameTeam.teamName).toBe(entityTeam.teamName);
      expect(gameTeam.gameworldId).toBe(entityTeam.gameworldId);
      expect(gameTeam.players).toHaveLength(16);
      expect(gameTeam.standings.played).toBe(entityTeam.played);
      expect(gameTeam.standings.points).toBe(entityTeam.points);
    });

    it('should create game players with correct structure', () => {
      const entityTeam = TeamsFactory.build();
      const players = [PlayerFactory.build()];

      const gameTeam = TeamBuilder.buildGameTeam(entityTeam, players);
      const gamePlayer = gameTeam.players[0];

      expect(gamePlayer).toHaveProperty('player');
      expect(gamePlayer).toHaveProperty('stats');
      expect(gamePlayer).toHaveProperty('rating');
      expect(gamePlayer!.rating).toBe(6);
    });
  });

  describe('buildGamePlayer', () => {
    it('should convert player entity to game player', () => {
      const player = PlayerFactory.build();

      const gamePlayer = TeamBuilder.buildGamePlayer(player);

      expect(gamePlayer.player).toBe(player);
      expect(gamePlayer.rating).toBe(6);
      expect(gamePlayer.stats).toBeDefined();
    });

    it('should create empty player stats', () => {
      const player = PlayerFactory.build();

      const gamePlayer = TeamBuilder.buildGamePlayer(player);

      expect(gamePlayer.stats.goals).toBe(0);
      expect(gamePlayer.stats.shots).toBe(0);
      expect(gamePlayer.stats.passesAttempted).toBe(0);
      expect(gamePlayer.stats.passesCompleted).toBe(0);
      expect(gamePlayer.stats.tackles).toBe(0);
      expect(gamePlayer.stats.fouls).toBe(0);
      expect(gamePlayer.stats.yellowCards).toBe(0);
      expect(gamePlayer.stats.redCards).toBe(0);
    });
  });

  describe('createStrongTeam', () => {
    it('should create team with 16 players', () => {
      const team = TeamBuilder.createStrongTeam('test-id', 'Test FC', 'gameworld-1');

      expect(team.players).toHaveLength(16);
      expect(team.teamId).toBe('test-id');
      expect(team.teamName).toBe('Test FC');
      expect(team.gameworldId).toBe('gameworld-1');
    });

    it('should create players with high attributes', () => {
      const team = TeamBuilder.createStrongTeam(
        'strong-id',
        'Strong FC',
        'gameworld-1',
        { min: 30, max: 35 }
      );

      const player = team.players[1]; // Skip goalkeeper
      expect(player!.player.attributes.passingCurrent).toBeGreaterThanOrEqual(30);
      expect(player!.player.attributes.passingCurrent).toBeLessThanOrEqual(35);
    });

    it('should make first player a goalkeeper with boosted attributes', () => {
      const team = TeamBuilder.createStrongTeam('test-id', 'Test FC', 'gameworld-1');

      const goalkeeper = team.players[0];
      expect(goalkeeper!.player.attributes.isGoalkeeper).toBe(true);
      expect(goalkeeper!.player.attributes.reflexesCurrent).toBeGreaterThan(20);
      expect(goalkeeper!.player.attributes.positioningCurrent).toBeGreaterThan(20);
      expect(goalkeeper!.player.attributes.shotStoppingCurrent).toBeGreaterThan(20);
    });

    it('should create team with correct standings structure', () => {
      const team = TeamBuilder.createStrongTeam('test-id', 'Test FC', 'gameworld-1');

      expect(team.standings).toEqual({
        played: 0,
        points: 0,
        goalsFor: 0,
        goalsAgainst: 0,
        wins: 0,
        draws: 0,
        losses: 0,
      });
    });
  });

  describe('createWeakTeam', () => {
    it('should create team with low attributes', () => {
      const team = TeamBuilder.createWeakTeam(
        'weak-id',
        'Weak FC',
        'gameworld-1',
        { min: 5, max: 10 }
      );

      const player = team.players[1]; // Skip goalkeeper
      expect(player!.player.attributes.passingCurrent).toBeGreaterThanOrEqual(5);
      expect(player!.player.attributes.passingCurrent).toBeLessThanOrEqual(10);
    });

    it('should still have 16 players', () => {
      const team = TeamBuilder.createWeakTeam('weak-id', 'Weak FC', 'gameworld-1');

      expect(team.players).toHaveLength(16);
    });
  });

  describe('createEmptyPlayerStats', () => {
    it('should create stats with all zeros', () => {
      const stats = TeamBuilder.createEmptyPlayerStats();

      expect(stats.shotsOnTarget).toBe(0);
      expect(stats.goals).toBe(0);
      expect(stats.saves).toBe(0);
      expect(stats.tackles).toBe(0);
      expect(stats.fouls).toBe(0);
      expect(stats.yellowCards).toBe(0);
      expect(stats.redCards).toBe(0);
      expect(stats.shots).toBe(0);
      expect(stats.passesAttempted).toBe(0);
      expect(stats.passesCompleted).toBe(0);
      expect(stats.ballCarriesAttempted).toBe(0);
      expect(stats.successfulBallCarries).toBe(0);
    });
  });

  describe('attribute generation', () => {
    it('should generate attributes within specified range', () => {
      const strongTeam = TeamBuilder.createStrongTeam(
        'test-id',
        'Test FC',
        'gameworld-1',
        { min: 20, max: 25 }
      );

      const player = strongTeam.players[5]; // Midfielder
      const attributes = player!.player.attributes;

      // Check several attributes are within range
      expect(attributes.passingCurrent).toBeGreaterThanOrEqual(20);
      expect(attributes.passingCurrent).toBeLessThanOrEqual(25);
      expect(attributes.ballControlCurrent).toBeGreaterThanOrEqual(20);
      expect(attributes.ballControlCurrent).toBeLessThanOrEqual(25);
      expect(attributes.tacklingCurrent).toBeGreaterThanOrEqual(20);
      expect(attributes.tacklingCurrent).toBeLessThanOrEqual(25);
    });

    it('should set potential attributes to maximum', () => {
      const team = TeamBuilder.createStrongTeam(
        'test-id',
        'Test FC',
        'gameworld-1',
        { min: 15, max: 20 }
      );

      const player = team.players[3];
      const attributes = player!.player.attributes;

      expect(attributes.passingPotential).toBe(20);
      expect(attributes.tacklingPotential).toBe(20);
      expect(attributes.finishingPotential).toBe(20);
    });

    it('should set stamina to reasonable value', () => {
      const team = TeamBuilder.createStrongTeam('test-id', 'Test FC', 'gameworld-1');

      const player = team.players[2];
      expect(player!.player.attributes.stamina).toBe(0.8);
    });
  });
});
