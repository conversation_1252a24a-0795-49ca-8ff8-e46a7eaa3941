import { Player } from '@/entities/Player.js';
import { Team as EntityTeam } from '@/entities/Team.js';
import { PlayerMatchStats } from '@/model/player.js';
import { seededRandomIntInRange } from '@/utils/seeded-random.js';
import { GamePlayer, Team } from '../types.js';

/**
 * Utility class for building game teams from database entities
 */
export class TeamBuilder {
  /**
   * Convert a database team entity to a game team
   */
  static buildGameTeam(entityTeam: EntityTeam, players: Player[]): Team {
    const gamePlayers = players.map((player) => this.buildGamePlayer(player));

    return {
      gameworldId: entityTeam.gameworldId,
      teamId: entityTeam.teamId,
      teamName: entityTeam.teamName,
      standings: {
        played: entityTeam.played,
        points: entityTeam.points,
        goalsFor: entityTeam.goalsFor,
        goalsAgainst: entityTeam.goalsAgainst,
        wins: entityTeam.wins,
        draws: entityTeam.draws,
        losses: entityTeam.losses,
      },
      players: gamePlayers,
    };
  }

  /**
   * Convert a database player entity to a game player
   */
  static buildGamePlayer(player: Player): GamePlayer {
    return {
      player: player,
      stats: this.createEmptyPlayerStats(),
      rating: 6, // Default rating
    };
  }

  /**
   * Create empty player match statistics
   */
  static createEmptyPlayerStats(): PlayerMatchStats {
    return {
      shotsOnTarget: 0,
      goals: 0,
      saves: 0,
      tackles: 0,
      fouls: 0,
      yellowCards: 0,
      redCards: 0,
      shots: 0,
      passesAttempted: 0,
      passesCompleted: 0,
      ballCarriesAttempted: 0,
      successfulBallCarries: 0,
    };
  }

  /**
   * Create a strong team for testing (high attributes)
   */
  static createStrongTeam(
    teamId: string,
    teamName: string,
    gameworldId: string,
    attributeRange: { min: number; max: number } = { min: 25, max: 35 }
  ): Team {
    const players: GamePlayer[] = [];

    // Create 16 players (11 starters + 5 subs)
    for (let i = 0; i < 16; i++) {
      const player = this.createPlayerWithAttributes(
        `${teamId}-player-${i}`,
        `Player${i}`,
        `Surname${i}`,
        gameworldId,
        attributeRange,
        i === 0 // First player is goalkeeper
      );

      players.push(this.buildGamePlayer(player));
    }

    return {
      gameworldId,
      teamId,
      teamName,
      standings: {
        played: 0,
        points: 0,
        goalsFor: 0,
        goalsAgainst: 0,
        wins: 0,
        draws: 0,
        losses: 0,
      },
      players,
    };
  }

  /**
   * Create a weak team for testing (low attributes)
   */
  static createWeakTeam(
    teamId: string,
    teamName: string,
    gameworldId: string,
    attributeRange: { min: number; max: number } = { min: 5, max: 15 }
  ): Team {
    return this.createStrongTeam(teamId, teamName, gameworldId, attributeRange);
  }

  /**
   * Create a player with specific attribute ranges
   */
  private static createPlayerWithAttributes(
    playerId: string,
    firstName: string,
    surname: string,
    gameworldId: string,
    attributeRange: { min: number; max: number },
    isGoalkeeper: boolean = false
  ): Player {
    const baseAttributes = this.generateAttributes(attributeRange);

    // Boost goalkeeper-specific attributes if this is a goalkeeper
    if (isGoalkeeper) {
      baseAttributes.reflexesCurrent = Math.max(
        baseAttributes.reflexesCurrent,
        attributeRange.max - 5
      );
      baseAttributes.positioningCurrent = Math.max(
        baseAttributes.positioningCurrent,
        attributeRange.max - 5
      );
      baseAttributes.shotStoppingCurrent = Math.max(
        baseAttributes.shotStoppingCurrent,
        attributeRange.max - 5
      );
    }

    // Create a mock player with attributes
    const player = {
      playerId,
      gameworldId,
      firstName,
      surname,
      age: 25,
      seed: seededRandomIntInRange(0, 1000000),
      value: 1000000,
      energy: 100,
      lastMatchPlayed: 0,
      suspendedForGames: 0,
      injuredUntil: 0,
      isTransferListed: false,
      retiringAtEndOfSeason: false,
    } as any;

    // Add attributes as a property for testing
    player.attributes = {
      ...baseAttributes,
      isGoalkeeper,
      stamina: 0.8,
    };

    return player;
  }

  /**
   * Generate random attributes within a range
   */
  private static generateAttributes(range: { min: number; max: number }) {
    const randomInRange = () => seededRandomIntInRange(range.min, range.max);
    // const randomInRange = () => Math.floor(Math.random() * (range.max - range.min + 1)) + range.min;

    return {
      reflexesCurrent: randomInRange(),
      reflexesPotential: range.max,
      positioningCurrent: randomInRange(),
      positioningPotential: range.max,
      shotStoppingCurrent: randomInRange(),
      shotStoppingPotential: range.max,
      tacklingCurrent: randomInRange(),
      tacklingPotential: range.max,
      markingCurrent: randomInRange(),
      markingPotential: range.max,
      headingCurrent: randomInRange(),
      headingPotential: range.max,
      finishingCurrent: randomInRange(),
      finishingPotential: range.max,
      paceCurrent: randomInRange(),
      pacePotential: range.max,
      crossingCurrent: randomInRange(),
      crossingPotential: range.max,
      passingCurrent: randomInRange(),
      passingPotential: range.max,
      visionCurrent: randomInRange(),
      visionPotential: range.max,
      ballControlCurrent: randomInRange(),
      ballControlPotential: range.max,
    };
  }
}
