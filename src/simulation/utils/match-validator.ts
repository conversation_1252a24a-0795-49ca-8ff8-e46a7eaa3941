import { MatchStats } from '@/model/fixture.js';
import { SimulationResult, Team } from '../types.js';

/**
 * Validation results for match simulation
 */
export interface MatchValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

/**
 * Expected match parameters for validation
 */
export interface MatchExpectations {
  maxSubstitutions?: number;
  maxRedCards?: number;
  maxYellowCards?: number;
  maxGoals?: number;
  minPossessionPercentage?: number;
  maxPossessionPercentage?: number;
}

/**
 * Utility class for validating match simulation results
 */
export class MatchValidator {
  private static readonly DEFAULT_EXPECTATIONS: Required<MatchExpectations> = {
    maxSubstitutions: 3,
    maxRedCards: 3,
    maxYellowCards: 8,
    maxGoals: 10,
    minPossessionPercentage: 20,
    maxPossessionPercentage: 80,
  };

  /**
   * Validate a complete match simulation result
   */
  static validateMatch(
    result: SimulationResult,
    homeTeam: Team,
    awayTeam: Team,
    expectations: MatchExpectations = {}
  ): MatchValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];
    const exp = { ...this.DEFAULT_EXPECTATIONS, ...expectations };

    // Validate basic match structure
    this.validateBasicStructure(result, errors);

    // Validate statistics
    this.validateStatistics(result.stats, exp, errors, warnings);

    // Validate team-specific data
    this.validateTeamData(homeTeam, awayTeam, exp, errors, warnings);

    // Validate events
    this.validateEvents(result, errors, warnings);

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }

  /**
   * Validate basic match structure
   */
  private static validateBasicStructure(result: SimulationResult, errors: string[]): void {
    if (!result.stats) {
      errors.push('Match stats are missing');
      return;
    }

    if (!result.events) {
      errors.push('Match events are missing');
      return;
    }

    if (!Array.isArray(result.stats.score) || result.stats.score.length !== 2) {
      errors.push('Invalid score format');
    }

    if (result.stats.score.some((score) => score < 0 || !Number.isInteger(score))) {
      errors.push('Invalid score values');
    }
  }

  /**
   * Validate match statistics
   */
  private static validateStatistics(
    stats: MatchStats,
    expectations: Required<MatchExpectations>,
    errors: string[],
    warnings: string[]
  ): void {
    if (!stats) {
      errors.push('Match stats are missing');
      return;
    }
    // Validate score
    if (stats.score[0] > expectations.maxGoals || stats.score[1] > expectations.maxGoals) {
      warnings.push(`Unusually high score: ${stats.score[0]}-${stats.score[1]}`);
    }

    // Validate cards
    if (
      stats.redCards[0] > expectations.maxRedCards ||
      stats.redCards[1] > expectations.maxRedCards
    ) {
      warnings.push(`Too many red cards: ${stats.redCards[0]} vs ${stats.redCards[1]}`);
    }

    if (
      stats.yellowCards[0] > expectations.maxYellowCards ||
      stats.yellowCards[1] > expectations.maxYellowCards
    ) {
      warnings.push(`Too many yellow cards: ${stats.yellowCards[0]} vs ${stats.yellowCards[1]}`);
    }

    // Validate possession
    const totalPossession = stats.possession[0] + stats.possession[1];
    if (totalPossession === 0) {
      errors.push('No possession recorded');
      return;
    }

    const homePossessionPercent = (stats.possession[0] / totalPossession) * 100;
    const awayPossessionPercent = (stats.possession[1] / totalPossession) * 100;

    if (
      homePossessionPercent < expectations.minPossessionPercentage ||
      homePossessionPercent > expectations.maxPossessionPercentage
    ) {
      warnings.push(`Unusual home possession: ${homePossessionPercent.toFixed(1)}%`);
    }

    if (
      awayPossessionPercent < expectations.minPossessionPercentage ||
      awayPossessionPercent > expectations.maxPossessionPercentage
    ) {
      warnings.push(`Unusual away possession: ${awayPossessionPercent.toFixed(1)}%`);
    }

    // Validate shots vs goals ratio
    if (stats.score[0] > stats.shots[0] || stats.score[1] > stats.shots[1]) {
      errors.push('More goals than shots recorded');
    }

    if (stats.shotsOnTarget[0] > stats.shots[0] || stats.shotsOnTarget[1] > stats.shots[1]) {
      errors.push('More shots on target than total shots');
    }
  }

  /**
   * Validate team-specific data
   */
  private static validateTeamData(
    homeTeam: Team,
    awayTeam: Team,
    expectations: Required<MatchExpectations>,
    errors: string[],
    warnings: string[]
  ): void {
    [homeTeam, awayTeam].forEach((team, index) => {
      const teamName = index === 0 ? 'Home' : 'Away';

      // Validate substitutions
      const substitutions = team.players.filter((p) => p.leftMatchMinute !== undefined).length;
      if (substitutions > expectations.maxSubstitutions) {
        errors.push(`${teamName} team made too many substitutions: ${substitutions}`);
      }

      // Validate player count
      if (team.players.length < 11) {
        errors.push(`${teamName} team has insufficient players: ${team.players.length}`);
      }

      // Validate player ratings
      const playersWithRatings = team.players.filter((p) => p.rating !== undefined);
      if (playersWithRatings.length === 0) {
        warnings.push(`${teamName} team has no player ratings`);
      }

      // Check for unrealistic ratings
      const extremeRatings = playersWithRatings.filter((p) => p.rating! < 1 || p.rating! > 10);
      if (extremeRatings.length > 0) {
        warnings.push(`${teamName} team has players with extreme ratings`);
      }
    });
  }

  /**
   * Validate match events
   */
  private static validateEvents(
    result: SimulationResult,
    errors: string[],
    warnings: string[]
  ): void {
    if (!result.events || result.events.length === 0) {
      warnings.push('No match events recorded');
      return;
    }

    // Check for required events
    const hasKickOff = result.events.some((e) => e.localisationId === 'KICK_OFF');
    const hasHalfTime = result.events.some((e) => e.localisationId === 'HALF_TIME');
    const hasFullTime = result.events.some((e) => e.localisationId === 'FULL_TIME');

    if (!hasKickOff) warnings.push('No kick-off event found');
    if (!hasHalfTime) warnings.push('No half-time event found');
    if (!hasFullTime) warnings.push('No full-time event found');

    // Validate event timing
    const invalidTiming = result.events.filter(
      (e) => e.minute < 0 || e.minute > 50 || e.half < 1 || e.half > 2
    );

    if (invalidTiming.length > 0) {
      errors.push(`Events with invalid timing: ${invalidTiming.length}`);
    }

    // Check goal events match score
    const goalEvents = result.events.filter((e) => e.localisationId === 'GOAL');
    const totalGoals = result.stats ? result.stats.score[0] + result.stats.score[1] : 0;

    if (goalEvents.length !== totalGoals) {
      errors.push(`Goal events (${goalEvents.length}) don't match total score (${totalGoals})`);
    }
  }

  /**
   * Check if a strong team beat a weak team (for testing team strength)
   */
  static validateTeamStrengthLogic(
    result: SimulationResult,
    strongTeamIndex: number
  ): { strongTeamWon: boolean; scoreDifference: number } {
    const strongTeamScore = result.stats.score[strongTeamIndex]!;
    const weakTeamScore = result.stats.score[1 - strongTeamIndex]!;

    return {
      strongTeamWon: strongTeamScore > weakTeamScore,
      scoreDifference: strongTeamScore - weakTeamScore,
    };
  }
}
