import { SimulationResult } from '@/simulation/types.js';
import { MatchValidator } from '@/simulation/utils/match-validator.js';
import { TeamBuilder } from '@/simulation/utils/team-builder.js';
import { beforeEach, describe, expect, it } from 'vitest';

describe('MatchValidator', () => {
  let homeTeam: any;
  let awayTeam: any;
  let validResult: SimulationResult;

  beforeEach(() => {
    homeTeam = TeamBuilder.createStrongTeam('home-1', 'Home FC', 'gameworld-1');
    awayTeam = TeamBuilder.createStrongTeam('away-1', 'Away United', 'gameworld-1');

    validResult = {
      stats: {
        score: [1, 0],
        possession: [60, 40],
        shots: [10, 8],
        shotsOnTarget: [5, 3],
        corners: [4, 2],
        fouls: [8, 6],
        yellowCards: [2, 1],
        redCards: [0, 0],
        passes: [400, 300],
        passAccuracy: [340, 240],
        tackles: [15, 12],
        interceptions: [8, 6],
        scorers: [
          {
            playerId: 'player1',
            playerName: 'Test Player',
            team: 0,
            goalTime: [{ minute: 25, half: 1 }],
          },
        ],
      },
      events: [
        {
          localisationId: 'KICK_OFF',
          substitutions: {},
          minute: 1,
          half: 1,
        },
        {
          localisationId: 'GOAL',
          substitutions: { player: 'player1' },
          minute: 25,
          half: 1,
        },
        {
          localisationId: 'HALF_TIME',
          substitutions: {},
          minute: 45,
          half: 1,
        },
        {
          localisationId: 'FULL_TIME',
          substitutions: {},
          minute: 45,
          half: 2,
        },
      ],
    };
  });

  describe('basic structure validation', () => {
    it('should validate a correct match result', () => {
      const validation = MatchValidator.validateMatch(validResult, homeTeam, awayTeam);

      expect(validation.isValid).toBe(true);
      expect(validation.errors).toHaveLength(0);
    });

    it('should detect missing stats', () => {
      const invalidResult = { ...validResult, stats: undefined as any };

      const validation = MatchValidator.validateMatch(invalidResult, homeTeam, awayTeam);

      expect(validation.isValid).toBe(false);
      expect(validation.errors).toContain('Match stats are missing');
    });

    it('should detect missing events', () => {
      const invalidResult = { ...validResult, events: undefined as any };

      const validation = MatchValidator.validateMatch(invalidResult, homeTeam, awayTeam);

      expect(validation.isValid).toBe(false);
      expect(validation.errors).toContain('Match events are missing');
    });

    it('should detect invalid score format', () => {
      const invalidResult = {
        ...validResult,
        stats: { ...validResult.stats, score: [2] as any },
      };

      const validation = MatchValidator.validateMatch(invalidResult, homeTeam, awayTeam);

      expect(validation.isValid).toBe(false);
      expect(validation.errors).toContain('Invalid score format');
    });

    it('should detect negative scores', () => {
      const invalidResult = {
        ...validResult,
        stats: { ...validResult.stats, score: [-1, 2] },
      };

      const validation = MatchValidator.validateMatch(invalidResult as any, homeTeam, awayTeam);

      expect(validation.isValid).toBe(false);
      expect(validation.errors).toContain('Invalid score values');
    });
  });

  describe('statistics validation', () => {
    it('should warn about unusually high scores', () => {
      const highScoreResult = {
        ...validResult,
        stats: { ...validResult.stats, score: [12, 8] },
      };

      const validation = MatchValidator.validateMatch(highScoreResult as any, homeTeam, awayTeam);

      expect(validation.warnings.some((w) => w.includes('Unusually high score'))).toBe(true);
    });

    it('should warn about too many cards', () => {
      const highCardsResult = {
        ...validResult,
        stats: { ...validResult.stats, redCards: [4, 2] },
      };

      const validation = MatchValidator.validateMatch(highCardsResult as any, homeTeam, awayTeam);

      expect(validation.warnings.some((w) => w.includes('Too many red cards'))).toBe(true);
    });

    it('should detect more goals than shots', () => {
      const invalidResult = {
        ...validResult,
        stats: { ...validResult.stats, score: [5, 1], shots: [3, 2] },
      };

      const validation = MatchValidator.validateMatch(invalidResult as any, homeTeam, awayTeam);

      expect(validation.isValid).toBe(false);
      expect(validation.errors).toContain('More goals than shots recorded');
    });

    it('should detect more shots on target than total shots', () => {
      const invalidResult = {
        ...validResult,
        stats: { ...validResult.stats, shots: [5, 3], shotsOnTarget: [8, 4] },
      };

      const validation = MatchValidator.validateMatch(invalidResult as any, homeTeam, awayTeam);

      expect(validation.isValid).toBe(false);
      expect(validation.errors).toContain('More shots on target than total shots');
    });

    it('should detect zero possession', () => {
      const invalidResult = {
        ...validResult,
        stats: { ...validResult.stats, possession: [0, 0] },
      };

      const validation = MatchValidator.validateMatch(invalidResult as any, homeTeam, awayTeam);

      expect(validation.isValid).toBe(false);
      expect(validation.errors).toContain('No possession recorded');
    });

    it('should warn about extreme possession percentages', () => {
      const extremePossessionResult = {
        ...validResult,
        stats: { ...validResult.stats, possession: [95, 5] },
      };

      const validation = MatchValidator.validateMatch(
        extremePossessionResult as any,
        homeTeam,
        awayTeam
      );

      expect(validation.warnings.some((w) => w.includes('Unusual home possession'))).toBe(true);
      expect(validation.warnings.some((w) => w.includes('Unusual away possession'))).toBe(true);
    });
  });

  describe('team data validation', () => {
    it('should detect too many substitutions', () => {
      // Mark 4 players as substituted
      for (let i = 0; i < 4; i++) {
        homeTeam.players[i + 1].leftMatchMinute = 30;
      }

      const validation = MatchValidator.validateMatch(validResult, homeTeam, awayTeam);

      expect(validation.isValid).toBe(false);
      expect(validation.errors.some((e) => e.includes('too many substitutions'))).toBe(true);
    });

    it('should detect insufficient players', () => {
      homeTeam.players = homeTeam.players.slice(0, 10); // Only 10 players

      const validation = MatchValidator.validateMatch(validResult, homeTeam, awayTeam);

      expect(validation.isValid).toBe(false);
      expect(validation.errors.some((e) => e.includes('insufficient players'))).toBe(true);
    });

    it('should warn about missing player ratings', () => {
      homeTeam.players.forEach((player: any) => {
        player.rating = undefined;
      });

      const validation = MatchValidator.validateMatch(validResult, homeTeam, awayTeam);

      expect(validation.warnings.some((w) => w.includes('no player ratings'))).toBe(true);
    });

    it('should warn about extreme player ratings', () => {
      homeTeam.players[0].rating = 15; // Invalid rating

      const validation = MatchValidator.validateMatch(validResult, homeTeam, awayTeam);

      expect(validation.warnings.some((w) => w.includes('extreme ratings'))).toBe(true);
    });
  });

  describe('event validation', () => {
    it('should warn about missing required events', () => {
      const noEventsResult = {
        ...validResult,
        events: [
          {
            localisationId: 'SOME_EVENT',
            substitutions: {},
            minute: 1,
            half: 1,
          },
        ],
      };

      const validation = MatchValidator.validateMatch(noEventsResult, homeTeam, awayTeam);

      expect(validation.warnings.some((w) => w.includes('No kick-off event'))).toBe(true);
      expect(validation.warnings.some((w) => w.includes('No half-time event'))).toBe(true);
      expect(validation.warnings.some((w) => w.includes('No full-time event'))).toBe(true);
    });

    it('should detect invalid event timing', () => {
      const invalidTimingResult = {
        ...validResult,
        events: [
          ...validResult.events,
          {
            localisationId: 'INVALID_EVENT',
            substitutions: {},
            minute: -5, // Invalid minute
            half: 3, // Invalid half
          },
        ],
      };

      const validation = MatchValidator.validateMatch(invalidTimingResult, homeTeam, awayTeam);

      expect(validation.isValid).toBe(false);
      expect(validation.errors.some((e) => e.includes('invalid timing'))).toBe(true);
    });

    it('should detect goal event mismatch', () => {
      const mismatchResult = {
        ...validResult,
        stats: { ...validResult.stats, score: [3, 1] }, // 4 total goals
        events: validResult.events, // Only 1 goal event
      };

      const validation = MatchValidator.validateMatch(mismatchResult as any, homeTeam, awayTeam);

      expect(validation.isValid).toBe(false);
      expect(
        validation.errors.some((e) => e.includes('Goal events') && e.includes("don't match"))
      ).toBe(true);
    });
  });

  describe('team strength validation', () => {
    it('should detect when strong team wins', () => {
      const strongWinResult = { ...validResult, stats: { ...validResult.stats, score: [3, 0] } };

      const strengthCheck = MatchValidator.validateTeamStrengthLogic(strongWinResult as any, 0);

      expect(strengthCheck.strongTeamWon).toBe(true);
      expect(strengthCheck.scoreDifference).toBe(3);
    });

    it('should detect when strong team loses', () => {
      const strongLoseResult = { ...validResult, stats: { ...validResult.stats, score: [0, 2] } };

      const strengthCheck = MatchValidator.validateTeamStrengthLogic(strongLoseResult as any, 0);

      expect(strengthCheck.strongTeamWon).toBe(false);
      expect(strengthCheck.scoreDifference).toBe(-2);
    });

    it('should handle draws correctly', () => {
      const drawResult = { ...validResult, stats: { ...validResult.stats, score: [1, 1] } };

      const strengthCheck = MatchValidator.validateTeamStrengthLogic(drawResult as any, 0);

      expect(strengthCheck.strongTeamWon).toBe(false);
      expect(strengthCheck.scoreDifference).toBe(0);
    });
  });

  describe('custom expectations', () => {
    it('should use custom expectations for validation', () => {
      const customExpectations = {
        maxSubstitutions: 1,
        maxGoals: 0,
      };

      const validation = MatchValidator.validateMatch(
        validResult,
        homeTeam,
        awayTeam,
        customExpectations
      );

      expect(validation.warnings.some((w) => w.includes('Unusually high score'))).toBe(true);
    });
  });
});
