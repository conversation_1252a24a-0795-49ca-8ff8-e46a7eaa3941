import { NegativeOutcome } from '@/simulation/event-outcomes.js';
import { seededRandom } from '@/utils/seeded-random.js';
import { GamePlayer, Team } from '../types.js';
import { MatchStateManager } from './match-state-manager.js';
import { SubstitutionManager } from './substitution-manager.js';

/**
 * Manages player injuries during a match
 */
export class InjuryManager {
  constructor(
    private matchStateManager: MatchStateManager,
    private substitutionManager: SubstitutionManager
  ) {}

  /**
   * Check if an injury should occur based on the event and players involved
   */
  checkForInjury(
    playerInPossession: GamePlayer,
    oppositionPlayer: GamePlayer,
    negativeOutcome: NegativeOutcome
  ): GamePlayer | null {
    // Higher chance on fouls, especially from players who already have cards
    let injuryChance = 0.005; // Base chance

    if (negativeOutcome.description.toLowerCase().includes('foul')) {
      injuryChance = 0.01; // Higher chance on fouls

      if (oppositionPlayer.stats.yellowCards > 0) {
        injuryChance = 0.05; // Even higher for reckless players
      }
    }

    if (seededRandom() < injuryChance) {
      // Usually the player receiving the foul gets injured
      return seededRandom() < 0.9 ? playerInPossession : oppositionPlayer;
    }

    return null;
  }

  /**
   * Handle an injury occurrence
   */
  handleInjury(team: Team, injuredPlayer: GamePlayer): void {
    const currentMinute = this.matchStateManager.getCurrentMinute() + 
      (this.matchStateManager.getCurrentHalf() - 1) * 45;
    
    injuredPlayer.injured = currentMinute;

    this.matchStateManager.addMatchEvent('INJURY', {
      team: team.teamId,
      player: injuredPlayer.player.playerId,
    });

    // Try to make a substitution for the injured player
    const substitutionMade = this.substitutionManager.forceSubstitution(team, injuredPlayer);
    
    if (!substitutionMade) {
      // Player has to continue playing while injured
      // This could affect their performance in future events
    }
  }

  /**
   * Check if a player is currently injured
   */
  isPlayerInjured(player: GamePlayer): boolean {
    return player.injured !== undefined;
  }

  /**
   * Get all injured players in a team
   */
  getInjuredPlayers(team: Team): GamePlayer[] {
    return team.players.filter(player => this.isPlayerInjured(player));
  }

  /**
   * Check if a team has any injured players on the field
   */
  hasInjuredPlayersOnField(team: Team): boolean {
    return team.players.slice(0, 11).some(player => this.isPlayerInjured(player));
  }

  /**
   * Calculate injury impact on player performance
   * Returns a multiplier (0.5 = 50% performance, 1.0 = normal performance)
   */
  getInjuryPerformanceMultiplier(player: GamePlayer): number {
    if (!this.isPlayerInjured(player)) {
      return 1.0;
    }
    
    // Injured players perform at reduced capacity
    return 0.7; // 70% of normal performance
  }
}
