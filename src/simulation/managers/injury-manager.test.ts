import { InjuryManager } from '@/simulation/managers/injury-manager.js';
import { MatchStateManager } from '@/simulation/managers/match-state-manager.js';
import { SubstitutionManager } from '@/simulation/managers/substitution-manager.js';
import { TeamBuilder } from '@/simulation/utils/team-builder.js';
import { NegativeOutcome } from '@/simulation/event-outcomes.js';
import { setRandomSeed } from '@/utils/seeded-random.js';
import { beforeEach, describe, expect, it, vi } from 'vitest';

describe('InjuryManager', () => {
  let injuryManager: InjuryManager;
  let matchStateManager: MatchStateManager;
  let substitutionManager: SubstitutionManager;
  let homeTeam: any;
  let awayTeam: any;

  beforeEach(() => {
    homeTeam = TeamBuilder.createStrongTeam('home-1', 'Home FC', 'gameworld-1');
    awayTeam = TeamBuilder.createStrongTeam('away-1', 'Away United', 'gameworld-1');
    matchStateManager = new MatchStateManager(homeTeam, awayTeam);
    substitutionManager = new SubstitutionManager(matchStateManager);
    injuryManager = new InjuryManager(matchStateManager, substitutionManager);

    // Mock the dependencies
    vi.spyOn(matchStateManager, 'getCurrentMinute').mockReturnValue(45);
    vi.spyOn(matchStateManager, 'getCurrentHalf').mockReturnValue(1);
    vi.spyOn(matchStateManager, 'addMatchEvent').mockImplementation(() => {});
    vi.spyOn(substitutionManager, 'forceSubstitution').mockReturnValue(true);
  });

  describe('injury probability', () => {
    it('should have low base injury chance', () => {
      setRandomSeed(12345);
      const playerInPossession = homeTeam.players[5];
      const oppositionPlayer = awayTeam.players[3];
      const negativeOutcome: NegativeOutcome = {
        description: 'Tackle',
        commentaryId: 'TACKLE',
        probability: 0.5,
      };

      // Test multiple times to check low probability
      let injuryCount = 0;
      for (let i = 0; i < 1000; i++) {
        setRandomSeed(i);
        const injured = injuryManager.checkForInjury(playerInPossession, oppositionPlayer, negativeOutcome);
        if (injured) injuryCount++;
      }

      // Should be very low chance (around 0.5% = 5 out of 1000)
      expect(injuryCount).toBeLessThan(20);
      expect(injuryCount).toBeGreaterThanOrEqual(0);
    });

    it('should have higher injury chance on fouls', () => {
      setRandomSeed(12345);
      const playerInPossession = homeTeam.players[5];
      const oppositionPlayer = awayTeam.players[3];
      const foulOutcome: NegativeOutcome = {
        description: 'Bad foul',
        commentaryId: 'FOUL',
        probability: 0.3,
      };

      // Test multiple times
      let injuryCount = 0;
      for (let i = 0; i < 1000; i++) {
        setRandomSeed(i);
        const injured = injuryManager.checkForInjury(playerInPossession, oppositionPlayer, foulOutcome);
        if (injured) injuryCount++;
      }

      // Should be higher than base chance (around 1% = 10 out of 1000)
      expect(injuryCount).toBeGreaterThan(5);
      expect(injuryCount).toBeLessThan(50);
    });

    it('should have very high injury chance for reckless players', () => {
      setRandomSeed(12345);
      const playerInPossession = homeTeam.players[5];
      const oppositionPlayer = awayTeam.players[3];
      oppositionPlayer.stats.yellowCards = 1; // Reckless player
      
      const foulOutcome: NegativeOutcome = {
        description: 'Reckless foul',
        commentaryId: 'RECKLESS_FOUL',
        probability: 0.3,
      };

      // Test multiple times
      let injuryCount = 0;
      for (let i = 0; i < 100; i++) {
        setRandomSeed(i);
        const injured = injuryManager.checkForInjury(playerInPossession, oppositionPlayer, foulOutcome);
        if (injured) injuryCount++;
      }

      // Should be much higher chance (around 5% = 5 out of 100)
      expect(injuryCount).toBeGreaterThan(2);
    });

    it('should usually injure the player in possession', () => {
      setRandomSeed(1); // Seed that causes injury
      const playerInPossession = homeTeam.players[5];
      const oppositionPlayer = awayTeam.players[3];
      const foulOutcome: NegativeOutcome = {
        description: 'Bad foul',
        commentaryId: 'FOUL',
        probability: 0.3,
      };

      // Find a seed that causes injury
      let injuredPlayer = null;
      for (let i = 0; i < 1000; i++) {
        setRandomSeed(i);
        injuredPlayer = injuryManager.checkForInjury(playerInPossession, oppositionPlayer, foulOutcome);
        if (injuredPlayer) break;
      }

      // When injury occurs, it should usually be the player in possession
      if (injuredPlayer) {
        // We can't guarantee which player gets injured due to randomness,
        // but we can check that one of the two players was returned
        expect([playerInPossession, oppositionPlayer]).toContain(injuredPlayer);
      }
    });
  });

  describe('injury handling', () => {
    it('should mark player as injured with correct timing', () => {
      const injuredPlayer = homeTeam.players[2];
      
      injuryManager.handleInjury(homeTeam, injuredPlayer);

      expect(injuredPlayer.injured).toBe(45); // Current minute
      expect(matchStateManager.addMatchEvent).toHaveBeenCalledWith('INJURY', {
        team: homeTeam.teamId,
        player: injuredPlayer.player.playerId,
      });
    });

    it('should attempt substitution for injured player', () => {
      const injuredPlayer = homeTeam.players[2];
      
      injuryManager.handleInjury(homeTeam, injuredPlayer);

      expect(substitutionManager.forceSubstitution).toHaveBeenCalledWith(homeTeam, injuredPlayer);
    });

    it('should handle failed substitution gracefully', () => {
      vi.mocked(substitutionManager.forceSubstitution).mockReturnValue(false);
      const injuredPlayer = homeTeam.players[2];
      
      // Should not throw error
      expect(() => {
        injuryManager.handleInjury(homeTeam, injuredPlayer);
      }).not.toThrow();
    });
  });

  describe('injury status checking', () => {
    it('should identify injured players', () => {
      const player = homeTeam.players[3];
      
      expect(injuryManager.isPlayerInjured(player)).toBe(false);
      
      player.injured = 30;
      expect(injuryManager.isPlayerInjured(player)).toBe(true);
    });

    it('should get all injured players in a team', () => {
      homeTeam.players[1].injured = 20;
      homeTeam.players[3].injured = 35;
      
      const injuredPlayers = injuryManager.getInjuredPlayers(homeTeam);
      
      expect(injuredPlayers).toHaveLength(2);
      expect(injuredPlayers).toContain(homeTeam.players[1]);
      expect(injuredPlayers).toContain(homeTeam.players[3]);
    });

    it('should check if team has injured players on field', () => {
      expect(injuryManager.hasInjuredPlayersOnField(homeTeam)).toBe(false);
      
      // Injure a starting player (position 0-10)
      homeTeam.players[5].injured = 25;
      expect(injuryManager.hasInjuredPlayersOnField(homeTeam)).toBe(true);
      
      // Injure only a substitute (position 11+) - should still be false for "on field"
      homeTeam.players[5].injured = undefined;
      homeTeam.players[12].injured = 25;
      expect(injuryManager.hasInjuredPlayersOnField(homeTeam)).toBe(false);
    });
  });

  describe('performance impact', () => {
    it('should return normal performance for healthy players', () => {
      const player = homeTeam.players[4];
      
      const multiplier = injuryManager.getInjuryPerformanceMultiplier(player);
      expect(multiplier).toBe(1.0);
    });

    it('should return reduced performance for injured players', () => {
      const player = homeTeam.players[4];
      player.injured = 30;
      
      const multiplier = injuryManager.getInjuryPerformanceMultiplier(player);
      expect(multiplier).toBe(0.7);
    });

    it('should consistently return same multiplier for injured players', () => {
      const player = homeTeam.players[4];
      player.injured = 30;
      
      const multiplier1 = injuryManager.getInjuryPerformanceMultiplier(player);
      const multiplier2 = injuryManager.getInjuryPerformanceMultiplier(player);
      
      expect(multiplier1).toBe(multiplier2);
    });
  });
});
