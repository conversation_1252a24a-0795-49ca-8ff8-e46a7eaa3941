import { MatchEvent } from '@/model/fixture.js';
import { CommentaryManager } from '@/simulation/commentary.js';
import { logger } from '@/utils/logger.js';
import { seededRandom } from '@/utils/seeded-random.js';
import { GamePlayer, MatchArea, Team } from '../types.js';

/**
 * Manages the overall state of a match including time, events, and basic flow
 */
export class MatchStateManager {
  private events: MatchEvent[] = [];
  private matchTick = 0;
  private minute = 0;
  private half = 0;
  private commentaryManager: CommentaryManager;

  constructor(
    private homeTeam: Team,
    private awayTeam: Team
  ) {
    this.commentaryManager = CommentaryManager.getInstance();
  }

  /**
   * Get the current match tick - an integer that increments every event of every minute
   */
  getMatchTick(): number {
    return this.matchTick;
  }
  incrementMatchTick(): void {
    this.matchTick++;
  }

  /**
   * Get the current minute of the match
   */
  getCurrentMinute(): number {
    return this.minute;
  }

  /**
   * Get the current half of the match
   */
  getCurrentHalf(): number {
    return this.half;
  }

  /**
   * Set the current minute
   */
  setCurrentMinute(minute: number): void {
    this.minute = minute;
  }

  /**
   * Set the current half
   */
  setCurrentHalf(half: number): void {
    this.half = half;
  }

  /**
   * Get all match events
   */
  getEvents(): MatchEvent[] {
    return this.events;
  }

  /**
   * Add a match event with commentary
   */
  addMatchEvent(
    stringID: string,
    substitutions: {
      team?: string;
      homeTeam?: string;
      oppTeam?: string;
      awayTeam?: string;
      player?: string;
      nextPlayer?: string;
      oppPlayer?: string;
      homeScore?: string;
      awayScore?: string;
      stats?: any;
    }
  ): void {
    const newEvent = {
      localisationId: stringID,
      substitutions: {
        team: substitutions.team,
        homeTeam: substitutions.homeTeam,
        oppTeam: substitutions.oppTeam,
        awayTeam: substitutions.awayTeam,
        player: substitutions.player,
        nextPlayer: substitutions.nextPlayer,
        oppPlayer: substitutions.oppPlayer,
        homeScore: substitutions.homeScore,
        awayScore: substitutions.awayScore,
      },
      minute: this.minute,
      half: this.half,
    };
    this.events.push(newEvent);
    this.log(this.commentaryManager.getText(stringID, newEvent.substitutions));
  }

  /**
   * Get the kick-off taker for a team
   */
  getKickOffTaker(teamInPossession: number): GamePlayer {
    const team = teamInPossession === 0 ? this.homeTeam : this.awayTeam;
    const players = team.players;

    // First try to get a midfielder
    const midfielders = players.filter((_, index) => index >= 5 && index <= 8);
    if (midfielders.length > 0) {
      // Get a random midfielder
      return midfielders[Math.floor(seededRandom() * midfielders.length)]!;
    }

    // If no midfielders, get any outfield player
    const outfieldPlayers = players.filter((_, index) => index > 0 && index <= 10);
    if (outfieldPlayers.length > 0) {
      return outfieldPlayers[Math.floor(seededRandom() * outfieldPlayers.length)]!;
    }

    logger.error('No players found', {
      teamInPossession,
      players,
    });
    throw new Error('No players found');
  }

  /**
   * Determine if something should happen in this minute
   */
  shouldEventHappen(matchArea: MatchArea): boolean {
    if (matchArea !== MatchArea.KICK_OFF && seededRandom() > 0.66) {
      this.log(
        `Minute ${this.minute > 45 ? `45+${this.minute - 45}` : this.minute}: Nothing happens`
      );
      return false;
    }
    return true;
  }

  /**
   * Log match state information
   */
  logMatchState(matchArea: MatchArea, teamInPossession: number): void {
    this.log(
      `Minute ${this.minute > 45 ? `45+${this.minute - 45}` : this.minute}: ${matchArea} - Team ${
        teamInPossession === 0 ? this.homeTeam.teamName : this.awayTeam.teamName
      }`
    );
  }

  /**
   * Get the opposition area from a match area
   */
  getOppositionAreaFromMatchArea(matchArea: MatchArea): MatchArea {
    if (matchArea === MatchArea.DEFENCE) {
      return MatchArea.ATTACK;
    }
    if (matchArea === MatchArea.MIDFIELD) {
      return MatchArea.MIDFIELD;
    }
    if (matchArea === MatchArea.ATTACK) {
      return MatchArea.DEFENCE;
    }
    if (matchArea === MatchArea.SHOT) {
      return MatchArea.GOAL_KICK;
    }
    throw new Error('Invalid match area');
  }

  /**
   * Calculate half length with added time
   */
  calculateHalfLength(): number {
    return 45 + Math.floor(seededRandom() * 6);
  }

  private log(message: string): void {
    logger.local(message);
  }
}
