import { logger } from '@/utils/logger.js';
import { GamePlayer, Team } from '../types.js';
import { MatchStateManager } from './match-state-manager.js';

/**
 * Manages player substitutions during a match
 */
export class SubstitutionManager {
  private readonly maxSubstitutions = 3;

  constructor(private matchStateManager: MatchStateManager) {}

  /**
   * Check if any substitutions should be made for both teams
   */
  checkForSubstitutions(homeTeam: Team, awayTeam: Team, minute: number): void {
    [homeTeam, awayTeam].forEach((team) => {
      this.checkTeamForSubstitutions(team, minute);
    });
  }

  /**
   * Check if a specific team needs substitutions
   */
  private checkTeamForSubstitutions(team: Team, minute: number): void {
    // Only check active players (0-10)
    const activePlayers = team.players.slice(0, 11);
    const substitutes = team.players.slice(11, 16);

    // Find players with low energy
    const exhaustedPlayers = activePlayers.filter(
      (player) =>
        ((player.player.energy < 40 && minute > 40) || player.player.energy < 10) &&
        player.leftMatchMinute === undefined
    );

    if (exhaustedPlayers.length === 0 || substitutes.length === 0) return;

    // Check substitution limit - count only players who were substituted (have a substitute)
    const subsUsed = team.players.filter((p) => p.substitute !== undefined).length;

    // Select one player to substitute this minute (avoid multiple subs at once)
    const playerToSub = exhaustedPlayers[0];
    if (subsUsed < this.maxSubstitutions && playerToSub) {
      this.substitutePlayer(team, playerToSub, substitutes);
    }
  }

  /**
   * Substitute a player with a substitute
   */
  private substitutePlayer(
    team: Team,
    exhaustedPlayer: GamePlayer,
    substitutes: GamePlayer[]
  ): void {
    // Find a substitute with similar position
    const position = team.players.indexOf(exhaustedPlayer);
    let bestSubstitute = substitutes[0]!;

    // Try to match by position type (defender, midfielder, forward)
    if (position > 0 && position <= 4) {
      // Defender - find a defensive substitute
      const defensiveSubs = substitutes.filter((s) => s.leftMatchMinute === undefined);
      if (defensiveSubs.length > 0) bestSubstitute = defensiveSubs[0]!;
    } else if (position >= 5 && position <= 8) {
      // Midfielder - find a midfield substitute
      const midfieldSubs = substitutes.filter((s) => s.leftMatchMinute === undefined);
      if (midfieldSubs.length > 0) bestSubstitute = midfieldSubs[0]!;
    } else if (position >= 9 && position <= 10) {
      // Forward - find an attacking substitute
      const attackingSubs = substitutes.filter((s) => s.leftMatchMinute === undefined);
      if (attackingSubs.length > 0) bestSubstitute = attackingSubs[0]!;
    }

    // Find the substitute's position in the array
    const substitutePosition = team.players.indexOf(bestSubstitute);

    // Swap the players completely (exchange their positions)
    team.players[substitutePosition] = exhaustedPlayer;
    team.players[position] = bestSubstitute;

    // Mark as subbed
    exhaustedPlayer.substitute = bestSubstitute;
    exhaustedPlayer.leftMatchMinute = this.matchStateManager.getCurrentMinute() + 
      (this.matchStateManager.getCurrentHalf() - 1) * 45;
    bestSubstitute.joinedMatchMinute = this.matchStateManager.getCurrentMinute() + 
      (this.matchStateManager.getCurrentHalf() - 1) * 45;

    // Log the substitution
    this.matchStateManager.addMatchEvent('SUBSTITUTION', {
      team: team.teamId,
      player: exhaustedPlayer.player.playerId,
      nextPlayer: bestSubstitute.player.playerId,
    });

    logger.local(
      `Substitution for ${team.teamName}: ${exhaustedPlayer.player.surname} OFF, ${bestSubstitute.player.surname} ON`
    );
  }

  /**
   * Force a substitution for an injured player
   */
  forceSubstitution(team: Team, injuredPlayer: GamePlayer): boolean {
    const substitutes = team.players.slice(11, 16).filter((p) => p.leftMatchMinute === undefined);
    const subsUsed = team.players.filter((p) => p.substitute !== undefined).length;

    logger.local(`${injuredPlayer.player.surname} is injured!`);

    if (subsUsed < this.maxSubstitutions && substitutes.length > 0) {
      // Make a substitution for the injured player
      this.substitutePlayer(team, injuredPlayer, substitutes);
      return true;
    } else {
      logger.local(
        `${injuredPlayer.player.surname} has to continue despite the injury as no substitutions remain.`
      );
      // Player remains injured and on field
      return false;
    }
  }

  /**
   * Get the number of substitutions used by a team
   */
  getSubstitutionsUsed(team: Team): number {
    return team.players.filter((p) => p.substitute !== undefined).length;
  }

  /**
   * Check if a team can make more substitutions
   */
  canMakeSubstitution(team: Team): boolean {
    const subsUsed = this.getSubstitutionsUsed(team);
    const availableSubstitutes = team.players.slice(11, 16).filter((p) => p.leftMatchMinute === undefined);
    return subsUsed < this.maxSubstitutions && availableSubstitutes.length > 0;
  }
}
