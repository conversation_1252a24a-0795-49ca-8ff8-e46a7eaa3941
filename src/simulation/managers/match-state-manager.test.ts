import { MatchStateManager } from '@/simulation/managers/match-state-manager.js';
import { TeamBuilder } from '@/simulation/utils/team-builder.js';
import { MatchArea } from '@/simulation/types.js';
import { setRandomSeed } from '@/utils/seeded-random.js';
import { beforeEach, describe, expect, it } from 'vitest';

describe('MatchStateManager', () => {
  let matchStateManager: MatchStateManager;
  let homeTeam: any;
  let awayTeam: any;

  beforeEach(() => {
    setRandomSeed(12345);
    homeTeam = TeamBuilder.createStrongTeam('home-1', 'Home FC', 'gameworld-1');
    awayTeam = TeamBuilder.createStrongTeam('away-1', 'Away United', 'gameworld-1');
    matchStateManager = new MatchStateManager(homeTeam, awayTeam);
  });

  describe('time management', () => {
    it('should initialize with minute 0 and half 0', () => {
      expect(matchStateManager.getCurrentMinute()).toBe(0);
      expect(matchStateManager.getCurrentHalf()).toBe(0);
    });

    it('should update current minute', () => {
      matchStateManager.setCurrentMinute(25);
      expect(matchStateManager.getCurrentMinute()).toBe(25);
    });

    it('should update current half', () => {
      matchStateManager.setCurrentHalf(2);
      expect(matchStateManager.getCurrentHalf()).toBe(2);
    });

    it('should calculate half length between 45-50 minutes', () => {
      const halfLength = matchStateManager.calculateHalfLength();
      expect(halfLength).toBeGreaterThanOrEqual(45);
      expect(halfLength).toBeLessThanOrEqual(50);
    });
  });

  describe('event management', () => {
    it('should start with no events', () => {
      expect(matchStateManager.getEvents()).toHaveLength(0);
    });

    it('should add match events with correct structure', () => {
      matchStateManager.setCurrentMinute(10);
      matchStateManager.setCurrentHalf(1);
      
      matchStateManager.addMatchEvent('TEST_EVENT', {
        team: 'home-1',
        player: 'player-1',
      });

      const events = matchStateManager.getEvents();
      expect(events).toHaveLength(1);
      expect(events[0]).toEqual({
        localisationId: 'TEST_EVENT',
        substitutions: {
          team: 'home-1',
          player: 'player-1',
          homeTeam: undefined,
          oppTeam: undefined,
          awayTeam: undefined,
          nextPlayer: undefined,
          oppPlayer: undefined,
          homeScore: undefined,
          awayScore: undefined,
        },
        minute: 10,
        half: 1,
      });
    });

    it('should add multiple events in sequence', () => {
      matchStateManager.addMatchEvent('EVENT_1', { team: 'home-1' });
      matchStateManager.addMatchEvent('EVENT_2', { team: 'away-1' });

      const events = matchStateManager.getEvents();
      expect(events).toHaveLength(2);
      expect(events[0]!.localisationId).toBe('EVENT_1');
      expect(events[1]!.localisationId).toBe('EVENT_2');
    });
  });

  describe('kick-off taker selection', () => {
    it('should select a midfielder for kick-off when available', () => {
      const kickOffTaker = matchStateManager.getKickOffTaker(0);
      
      // Check that the selected player is from the home team
      expect(homeTeam.players).toContain(kickOffTaker);
      
      // Check that it's likely a midfielder (positions 5-8)
      const playerIndex = homeTeam.players.indexOf(kickOffTaker);
      expect(playerIndex).toBeGreaterThanOrEqual(0);
      expect(playerIndex).toBeLessThan(16);
    });

    it('should select from away team when specified', () => {
      const kickOffTaker = matchStateManager.getKickOffTaker(1);
      
      // Check that the selected player is from the away team
      expect(awayTeam.players).toContain(kickOffTaker);
    });

    it('should throw error when no players available', () => {
      const emptyTeam = TeamBuilder.createStrongTeam('empty', 'Empty FC', 'gameworld-1');
      emptyTeam.players = []; // Remove all players
      
      const emptyMatchState = new MatchStateManager(emptyTeam, awayTeam);
      
      expect(() => emptyMatchState.getKickOffTaker(0)).toThrow('No players found');
    });
  });

  describe('event probability', () => {
    it('should always allow kick-off events', () => {
      const shouldHappen = matchStateManager.shouldEventHappen(MatchArea.KICK_OFF);
      expect(shouldHappen).toBe(true);
    });

    it('should handle event probability logic', () => {
      // This test mainly ensures the method doesn't throw and returns a boolean
      const result = matchStateManager.shouldEventHappen(MatchArea.MIDFIELD);
      expect(typeof result).toBe('boolean');
    });
  });

  describe('opposition area mapping', () => {
    it('should map defence to attack', () => {
      const oppositeArea = matchStateManager.getOppositionAreaFromMatchArea(MatchArea.DEFENCE);
      expect(oppositeArea).toBe(MatchArea.ATTACK);
    });

    it('should map attack to defence', () => {
      const oppositeArea = matchStateManager.getOppositionAreaFromMatchArea(MatchArea.ATTACK);
      expect(oppositeArea).toBe(MatchArea.DEFENCE);
    });

    it('should map midfield to midfield', () => {
      const oppositeArea = matchStateManager.getOppositionAreaFromMatchArea(MatchArea.MIDFIELD);
      expect(oppositeArea).toBe(MatchArea.MIDFIELD);
    });

    it('should map shot to goal kick', () => {
      const oppositeArea = matchStateManager.getOppositionAreaFromMatchArea(MatchArea.SHOT);
      expect(oppositeArea).toBe(MatchArea.GOAL_KICK);
    });

    it('should throw error for invalid match area', () => {
      expect(() => 
        matchStateManager.getOppositionAreaFromMatchArea('INVALID_AREA' as MatchArea)
      ).toThrow('Invalid match area');
    });
  });

  describe('match state logging', () => {
    it('should log match state with correct format', () => {
      matchStateManager.setCurrentMinute(25);
      
      // This test mainly ensures the method doesn't throw
      expect(() => {
        matchStateManager.logMatchState(MatchArea.MIDFIELD, 0);
      }).not.toThrow();
    });

    it('should handle added time format correctly', () => {
      matchStateManager.setCurrentMinute(47);
      
      // This test mainly ensures the method doesn't throw for added time
      expect(() => {
        matchStateManager.logMatchState(MatchArea.ATTACK, 1);
      }).not.toThrow();
    });
  });
});
