import { MatchStateManager } from '@/simulation/managers/match-state-manager.js';
import { SubstitutionManager } from '@/simulation/managers/substitution-manager.js';
import { Team } from '@/simulation/types.js';
import { TeamBuilder } from '@/simulation/utils/team-builder.js';
import { beforeEach, describe, expect, it, vi } from 'vitest';

describe('SubstitutionManager', () => {
  let substitutionManager: SubstitutionManager;
  let matchStateManager: MatchStateManager;
  let homeTeam: Team;
  let awayTeam: Team;

  beforeEach(() => {
    homeTeam = TeamBuilder.createStrongTeam('home-1', 'Home FC', 'gameworld-1');
    awayTeam = TeamBuilder.createStrongTeam('away-1', 'Away United', 'gameworld-1');
    matchStateManager = new MatchStateManager(homeTeam, awayTeam);
    substitutionManager = new SubstitutionManager(matchStateManager);

    // Mock the match state manager methods
    vi.spyOn(matchStateManager, 'getCurrentMinute').mockReturnValue(60);
    vi.spyOn(matchStateManager, 'getCurrentHalf').mockReturnValue(2);
    vi.spyOn(matchStateManager, 'addMatchEvent').mockImplementation(() => {});
  });

  describe('substitution eligibility', () => {
    it('should identify teams that can make substitutions', () => {
      const canSub = substitutionManager.canMakeSubstitution(homeTeam);
      expect(canSub).toBe(true);
    });

    it('should prevent substitutions when limit reached', () => {
      // Mark 3 players as substituted
      for (let i = 0; i < 3; i++) {
        homeTeam.players[11 + i]!.substitute = homeTeam.players[i + 1];
      }

      const canSub = substitutionManager.canMakeSubstitution(homeTeam);
      expect(canSub).toBe(false);
    });

    it('should prevent substitutions when no substitutes available', () => {
      // Mark all substitutes as already used
      for (let i = 11; i < 16; i++) {
        homeTeam.players[i]!.leftMatchMinute = 30;
      }

      const canSub = substitutionManager.canMakeSubstitution(homeTeam);
      expect(canSub).toBe(false);
    });
  });

  describe('automatic substitutions', () => {
    it('should substitute exhausted players after minute 40', () => {
      // Set a player's energy very low
      homeTeam.players[5]!.player.energy = 35;

      substitutionManager.checkForSubstitutions(homeTeam, awayTeam, 45);

      // Check that a substitution was made - the player should be moved to substitute position
      const substitutedPlayers = homeTeam.players.filter((p) => p.substitute !== undefined);
      expect(substitutedPlayers.length).toBeGreaterThan(0);
    });

    it('should substitute players with critically low energy anytime', () => {
      // Set a player's energy critically low
      homeTeam.players[3]!.player.energy = 5;

      substitutionManager.checkForSubstitutions(homeTeam, awayTeam, 20);

      // Check that a substitution was made even early in the game
      const substitutedPlayers = homeTeam.players.filter((p) => p.substitute !== undefined);
      expect(substitutedPlayers.length).toBeGreaterThan(0);
    });

    it('should not substitute players with good energy early in game', () => {
      // All players have good energy
      homeTeam.players.forEach((player) => {
        player.player.energy = 80;
      });

      substitutionManager.checkForSubstitutions(homeTeam, awayTeam, 20);

      // Check that no substitutions were made
      const substitutions = homeTeam.players.filter((p) => p.substitute !== undefined);
      expect(substitutions).toHaveLength(0);
    });

    it('should respect substitution limit', () => {
      // Set multiple players with low energy
      for (let i = 1; i <= 5; i++) {
        homeTeam.players[i]!.player.energy = 5;
      }

      // Make substitutions multiple times
      for (let minute = 20; minute <= 80; minute += 10) {
        substitutionManager.checkForSubstitutions(homeTeam, awayTeam, minute);
      }

      // Should only make maximum 3 substitutions
      const substitutions = homeTeam.players.filter((p) => p.substitute !== undefined);
      expect(substitutions.length).toBeLessThanOrEqual(3);
    });
  });

  describe('forced substitutions (injuries)', () => {
    it('should force substitution for injured player when possible', () => {
      const injuredPlayer = homeTeam.players[2]!;

      const result = substitutionManager.forceSubstitution(homeTeam, injuredPlayer);

      expect(result).toBe(true);
      expect(injuredPlayer.leftMatchMinute).toBeDefined();
      expect(injuredPlayer.substitute).toBeDefined();
    });

    it('should fail to substitute when no subs available', () => {
      // Use up all substitutes
      for (let i = 11; i < 16; i++) {
        homeTeam.players[i]!.leftMatchMinute = 30;
      }

      const injuredPlayer = homeTeam.players[2]!;
      const result = substitutionManager.forceSubstitution(homeTeam, injuredPlayer);

      expect(result).toBe(false);
      expect(injuredPlayer.leftMatchMinute).toBeUndefined();
    });

    it('should fail to substitute when substitution limit reached', () => {
      // Mark 3 players as already substituted
      for (let i = 0; i < 3; i++) {
        homeTeam.players[i + 1]!.substitute = homeTeam.players[11 + i];
      }

      const injuredPlayer = homeTeam.players[4]!;
      const result = substitutionManager.forceSubstitution(homeTeam, injuredPlayer);

      expect(result).toBe(false);
      expect(injuredPlayer.leftMatchMinute).toBeUndefined();
    });
  });

  describe('position matching', () => {
    it('should swap player positions correctly', () => {
      const originalPlayer = homeTeam.players[5]!; // Midfielder
      const originalSubstitute = homeTeam.players[12]!; // First substitute

      // Set low energy to trigger substitution
      originalPlayer.player.energy = 5;

      substitutionManager.checkForSubstitutions(homeTeam, awayTeam, 60);

      // Check that a substitution occurred and positions were swapped
      const substitutedPlayers = homeTeam.players.filter((p) => p.substitute !== undefined);
      expect(substitutedPlayers.length).toBeGreaterThan(0);

      // The original player should now be marked as substituted
      expect(originalPlayer.leftMatchMinute).toBeDefined();
      expect(originalPlayer.substitute).toBeDefined();
    });

    it('should set correct substitution timing', () => {
      const player = homeTeam.players[7]!;
      player.player.energy = 5;

      substitutionManager.checkForSubstitutions(homeTeam, awayTeam, 65);

      // Check timing is set correctly (minute 65 of second half = minute 105 overall based on mock)
      const substitutedPlayers = homeTeam.players.filter((p) => p.substitute !== undefined);
      expect(substitutedPlayers.length).toBeGreaterThan(0);

      // Find the substituted player and check timing
      const substitutedPlayer = substitutedPlayers.find(
        (p) => p.player.playerId === player.player.playerId
      );
      if (substitutedPlayer) {
        expect(substitutedPlayer.leftMatchMinute).toBe(105); // 60 + (2-1) * 45 = 105
        expect(substitutedPlayer.substitute!.joinedMatchMinute).toBe(105);
      }
    });
  });

  describe('substitution counting', () => {
    it('should count substitutions correctly', () => {
      expect(substitutionManager.getSubstitutionsUsed(homeTeam)).toBe(0);

      // Make a substitution
      homeTeam.players[1]!.substitute = homeTeam.players[12]!;
      expect(substitutionManager.getSubstitutionsUsed(homeTeam)).toBe(1);

      // Make another substitution
      homeTeam.players[2]!.substitute = homeTeam.players[13];
      expect(substitutionManager.getSubstitutionsUsed(homeTeam)).toBe(2);
    });

    it('should handle multiple teams independently', () => {
      // Make substitution for home team
      homeTeam.players[1]!.substitute = homeTeam.players[12];

      // Away team should still have 0 substitutions
      expect(substitutionManager.getSubstitutionsUsed(homeTeam)).toBe(1);
      expect(substitutionManager.getSubstitutionsUsed(awayTeam)).toBe(0);
    });
  });
});
