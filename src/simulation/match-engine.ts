import { logger } from '@/utils/logger.js';
import { seededRandom, seededRandomIntInRange } from '@/utils/seeded-random.js';
import { matchAreaPossibilities } from './match-events.js';
import { getPlayersInPosition } from './player-attributes.js';
import { StatsCalculator } from './stats-calculator.js';
import { GamePlayer, MatchArea, NextEvent, SimulationResult, Team } from './types.js';

// Import the new modular components
import { InjuryManager } from './managers/injury-manager.js';
import { MatchStateManager } from './managers/match-state-manager.js';
import { SubstitutionManager } from './managers/substitution-manager.js';
import { EventProcessor } from './processors/event-processor.js';

export class MatchEngine {
  private readonly statsCalculator: StatsCalculator;
  private readonly matchStateManager: MatchStateManager;
  private eventProcessor: EventProcessor;
  private readonly substitutionManager: SubstitutionManager;
  private readonly injuryManager: InjuryManager;

  constructor(
    private homeTeam: Team,
    private awayTeam: Team,
    private maximumEventsPerMinute: number = 4
  ) {
    this.statsCalculator = new StatsCalculator();
    this.matchStateManager = new MatchStateManager(homeTeam, awayTeam);
    this.substitutionManager = new SubstitutionManager(this.matchStateManager);
    this.injuryManager = new InjuryManager(this.matchStateManager, this.substitutionManager);
    this.eventProcessor = new EventProcessor(
      this.matchStateManager,
      this.injuryManager,
      this.statsCalculator
    );
  }

  private log(message: string) {
    logger.local(message);
  }

  public simulate(): SimulationResult {
    const teamStartGameInPossession = seededRandom() > 0.5 ? 0 : 1;

    // Simulate each half
    for (let half = 1; half <= 2; half++) {
      this.matchStateManager.setCurrentHalf(half);
      this.simulateHalf(half, teamStartGameInPossession);
    }

    return {
      stats: this.statsCalculator.getStats(),
      events: this.matchStateManager.getEvents(),
    };
  }

  private simulateHalf(half: number, teamStartGameInPossession: number) {
    const halfLength = this.matchStateManager.calculateHalfLength();
    this.log(`Half ${half} begins`);

    const matchArea = MatchArea.KICK_OFF;
    const teamInPossession = half === 1 ? teamStartGameInPossession : 1 - teamStartGameInPossession;
    const playerInPossession = this.matchStateManager.getKickOffTaker(teamInPossession);

    let nextEvent: NextEvent | null = {
      matchArea: matchArea,
      teamInPossession: teamInPossession,
      playerInPossession: playerInPossession,
    };
    const randomTeamInPossession = seededRandomIntInRange(0, 1);
    for (let minute = 1; minute <= halfLength; minute++) {
      this.matchStateManager.setCurrentMinute(minute);
      nextEvent = this.simulateMinute(
        minute,
        nextEvent?.matchArea ?? MatchArea.MIDFIELD,
        nextEvent?.teamInPossession ?? randomTeamInPossession,
        nextEvent?.playerInPossession ??
          this.matchStateManager.getKickOffTaker(randomTeamInPossession)
      );
    }

    const stats = this.statsCalculator.getStats();
    if (half === 1) {
      this.matchStateManager.addMatchEvent('HALF_TIME', {
        stats,
        homeTeam: this.homeTeam.teamId,
        awayTeam: this.awayTeam.teamId,
        homeScore: stats.score[0]?.toString(),
        awayScore: stats.score[1]?.toString(),
      });
    } else {
      this.matchStateManager.addMatchEvent('FULL_TIME', {
        stats,
        homeTeam: this.homeTeam.teamId,
        awayTeam: this.awayTeam.teamId,
        homeScore: stats.score[0]?.toString(),
        awayScore: stats.score[1]?.toString(),
      });
    }
  }

  // This method is now handled by MatchStateManager

  private simulateMinute(
    minute: number,
    matchArea: MatchArea,
    teamInPossession: number,
    playerInPossession: GamePlayer
  ): NextEvent | null {
    if (!this.matchStateManager.shouldEventHappen(matchArea)) {
      return null;
    }

    this.matchStateManager.logMatchState(matchArea, teamInPossession);

    const teams = [this.homeTeam, this.awayTeam];

    const newEvent = this.simulateEvent(matchArea, teamInPossession, playerInPossession, teams);

    this.substitutionManager.checkForSubstitutions(this.homeTeam, this.awayTeam, minute);

    return newEvent;
  }

  private simulateEvent(
    matchArea: MatchArea,
    teamInPossession: number,
    playerInPossession: GamePlayer,
    teams: Team[],
    eventNumber = 0
  ): NextEvent {
    const possibilities = matchAreaPossibilities.find((p) => p.matchArea === matchArea);
    if (!possibilities) {
      throw new Error(`Match area not found ${matchArea}`);
    }

    const randomValue = seededRandom();
    const event = possibilities.possibilities.find((_p, index, arr) => {
      const cumulativeProbability = arr
        .slice(0, index + 1)
        .reduce((acc, curr) => acc + curr.probability, 0);
      return randomValue < cumulativeProbability;
    });

    if (!event) {
      throw new Error('Event not found');
    }

    // Get all players in target position for in possession team
    const possibleTargetPlayers = getPlayersInPosition(
      playerInPossession.player.playerId,
      teams[teamInPossession]!,
      event.targetArea
    );
    const targetPlayer =
      possibleTargetPlayers[seededRandomIntInRange(0, possibleTargetPlayers.length - 1)]!;

    // This event always succeeds so no need to simulate anything
    if (matchArea === MatchArea.KICK_OFF) {
      this.matchStateManager.addMatchEvent('KICK_OFF', {
        team: teams[teamInPossession]!.teamId,
        oppTeam: teams[1 - teamInPossession]!.teamId,
        player: playerInPossession.player.playerId,
        nextPlayer: targetPlayer.player.playerId,
        oppPlayer: '',
      });

      return {
        matchArea: event.targetArea,
        teamInPossession: teamInPossession,
        playerInPossession: targetPlayer,
      };
    } else {
      this.matchStateManager.addMatchEvent(event.commentaryId, {
        player: playerInPossession.player.playerId,
        nextPlayer: targetPlayer.player.playerId,
        team: teamInPossession === 0 ? this.homeTeam.teamId : this.awayTeam.teamId,
        oppTeam: teamInPossession === 0 ? this.awayTeam.teamId : this.homeTeam.teamId,
      });
    }

    // Get all players in target position for opposition team
    const possibleOppositionPlayers = getPlayersInPosition(
      '',
      teams[1 - teamInPossession]!,
      event.targetArea
    );

    // pick a random opposition player to challenge for the ball
    const oppositionPlayer =
      possibleOppositionPlayers[seededRandomIntInRange(0, possibleOppositionPlayers.length - 1)]!;

    const playerTeamRedCards = this.statsCalculator.getStats().redCards[teamInPossession] || 0;
    const opposingTeamRedCards =
      this.statsCalculator.getStats().redCards[1 - teamInPossession] || 0;

    const hasPlayerTeamInjury = teams[teamInPossession]!.players.slice(0, 11).some(
      (p) => p.injured !== undefined
    );
    const hasOpposingTeamInjury = teams[1 - teamInPossession]!.players.slice(0, 11).some(
      (p) => p.injured !== undefined
    );

    // Energy cost is doubled for teams with red cards
    playerInPossession.player.energy -= playerTeamRedCards > 0 || hasPlayerTeamInjury ? 2 : 1;
    oppositionPlayer.player.energy -= opposingTeamRedCards > 0 || hasOpposingTeamInjury ? 2 : 1;

    // Ensure energy doesn't go below zero
    playerInPossession.player.energy = Math.max(0, playerInPossession.player.energy);
    oppositionPlayer.player.energy = Math.max(0, oppositionPlayer.player.energy);

    const successThreshold = this.eventProcessor.calculateSuccessThreshold(
      event,
      playerInPossession,
      oppositionPlayer
    );

    if (randomValue > successThreshold) {
      this.statsCalculator.updateMatchStats({
        event,
        teamInPossession,
        matchArea: event.targetArea,
      });

      const successEvent = this.eventProcessor.processEventSuccess(
        event,
        teams,
        playerInPossession,
        oppositionPlayer,
        teamInPossession,
        possibleTargetPlayers
      );

      this.statsCalculator.updatePlayerStats({
        playerInPossession,
        oppositionPlayer,
        playerStats: playerInPossession.stats,
        oppositionPlayerStats: oppositionPlayer.stats,
        event,
        minute:
          this.matchStateManager.getCurrentMinute() +
          (this.matchStateManager.getCurrentHalf() - 1) * 45,
      });

      if (eventNumber < this.maximumEventsPerMinute) {
        this.matchStateManager.incrementMatchTick();
        return this.simulateEvent(
          successEvent.matchArea,
          successEvent.teamInPossession,
          successEvent.playerInPossession!,
          teams,
          eventNumber + 1
        );
      }
      return successEvent;
    }

    // otherwise opposition player wins the ball
    const { matchEvent: failureEvent, negativeOutcome } = this.eventProcessor.processEventFailure(
      playerInPossession,
      oppositionPlayer,
      targetPlayer,
      event,
      teamInPossession,
      teams,
      possibleTargetPlayers
    );

    this.statsCalculator.updateMatchStats({
      event,
      negativeOutcome,
      teamInPossession,
      matchArea: event.targetArea,
    });

    this.statsCalculator.updatePlayerStats({
      playerInPossession,
      oppositionPlayer,
      playerStats: playerInPossession.stats,
      oppositionPlayerStats: oppositionPlayer.stats,
      event,
      negativeOutcome,
      minute:
        this.matchStateManager.getCurrentMinute() +
        (this.matchStateManager.getCurrentHalf() - 1) * 45,
    });

    if (!failureEvent.playerInPossession) {
      failureEvent.playerInPossession =
        possibleTargetPlayers[seededRandomIntInRange(0, possibleTargetPlayers.length - 1)]!;
    }

    if (eventNumber < this.maximumEventsPerMinute) {
      this.matchStateManager.incrementMatchTick();
      return this.simulateEvent(
        failureEvent.matchArea,
        failureEvent.teamInPossession,
        failureEvent.playerInPossession,
        teams,
        eventNumber + 1
      );
    }

    return failureEvent;
  }
}
