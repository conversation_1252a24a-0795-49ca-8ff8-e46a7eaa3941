import { Attributes } from '@/model/player.js';
import {
  NegativeOutcome,
  passNegativeOutcomes,
  runNegativeOutcomes,
  shotNegativeOutcomes,
} from './event-outcomes.js';
import { MatchArea } from './types.js';

export interface Possibility {
  description: string;
  commentaryId: string;
  targetArea: MatchArea;
  probability: number;
  positiveAttributes: (keyof Attributes)[];
  negativeAttributes: (keyof Attributes)[];
  negativeOutcomes?: NegativeOutcome[];
}

export interface MatchAreaPossibilities {
  matchArea: MatchArea;
  possibilities: Possibility[];
}

export const matchAreaPossibilities: MatchAreaPossibilities[] = [
  {
    // Always 100% chance of success from kick off
    matchArea: MatchArea.KICK_OFF,
    possibilities: [
      {
        description: 'Pass back',
        commentaryId: 'PASS_BACK',
        probability: 0.8,
        targetArea: MatchArea.DEFENCE,
        positiveAttributes: ['passing'],
        negativeAttributes: [],
      },
      {
        description: 'Pass sideways',
        commentaryId: 'PASS_SIDEWAYS',
        probability: 0.2,
        targetArea: MatchArea.MIDFIELD,
        positiveAttributes: ['passing'],
        negativeAttributes: [],
      },
    ],
  },
  {
    matchArea: MatchArea.MIDFIELD,
    possibilities: [
      {
        description: 'Pass back (defence)',
        commentaryId: 'PASS_BACK',
        probability: 0.2,
        targetArea: MatchArea.DEFENCE,
        positiveAttributes: ['passing'],
        negativeAttributes: ['marking'],
        negativeOutcomes: passNegativeOutcomes,
      },
      {
        description: 'Pass sideways (midfield)',
        commentaryId: 'PASS_SIDEWAYS',
        probability: 0.25,
        targetArea: MatchArea.MIDFIELD,
        positiveAttributes: ['passing'],
        negativeAttributes: ['tackling'],
        negativeOutcomes: passNegativeOutcomes,
      },
      {
        description: 'Pass forwards (attack)',
        commentaryId: 'PASS_FORWARD',
        probability: 0.25,
        targetArea: MatchArea.ATTACK,
        positiveAttributes: ['passing'],
        negativeAttributes: ['heading'],
        negativeOutcomes: passNegativeOutcomes,
      },
      {
        description: 'Carry ball forwards (attack)',
        commentaryId: 'CARRY_FORWARDS',
        probability: 0.15,
        targetArea: MatchArea.ATTACK,
        positiveAttributes: ['ballControl', 'pace'],
        negativeAttributes: ['tackling'],
        negativeOutcomes: runNegativeOutcomes,
      },
      {
        description: 'Carry ball sideways (midfield)',
        commentaryId: 'CARRY_SIDEWAYS',
        probability: 0.1,
        targetArea: MatchArea.MIDFIELD,
        positiveAttributes: ['ballControl', 'pace'],
        negativeAttributes: ['tackling'],
        negativeOutcomes: runNegativeOutcomes,
      },
      {
        description: 'Carry ball backwards (defence)',
        commentaryId: 'CARRY_SIDEWAYS',
        probability: 0.04,
        targetArea: MatchArea.DEFENCE,
        positiveAttributes: ['ballControl', 'pace'],
        negativeAttributes: ['tackling'],
        negativeOutcomes: runNegativeOutcomes,
      },
      {
        description: 'Lob the keeper',
        commentaryId: 'LOB_KEEPER',
        probability: 0.01,
        targetArea: MatchArea.SHOT,
        positiveAttributes: ['finishing'],
        negativeAttributes: ['positioning'],
        negativeOutcomes: shotNegativeOutcomes,
      },
    ],
  },
  {
    matchArea: MatchArea.DEFENCE,
    possibilities: [
      {
        description: 'Pass back (goal keeper)',
        commentaryId: 'PASS_BACK',
        probability: 0.1,
        targetArea: MatchArea.DEFENCE,
        positiveAttributes: ['passing'],
        negativeAttributes: ['marking'],
        negativeOutcomes: passNegativeOutcomes,
      },
      {
        description: 'Pass sideways (defence)',
        commentaryId: 'PASS_SIDEWAYS',
        probability: 0.15,
        targetArea: MatchArea.DEFENCE,
        positiveAttributes: ['passing'],
        negativeAttributes: ['tackling'],
        negativeOutcomes: passNegativeOutcomes,
      },
      {
        description: 'Pass forwards (midfield)',
        commentaryId: 'PASS_FORWARD',
        probability: 0.35,
        targetArea: MatchArea.MIDFIELD,
        positiveAttributes: ['passing'],
        negativeAttributes: ['heading'],
        negativeOutcomes: passNegativeOutcomes,
      },
      {
        description: 'Long ball forwards (attack)',
        commentaryId: 'LONG_BALL',
        probability: 0.1,
        targetArea: MatchArea.ATTACK,
        positiveAttributes: ['passing'],
        negativeAttributes: ['heading'],
        negativeOutcomes: passNegativeOutcomes,
      },
      {
        description: 'Carry ball forwards (midfield)',
        commentaryId: 'CARRY_FORWARDS',
        probability: 0.15,
        targetArea: MatchArea.MIDFIELD,
        positiveAttributes: ['ballControl', 'pace'],
        negativeAttributes: ['tackling'],
        negativeOutcomes: runNegativeOutcomes,
      },
      {
        description: 'Carry ball sideways (defence)',
        commentaryId: 'CARRY_SIDEWAYS',
        probability: 0.15,
        targetArea: MatchArea.DEFENCE,
        positiveAttributes: ['ballControl', 'pace'],
        negativeAttributes: ['tackling'],
        negativeOutcomes: runNegativeOutcomes,
      },
    ],
  },
  {
    matchArea: MatchArea.ATTACK,
    possibilities: [
      {
        description: 'Pass back (midfield)',
        commentaryId: 'PASS_BACK',
        probability: 0.2,
        targetArea: MatchArea.MIDFIELD,
        positiveAttributes: ['passing'],
        negativeAttributes: ['marking'],
        negativeOutcomes: passNegativeOutcomes,
      },
      {
        description: 'Pass sideways (attack)',
        commentaryId: 'PASS_SIDEWAYS',
        probability: 0.3,
        targetArea: MatchArea.ATTACK,
        positiveAttributes: ['passing'],
        negativeAttributes: ['tackling'],
        negativeOutcomes: passNegativeOutcomes,
      },
      {
        description: 'Shoot',
        commentaryId: 'SHOOT',
        probability: 0.2,
        targetArea: MatchArea.SHOT,
        positiveAttributes: ['passing'],
        negativeAttributes: ['reflexes', 'shotStopping'],
        negativeOutcomes: shotNegativeOutcomes,
      },
      {
        description: 'Carry ball sideways (attack)',
        commentaryId: 'CARRY_SIDEWAYS',
        probability: 0.25,
        targetArea: MatchArea.ATTACK,
        positiveAttributes: ['ballControl', 'pace'],
        negativeAttributes: ['tackling'],
        negativeOutcomes: runNegativeOutcomes,
      },
      {
        description: 'Carry ball backwards (midfield)',
        commentaryId: 'CARRY_SIDEWAYS',
        probability: 0.05,
        targetArea: MatchArea.MIDFIELD,
        positiveAttributes: ['ballControl', 'pace'],
        negativeAttributes: ['tackling'],
        negativeOutcomes: runNegativeOutcomes,
      },
    ],
  },
  {
    matchArea: MatchArea.THROW_IN,
    possibilities: [
      {
        description: 'Pass (midfield)',
        commentaryId: 'PASS_BACK',
        probability: 0.4,
        targetArea: MatchArea.MIDFIELD,
        positiveAttributes: ['vision', 'passing'],
        negativeAttributes: ['marking'],
        negativeOutcomes: passNegativeOutcomes,
      },
      {
        description: 'Pass (Defence)',
        commentaryId: 'PASS_BACK',
        probability: 0.3,
        targetArea: MatchArea.DEFENCE,
        positiveAttributes: ['vision', 'passing'],
        negativeAttributes: ['tackling'],
        negativeOutcomes: passNegativeOutcomes,
      },
      {
        description: 'Pass (Attack)',
        commentaryId: 'PASS_FORWARD',
        probability: 0.3,
        targetArea: MatchArea.ATTACK,
        positiveAttributes: ['vision', 'passing'],
        negativeAttributes: ['tackling', 'heading'],
        negativeOutcomes: passNegativeOutcomes,
      },
    ],
  },
  {
    matchArea: MatchArea.GOAL_KICK,
    possibilities: [
      {
        description: 'Pass (midfield)',
        commentaryId: 'GOAL_KICK',
        probability: 0.4,
        targetArea: MatchArea.MIDFIELD,
        positiveAttributes: ['vision', 'passing'],
        negativeAttributes: ['heading', 'marking'],
        negativeOutcomes: passNegativeOutcomes,
      },
      {
        description: 'Pass (Defence)',
        commentaryId: 'GOAL_KICK',
        probability: 0.3,
        targetArea: MatchArea.DEFENCE,
        positiveAttributes: ['passing'],
        negativeAttributes: ['tackling', 'marking'],
        negativeOutcomes: passNegativeOutcomes,
      },
      {
        description: 'Long Ball (Attack)',
        commentaryId: 'LONG_BALL',
        probability: 0.3,
        targetArea: MatchArea.ATTACK,
        positiveAttributes: ['vision', 'passing'],
        negativeAttributes: ['marking', 'heading'],
        negativeOutcomes: passNegativeOutcomes,
      },
    ],
  },
  {
    matchArea: MatchArea.CORNER,
    possibilities: [
      {
        description: 'Pass short (attack)',
        commentaryId: 'CORNER_SHORT',
        probability: 0.33,
        targetArea: MatchArea.ATTACK,
        positiveAttributes: ['passing'],
        negativeAttributes: ['marking'],
        negativeOutcomes: passNegativeOutcomes,
      },
      {
        description: 'Near post cross (attack)',
        commentaryId: 'CORNER_MID',
        probability: 0.33,
        targetArea: MatchArea.ATTACK,
        positiveAttributes: ['crossing'],
        negativeAttributes: ['marking', 'heading'],
        negativeOutcomes: passNegativeOutcomes,
      },
      {
        description: 'Far post cross (attack)',
        commentaryId: 'CORNER_FAR',
        probability: 0.34,
        targetArea: MatchArea.ATTACK,
        positiveAttributes: ['crossing'],
        negativeAttributes: ['marking', 'heading'],
        negativeOutcomes: passNegativeOutcomes,
      },
    ],
  },
  {
    matchArea: MatchArea.ATTACKING_FREE_KICK,
    possibilities: [
      {
        description: 'Pass short (attack)',
        commentaryId: 'ATTACKING_FREE_KICK',
        probability: 0.33,
        targetArea: MatchArea.ATTACK,
        positiveAttributes: ['passing'],
        negativeAttributes: ['marking'],
        negativeOutcomes: passNegativeOutcomes,
      },
      {
        description: 'Cross into area (attack)',
        commentaryId: 'ATTACKING_FREE_KICK',
        probability: 0.33,
        targetArea: MatchArea.ATTACK,
        positiveAttributes: ['crossing'],
        negativeAttributes: ['marking', 'heading'],
        negativeOutcomes: passNegativeOutcomes,
      },
      {
        description: 'Shot on goal',
        commentaryId: 'SHOOT',
        probability: 0.34,
        targetArea: MatchArea.SHOT,
        positiveAttributes: ['finishing'],
        negativeAttributes: ['reflexes', 'shotStopping'],
        negativeOutcomes: shotNegativeOutcomes,
      },
    ],
  },
  {
    matchArea: MatchArea.MIDFIELD_FREE_KICK,
    possibilities: [
      {
        description: 'Pass back (defence)',
        commentaryId: 'MIDFIELD_FREE_KICK',
        probability: 0.33,
        targetArea: MatchArea.DEFENCE,
        positiveAttributes: ['passing'],
        negativeAttributes: ['marking'],
        negativeOutcomes: passNegativeOutcomes,
      },
      {
        description: 'Pass sideways (midfield)',
        commentaryId: 'MIDFIELD_FREE_KICK',
        probability: 0.33,
        targetArea: MatchArea.MIDFIELD,
        positiveAttributes: ['passing'],
        negativeAttributes: ['tackling'],
        negativeOutcomes: passNegativeOutcomes,
      },
      {
        description: 'Pass forwards (attack)',
        commentaryId: 'MIDFIELD_FREE_KICK',
        probability: 0.34,
        targetArea: MatchArea.ATTACK,
        positiveAttributes: ['passing'],
        negativeAttributes: ['heading'],
        negativeOutcomes: passNegativeOutcomes,
      },
    ],
  },
  {
    matchArea: MatchArea.DEFENDING_FREE_KICK,
    possibilities: [
      {
        description: 'Pass back (goal keeper)',
        commentaryId: 'DEFENDING_FREE_KICK',
        probability: 0.05,
        targetArea: MatchArea.DEFENCE,
        positiveAttributes: ['passing'],
        negativeAttributes: ['marking'],
        negativeOutcomes: passNegativeOutcomes,
      },
      {
        description: 'Pass sideways (defence)',
        commentaryId: 'DEFENDING_FREE_KICK',
        probability: 0.15,
        targetArea: MatchArea.DEFENCE,
        positiveAttributes: ['passing'],
        negativeAttributes: ['tackling'],
        negativeOutcomes: passNegativeOutcomes,
      },
      {
        description: 'Pass forwards (midfield)',
        commentaryId: 'DEFENDING_FREE_KICK',
        probability: 0.4,
        targetArea: MatchArea.MIDFIELD,
        positiveAttributes: ['passing'],
        negativeAttributes: ['heading'],
        negativeOutcomes: passNegativeOutcomes,
      },
      {
        description: 'Long ball forwards (attack)',
        commentaryId: 'LONG_BALL',
        probability: 0.4,
        targetArea: MatchArea.ATTACK,
        positiveAttributes: ['passing'],
        negativeAttributes: ['heading'],
        negativeOutcomes: passNegativeOutcomes,
      },
    ],
  },
];
