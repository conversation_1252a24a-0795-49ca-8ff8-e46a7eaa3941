import { Player } from '@/entities/Player.js';
import { MatchEvent, MatchStats } from '@/model/fixture.js';
import { PlayerMatchStats } from '@/model/player.js';
import { LeagueStandings } from '@/model/team.js';

export interface GamePlayer {
  player: Player;
  stats: PlayerMatchStats;
  joinedMatchMinute?: number;
  leftMatchMinute?: number;
  rating?: number; // 1-10
  sentOff?: number; // minute sent off
  injured?: number; // minute injured
  goals?: number[]; // minutes goals scored
  assists?: number[]; // minutes assists made
  substitute?: GamePlayer;
}

export interface NextEvent {
  matchArea: MatchArea;
  teamInPossession: number;
  playerInPossession: GamePlayer | null;
}

export interface Team {
  gameworldId: string;
  teamId: string;
  teamName: string;
  standings: LeagueStandings;
  players: GamePlayer[];
}

export enum MatchArea {
  KICK_OFF = 'KICK_OFF',
  MIDFIELD = 'MIDFIELD',
  DEFENCE = 'DEFENCE',
  ATTACK = 'ATTACK',
  GOAL_KICK = 'GOAL_KICK',
  CORNER = 'CORNER',
  ATTACKING_FREE_KICK = 'ATTACKING_FREE_KICK',
  MIDFIELD_FREE_KICK = 'MIDFIELD_FREE_KICK',
  DEFENDING_FREE_KICK = 'DEFENDING_FREE_KICK',
  SHOT = 'SHOT',
  THROW_IN = 'THROW_IN',
}

export interface SimulationResult {
  stats: MatchStats;
  events: MatchEvent[];
}
