import { MatchStats } from '@/model/fixture.js';
import { PlayerMatchStats } from '@/model/player.js';
import { NegativeOutcome } from '@/simulation/event-outcomes.js';
import { Possibility } from './match-events.js';
import { GamePlayer, MatchArea } from './types.js';

export class StatsCalculator {
  private readonly stats: MatchStats;
  constructor() {
    this.stats = {
      possession: [0, 0],
      shots: [0, 0],
      shotsOnTarget: [0, 0],
      corners: [0, 0],
      fouls: [0, 0],
      yellowCards: [0, 0],
      redCards: [0, 0],
      passes: [0, 0],
      passAccuracy: [0, 0],
      tackles: [0, 0],
      interceptions: [0, 0],
      score: [0, 0],
      scorers: [],
    };
  }

  updateMatchStats({
    event,
    teamInPossession,
    matchArea,
    negativeOutcome,
  }: {
    event: Possibility;
    teamInPossession: number;
    matchArea: MatchArea;
    negativeOutcome?: NegativeOutcome;
  }) {
    // Update possession counter
    this.stats.possession[teamInPossession]!++;

    // Track event-specific stats
    if (
      event.description.toLowerCase().includes('pass') ||
      event.description.toLowerCase().includes('long ball') ||
      event.description.toLowerCase().includes('cross')
    ) {
      this.stats.passes[teamInPossession]!++;
      if (!negativeOutcome) {
        // Successful pass
        this.stats.passAccuracy[teamInPossession]!++;
      }
    } else if (matchArea === MatchArea.SHOT) {
      this.stats.shots[teamInPossession]!++;
      if (
        (negativeOutcome && negativeOutcome.description.toLowerCase().includes('saved')) ||
        !negativeOutcome
      ) {
        this.stats.shotsOnTarget[teamInPossession]!++;
      }
    } else if (negativeOutcome && negativeOutcome.targetArea === MatchArea.CORNER) {
      this.stats.corners[teamInPossession]!++;
    }
    if (negativeOutcome && negativeOutcome.description.toLowerCase().includes('foul')) {
      this.stats.fouls[1 - teamInPossession]!++;
      if (negativeOutcome.description.toLowerCase().includes('yellow')) {
        this.stats.yellowCards[1 - teamInPossession]!++;
      } else if (negativeOutcome.description.toLowerCase().includes('red')) {
        this.stats.redCards[1 - teamInPossession]!++;
      }
    }
  }

  updatePlayerStats({
    playerInPossession,
    oppositionPlayer,
    playerStats,
    oppositionPlayerStats,
    event,
    negativeOutcome,
    minute,
  }: {
    playerInPossession: GamePlayer;
    oppositionPlayer: GamePlayer;
    playerStats: PlayerMatchStats;
    oppositionPlayerStats: PlayerMatchStats;
    event: Possibility;
    negativeOutcome?: NegativeOutcome;
    minute: number;
  }): void {
    // Track event-specific stats
    if (
      event.description.toLowerCase().includes('pass') ||
      event.description.toLowerCase().includes('long ball') ||
      event.description.toLowerCase().includes('cross')
    ) {
      playerStats.passesAttempted++;
      if (!negativeOutcome) {
        // Successful pass
        playerStats.passesCompleted++;
      }
    } else if (event.description.toLowerCase().includes('carry')) {
      playerStats.ballCarriesAttempted++;
      if (!negativeOutcome) {
        playerStats.successfulBallCarries++;
      }
    } else if (event.targetArea === MatchArea.SHOT) {
      playerStats.shots++;
      if (!negativeOutcome) {
        playerStats.shotsOnTarget++;
      } else if (negativeOutcome.description.toLowerCase().includes('saved')) {
        oppositionPlayerStats.saves++;
      }
    }
    if (negativeOutcome) {
      // Failed pass
      if (negativeOutcome.description.toLowerCase().includes('blocked')) {
        oppositionPlayerStats.tackles++;
      } else if (negativeOutcome.description.toLowerCase().includes('foul')) {
        oppositionPlayerStats.fouls++;
        if (negativeOutcome.description.toLowerCase().includes('yellow')) {
          oppositionPlayerStats.yellowCards++;
          if (oppositionPlayerStats.yellowCards === 2) {
            oppositionPlayerStats.redCards++;
            oppositionPlayer.sentOff = minute;
            oppositionPlayer.rating = Math.max(1, (oppositionPlayer.rating || 5) - 1);
          }
        } else if (negativeOutcome.description.toLowerCase().includes('red')) {
          oppositionPlayerStats.redCards++;
          oppositionPlayer.sentOff = minute;
          oppositionPlayer.rating = Math.max(1, (oppositionPlayer.rating || 5) - 1);
        }
      }
    }
  }

  getStats(): MatchStats {
    return this.stats;
  }

  goalScored(
    teamInPossession: number,
    scorerId: string,
    scorerSurname: string,
    minute: number,
    half: number
  ): [number, number] {
    this.stats.score[teamInPossession]!++;

    if (this.stats.scorers!.some((scorer) => scorer.playerId === scorerId)) {
      this.stats
        .scorers!.find((scorer) => scorer.playerId === scorerId)!
        .goalTime.push({ minute, half });
    } else {
      this.stats.scorers!.push({
        playerId: scorerId,
        playerName: scorerSurname,
        team: teamInPossession,
        goalTime: [
          {
            minute,
            half,
          },
        ],
      });
    }
    return this.stats.score;
  }
}
