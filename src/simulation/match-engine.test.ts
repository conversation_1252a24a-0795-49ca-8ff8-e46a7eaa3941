import { MatchEngine } from '@/simulation/match-engine.js';
import { MatchValidator } from '@/simulation/utils/match-validator.js';
import { TeamBuilder } from '@/simulation/utils/team-builder.js';
import { setRandomSeed } from '@/utils/seeded-random.js';
import { beforeEach, describe, expect, it } from 'vitest';

describe('MatchEngine (End-to-End)', () => {
  let homeTeam: any;
  let awayTeam: any;

  beforeEach(() => {
    setRandomSeed(12345);
    homeTeam = TeamBuilder.createStrongTeam('home-1', 'Home FC', 'gameworld-1');
    awayTeam = TeamBuilder.createStrongTeam('away-1', 'Away United', 'gameworld-1');
  });

  describe('basic simulation', () => {
    it('should complete a full match simulation', () => {
      const matchEngine = new MatchEngine(homeTeam, awayTeam);

      const result = matchEngine.simulate();

      expect(result).toBeDefined();
      expect(result.stats).toBeDefined();
      expect(result.events).toBeDefined();
    });

    it('should produce valid match results', () => {
      const matchEngine = new MatchEngine(homeTeam, awayTeam);

      const result = matchEngine.simulate();
      const validation = MatchValidator.validateMatch(result, homeTeam, awayTeam);

      expect(validation.isValid).toBe(true);
      if (validation.errors.length > 0) {
        console.log('Validation errors:', validation.errors);
      }
    });

    it('should have consistent results with same seed', () => {
      setRandomSeed(12345);
      const homeTeam1 = TeamBuilder.createStrongTeam('home-1', 'Home FC', 'gameworld-1');
      const awayTeam1 = TeamBuilder.createStrongTeam('away-1', 'Away United', 'gameworld-1');
      // make sure teams are reset
      setRandomSeed(12345);
      const homeTeam2 = TeamBuilder.createStrongTeam('home-1', 'Home FC', 'gameworld-1');
      const awayTeam2 = TeamBuilder.createStrongTeam('away-1', 'Away United', 'gameworld-1');

      expect(homeTeam1).toEqual(homeTeam2);

      setRandomSeed(54321);
      const matchEngine1 = new MatchEngine(homeTeam1, awayTeam1);
      const result1 = matchEngine1.simulate();

      setRandomSeed(54321);
      const matchEngine2 = new MatchEngine(homeTeam2, awayTeam2);
      const result2 = matchEngine2.simulate();

      expect(result1.stats.score).toEqual(result2.stats.score);
      expect(result1.events.length).toBe(result2.events.length);
    });

    it('should have different results with different seeds', () => {
      setRandomSeed(11111);
      const homeTeam1 = TeamBuilder.createStrongTeam('home-1', 'Home FC', 'gameworld-1');
      const awayTeam1 = TeamBuilder.createStrongTeam('away-1', 'Away United', 'gameworld-1');
      const matchEngine1 = new MatchEngine(homeTeam1, awayTeam1);
      const result1 = matchEngine1.simulate();

      setRandomSeed(22222);
      const homeTeam2 = TeamBuilder.createStrongTeam('home-1', 'Home FC', 'gameworld-1');
      const awayTeam2 = TeamBuilder.createStrongTeam('away-1', 'Away United', 'gameworld-1');
      const matchEngine2 = new MatchEngine(homeTeam2, awayTeam2);
      const result2 = matchEngine2.simulate();

      // Results should be different (very unlikely to be identical)
      const sameScore =
        result1.stats.score[0] === result2.stats.score[0] &&
        result1.stats.score[1] === result2.stats.score[1];
      const sameEventCount = result1.events.length === result2.events.length;

      expect(sameScore && sameEventCount).toBe(false);
    });
  });

  describe('team strength impact', () => {
    it('should favor strong teams over weak teams', () => {
      let strongWins = 0;
      let totalGames = 0;

      // Simulate multiple matches to test probability
      for (let i = 0; i < 20; i++) {
        setRandomSeed(i * 1000);

        // Create fresh teams for each match to avoid mutation issues
        const strongTeam = TeamBuilder.createStrongTeam('strong', 'Strong FC', 'gw1', {
          min: 30,
          max: 35,
        });
        const weakTeam = TeamBuilder.createWeakTeam('weak', 'Weak FC', 'gw1', { min: 5, max: 10 });

        const matchEngine = new MatchEngine(strongTeam, weakTeam);
        const result = matchEngine.simulate();

        totalGames++;
        if (result.stats.score[0] > result.stats.score[1]) {
          strongWins++;
        }
      }

      // Strong team should win majority of games (at least 60%)
      const winRate = strongWins / totalGames;
      expect(winRate).toBeGreaterThan(0.6);
    });

    it('should produce reasonable score differences', () => {
      const strongTeam = TeamBuilder.createStrongTeam('strong', 'Strong FC', 'gw1', {
        min: 32,
        max: 36,
      });
      const weakTeam = TeamBuilder.createWeakTeam('weak', 'Weak FC', 'gw1', { min: 3, max: 8 });

      const matchEngine = new MatchEngine(strongTeam, weakTeam);
      const result = matchEngine.simulate();

      // Score should be reasonable (not 20-0) - allow for some high-scoring games
      expect(result.stats.score[0]).toBeLessThanOrEqual(15);
      expect(result.stats.score[1]).toBeLessThanOrEqual(15);
      expect(result.stats.score[0] + result.stats.score[1]).toBeLessThanOrEqual(20);
    });
  });

  describe('match statistics', () => {
    it('should generate realistic possession statistics', () => {
      const matchEngine = new MatchEngine(homeTeam, awayTeam);
      const result = matchEngine.simulate();

      const totalPossession = result.stats.possession[0] + result.stats.possession[1];
      const homePossession = (result.stats.possession[0] / totalPossession) * 100;
      const awayPossession = (result.stats.possession[1] / totalPossession) * 100;

      expect(homePossession).toBeGreaterThan(20);
      expect(homePossession).toBeLessThan(80);
      expect(awayPossession).toBeGreaterThan(20);
      expect(awayPossession).toBeLessThan(80);
      expect(Math.abs(homePossession + awayPossession - 100)).toBeLessThan(1);
    });

    it('should generate reasonable shot statistics', () => {
      const matchEngine = new MatchEngine(homeTeam, awayTeam);
      const result = matchEngine.simulate();

      // Each team should have some shots
      expect(result.stats.shots[0]).toBeGreaterThan(0);
      expect(result.stats.shots[1]).toBeGreaterThan(0);

      // Shots on target should be less than or equal to total shots
      expect(result.stats.shotsOnTarget[0]).toBeLessThanOrEqual(result.stats.shots[0]);
      expect(result.stats.shotsOnTarget[1]).toBeLessThanOrEqual(result.stats.shots[1]);

      // Goals should be less than or equal to shots on target
      expect(result.stats.score[0]).toBeLessThanOrEqual(result.stats.shotsOnTarget[0]);
      expect(result.stats.score[1]).toBeLessThanOrEqual(result.stats.shotsOnTarget[1]);
    });

    it('should track fouls and cards appropriately', () => {
      const matchEngine = new MatchEngine(homeTeam, awayTeam);
      const result = matchEngine.simulate();

      // Should have some fouls in a match
      expect(result.stats.fouls[0] + result.stats.fouls[1]).toBeGreaterThan(0);

      // Yellow cards should be reasonable
      expect(result.stats.yellowCards[0]).toBeLessThanOrEqual(5);
      expect(result.stats.yellowCards[1]).toBeLessThanOrEqual(5);

      // Red cards should be rare
      expect(result.stats.redCards[0]).toBeLessThanOrEqual(2);
      expect(result.stats.redCards[1]).toBeLessThanOrEqual(2);
    });
  });

  describe('substitutions', () => {
    it('should make substitutions when players are exhausted', () => {
      // Create fresh teams for this test
      const testHomeTeam = TeamBuilder.createStrongTeam('home-1', 'Home FC', 'gameworld-1');
      const testAwayTeam = TeamBuilder.createStrongTeam('away-1', 'Away United', 'gameworld-1');

      // Set some players to low energy
      testHomeTeam.players[5]!.player.energy = 20;
      testHomeTeam.players[7]!.player.energy = 15;
      testAwayTeam.players[3]!.player.energy = 25;

      const matchEngine = new MatchEngine(testHomeTeam, testAwayTeam);
      matchEngine.simulate();

      // Check that substitutions were made
      const homeSubstitutions = testHomeTeam.players.filter((p: any) => p.substitute !== undefined);
      const awaySubstitutions = testAwayTeam.players.filter((p: any) => p.substitute !== undefined);

      expect(homeSubstitutions.length).toBeGreaterThan(0);
      expect(homeSubstitutions.length).toBeLessThanOrEqual(3);
      expect(awaySubstitutions.length).toBeLessThanOrEqual(3);
    });

    it('should not exceed maximum substitutions', () => {
      // Create fresh teams for this test
      const testHomeTeam = TeamBuilder.createStrongTeam('home-1', 'Home FC', 'gameworld-1');
      const testAwayTeam = TeamBuilder.createStrongTeam('away-1', 'Away United', 'gameworld-1');

      // Set many players to very low energy
      for (let i = 1; i <= 8; i++) {
        testHomeTeam.players[i]!.player.energy = 5;
        testAwayTeam.players[i]!.player.energy = 5;
      }

      const matchEngine = new MatchEngine(testHomeTeam, testAwayTeam);
      matchEngine.simulate();

      const homeSubstitutions = testHomeTeam.players.filter((p: any) => p.substitute !== undefined);
      const awaySubstitutions = testAwayTeam.players.filter((p: any) => p.substitute !== undefined);

      expect(homeSubstitutions.length).toBeLessThanOrEqual(3);
      expect(awaySubstitutions.length).toBeLessThanOrEqual(3);
    });
  });

  describe('match events', () => {
    it('should include required match events', () => {
      const matchEngine = new MatchEngine(homeTeam, awayTeam);
      const result = matchEngine.simulate();

      const eventTypes = result.events.map((e) => e.localisationId);

      expect(eventTypes).toContain('KICK_OFF');
      expect(eventTypes).toContain('HALF_TIME');
      expect(eventTypes).toContain('FULL_TIME');
    });

    it('should have events with valid timing', () => {
      const matchEngine = new MatchEngine(homeTeam, awayTeam);
      const result = matchEngine.simulate();

      for (const event of result.events) {
        expect(event.minute).toBeGreaterThanOrEqual(0);
        expect(event.minute).toBeLessThanOrEqual(50);
        expect(event.half).toBeGreaterThanOrEqual(1);
        expect(event.half).toBeLessThanOrEqual(2);
      }
    });

    it('should have goal events matching the score', () => {
      const matchEngine = new MatchEngine(homeTeam, awayTeam);
      const result = matchEngine.simulate();

      const goalEvents = result.events.filter((e) => e.localisationId === 'GOAL');
      const totalGoals = result.stats.score[0] + result.stats.score[1];

      expect(goalEvents.length).toBe(totalGoals);
    });
  });

  describe('player performance', () => {
    it('should assign ratings to players', () => {
      const matchEngine = new MatchEngine(homeTeam, awayTeam);
      matchEngine.simulate();

      // Check that starting players have ratings
      for (let i = 0; i < 11; i++) {
        expect(homeTeam.players[i].rating).toBeDefined();
        expect(homeTeam.players[i].rating).toBeGreaterThanOrEqual(1);
        expect(homeTeam.players[i].rating).toBeLessThanOrEqual(10);

        expect(awayTeam.players[i].rating).toBeDefined();
        expect(awayTeam.players[i].rating).toBeGreaterThanOrEqual(1);
        expect(awayTeam.players[i].rating).toBeLessThanOrEqual(10);
      }
    });

    it('should track player statistics', () => {
      const matchEngine = new MatchEngine(homeTeam, awayTeam);
      matchEngine.simulate();

      // Check that some players have statistics
      const allPlayers = [...homeTeam.players, ...awayTeam.players];
      const playersWithStats = allPlayers.filter(
        (p: any) => p.stats.passesAttempted > 0 || p.stats.tackles > 0 || p.stats.shots > 0
      );

      expect(playersWithStats.length).toBeGreaterThan(0);
    });
  });

  describe('edge cases', () => {
    it('should handle teams with minimum players', () => {
      // Create fresh teams for this test
      const testHomeTeam = TeamBuilder.createStrongTeam('home-1', 'Home FC', 'gameworld-1');
      const testAwayTeam = TeamBuilder.createStrongTeam('away-1', 'Away United', 'gameworld-1');

      testHomeTeam.players = testHomeTeam.players.slice(0, 11); // Only 11 players
      testAwayTeam.players = testAwayTeam.players.slice(0, 11);

      const matchEngine = new MatchEngine(testHomeTeam, testAwayTeam);

      expect(() => matchEngine.simulate()).not.toThrow();
    });

    it('should handle very low energy players', () => {
      // Create fresh teams for this test
      const testHomeTeam = TeamBuilder.createStrongTeam('home-1', 'Home FC', 'gameworld-1');
      const testAwayTeam = TeamBuilder.createStrongTeam('away-1', 'Away United', 'gameworld-1');

      // Set all players to very low energy
      testHomeTeam.players.forEach((p: any) => {
        p.player.energy = 1;
      });
      testAwayTeam.players.forEach((p: any) => {
        p.player.energy = 1;
      });

      const matchEngine = new MatchEngine(testHomeTeam, testAwayTeam);

      expect(() => matchEngine.simulate()).not.toThrow();
    });
  });
});
