import { MatchArea } from '@/simulation/types.js';

export interface NegativeOutcome {
  description: string;
  targetArea?: MatchArea;
  probability: number;
  noPossessionLost?: boolean;
  commentaryId: string;
}

export const shotNegativeOutcomes: NegativeOutcome[] = [
  {
    description: 'Player hits frame of goal. Ball falls to opposition',
    probability: 0.15,
    targetArea: MatchArea.DEFENCE,
    commentaryId: 'OUTCOME_SHOT_WOODWORK_OPP',
  },
  {
    description: 'Player hits frame of goal. Ball falls to same team',
    probability: 0.15,
    noPossessionLost: true,
    targetArea: MatchArea.ATTACK,
    commentaryId: 'OUTCOME_SHOT_WOODWORK_SAFE',
  },
  {
    description: 'Shot is saved and keeper pushes it out. Ball falls to opposition',
    probability: 0.2,
    targetArea: MatchArea.DEFENCE,
    commentaryId: 'OUTCOME_SHOT_SAVED_OPP',
  },
  {
    description: 'Shot is saved and keeper pushes it out. Ball falls to same team',
    probability: 0.2,
    noPossessionLost: true,
    targetArea: MatchArea.ATTACK,
    commentaryId: 'OUTCOME_SHOT_SAVED_SAFE',
  },
  {
    description: 'Shot is caught by goalkeeper',
    probability: 0.2,
    targetArea: MatchArea.GOAL_KICK,
    commentaryId: 'OUTCOME_SHOT_CAUGHT',
  },
  {
    description: 'Shot goes out for a corner',
    probability: 0.1,
    noPossessionLost: true,
    targetArea: MatchArea.CORNER,
    commentaryId: 'OUTCOME_SHOT_CORNER',
  },
];

export const passNegativeOutcomes = [
  {
    description: 'Pass is blocked and loses possession',
    probability: 0.491,
    commentaryId: 'OUTCOME_PASS_BLOCKED_OPP',
  },
  {
    description: 'Pass is blocked but ball falls to another player on same team',
    probability: 0.27,
    noPossessionLost: true,
    commentaryId: 'OUTCOME_PASS_BLOCKED_SAFE',
  },
  {
    description: 'Player is fouled and wins free kick - no card',
    probability: 0.09,
    noPossessionLost: true,
    commentaryId: 'OUTCOME_PASS_FOUL_NOCARD',
  },
  {
    description: 'Player is fouled and wins free kick - yellow card',
    probability: 0.027,
    noPossessionLost: true,
    commentaryId: 'OUTCOME_PASS_FOUL_YELLOWCARD',
  },
  {
    description: 'Player is fouled and wins free kick - red card',
    probability: 0.004,
    noPossessionLost: true,
    commentaryId: 'OUTCOME_PASS_FOUL_REDCARD',
  },
  {
    description: 'Player misplaces pass and goes out for a throw in',
    probability: 0.118,
    targetArea: MatchArea.THROW_IN,
    commentaryId: 'OUTCOME_PASS_THROWIN',
  },
];

export const runNegativeOutcomes = [
  {
    description: 'Player is successfully tackled and loses possession',
    probability: 0.409,
    commentaryId: 'OUTCOME_CARRY_TACKLE_OPP',
  },
  {
    description: 'Player is tackled but ball falls to another player on same team',
    probability: 0.227,
    noPossessionLost: true,
    commentaryId: 'OUTCOME_CARRY_TACKLE_SAFE',
  },
  {
    description: 'Player is fouled and wins free kick - no card',
    probability: 0.109,
    noPossessionLost: true,
    commentaryId: 'OUTCOME_CARRY_FOUL_NOCARD',
  },
  {
    description: 'Player is fouled and wins free kick - yellow card',
    probability: 0.055,
    noPossessionLost: true,
    commentaryId: 'OUTCOME_CARRY_FOUL_YELLOWCARD',
  },
  {
    description: 'Player is fouled and wins free kick - red card',
    probability: 0.018,
    noPossessionLost: true,
    commentaryId: 'OUTCOME_CARRY_FOUL_REDCARD',
  },
  {
    description: 'Player trips over his own shoe laces and loses possession',
    probability: 0.045,
    commentaryId: 'OUTCOME_CARRY_TRIP',
  },
  {
    description: 'Player is tackled and ball goes out for a throw in',
    probability: 0.136,
    targetArea: MatchArea.THROW_IN,
    commentaryId: 'OUTCOME_CARRY_THROWIN',
  },
];
