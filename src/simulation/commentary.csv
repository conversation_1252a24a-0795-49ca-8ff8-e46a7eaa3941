ID,en
PASS_BACK,{player} passes back to {nextPlayer}
PASS_SIDEWAYS,{player} passes to {nextPlayer}
PASS_FORWARD_1,{player} passes forwards to {nextPlayer}
PASS_FORWARD_2,{player} threads the ball through to {nextPlayer}
CARRY_FORWARDS_1,{player} advances with the ball
CARRY_FORWARDS_2,{player} is going on a mazy run
CARRY_FORWARDS_3,{player} looking to take players on
CARRY_SIDEWAYS,{player} keeps possession
LOB_KEEPER_1,Oh my! {player} tries to lob the keeper from distance!
LOB_KEEPER_2,{player} has seen {nextPlayer} off his line and goes for a chip!
LONG_BALL_1,{player} plays the long ball forward to {nextPlayer}
LONG_BALL_2,{player} goes route one to {nextPlayer}
SHOOT_1,Shot from {player}
SHOOT_2,{player} has a shot on goal
THROW_IN,{player} with the throw in
GOAL_KICK,{player} takes the goal kick
CORNER_SHORT,{player} takes a short corner to {nextPlayer}
CORNER_MID,{player} aims the corner at the near post
CORNER_FAR,{player} aims the corner towards the back post
ATTACKING_FREE_KICK,{player} wins a free kick in a promising position
MIDFIELD_FREE_KICK,{player} wins a free kick in the middle of the pitch
DEFENCE_FREE_KICK,{player} wins a free kick in their own half
OUTCOME_SHOT_WOODWORK_OPP,The ball cannons of the woodwork and falls to {oppPlayer} for {oppTeam}
OUTCOME_SHOT_WOODWORK_SAFE,The ball cannons of the woodwork and falls to {player} for {team}
OUTCOME_SHOT_SAVED_OPP,Great save by the keeper but they can't keep hold of it. The ball falls to {oppPlayer} for {oppTeam}
OUTCOME_SHOT_SAVED_SAFE,Great save by the keeper but they can't keep hold of it. The ball falls to {player} and {team} are still on the attack
OUTCOME_SHOT_CAUGHT,Great save by {oppPlayer}
OUTCOME_SHOT_CORNER,The ball takes a deflection and goes out for a corner
OUTCOME_PASS_BLOCKED_OPP,The pass is cut out by {oppPlayer} and {oppTeam} have the ball
OUTCOME_PASS_BLOCKED_SAFE,{oppPlayer} gets in the way of the pass but the ball falls to {nextPlayer} and {team} keep possession
OUTCOME_PASS_FOUL_NOCARD,{oppPlayer} challenges for the ball with {nextPlayer} but the referee blows his whistle. Foul
OUTCOME_PASS_FOUL_YELLOWCARD_1,{oppPlayer} makes the tackle but it's late. The referee blows his whistle and awards a yellow card.
OUTCOME_PASS_FOUL_YELLOWCARD_2,Cynical foul by {oppPlayer}. The referee has no choice but to award a yellow card.
OUTCOME_PASS_FOUL_REDCARD_1,Ouch. That looked painful. {oppPlayer} with a reckless tackle on {nextPlayer}. Red card!
OUTCOME_PASS_FOUL_REDCARD_2,Thats a really high foot from {oppPlayer}. Red card! They're off!
OUTCOME_PASS_FOUL_REDCARD_3,What's happened here? {oppPlayer} and {player} are facing off. Oh! {oppPlayer} has nutted {player}! And he's sent back to the dressing room. Red card and his game is over.
OUTCOME_PASS_THROWIN,Terrible ball from {player} and it goes out for a throw in
OUTCOME_CARRY_TACKLE_1,Great tackle fom {oppPlayer} and {player}s run comes to a shuddering halt
OUTCOME_CARRY_TACKLE_2,Clean tackle fom {oppPlayer} and {oppTeam} regain possession
OUTCOME_CARRY_TACKLE_SAFE,Good tackle from {oppPlayer} but the ball falls to {nextPlayer} and {team} keep possession
OUTCOME_CARRY_FOUL_NOCARD,{oppPlayer} makes the tackle but the referee blows his whistle. Foul
OUTCOME_CARRY_FOUL_YELLOWCARD_1,{oppPlayer} makes the tackle but it's late. The referee blows his whistle and awards a yellow card.
OUTCOME_CARRY_FOUL_YELLOWCARD_2,Cynical foul by {oppPlayer}. The referee has no choice but to award a yellow card.
OUTCOME_CARRY_FOUL_REDCARD_1,Big oof! {oppPlayer} goes straight through {player} and is nowhere near the ball. Red card!
OUTCOME_CARRY_FOUL_REDCARD_2,Thats a really high foot from {oppPlayer}. Red card! They're off!
OUTCOME_CARRY_FOUL_REDCARD_3,What's happened here? {oppPlayer} and {player} are facing off. Oh! {oppPlayer} has nutted {player}! And he's sent back to the dressing room. Red card and his game is over.
OUTCOME_CARRY_TRIP,Well that's embarassing. {player} has tripped over their own feet. {oppTeam} have the ball
OUTCOME_CARRY_THROWIN,"Under pressure from {oppPlayer}, {player} knocks the ball out for a throw in"
KICK_OFF,{player} gets the half underway for {team}
HALF_TIME,The referee blows his whistle for half time. The score is {homeTeam} {homeScore} - {awayTeam} {awayScore}
FULL_TIME,That's the end of the game. It finishes {homeTeam} {homeScore} - {awayTeam} {awayScore}
GOAL_1,What a strike from {player}! {homeTeam} {homeScore} - {awayTeam} {awayScore}
GOAL_2,Cooly finished by {player}! {homeTeam} {homeScore} - {awayTeam} {awayScore}
GOAL_3,{player} with a thunder blast from the edge of the area! {homeTeam} {homeScore} - {awayTeam} {awayScore}
GOAL_4,And it goes in off the shins of {player}! They all count! {homeTeam} {homeScore} - {awayTeam} {awayScore}
SUBSTITUTION,Substitution for {team}. {player} is replaced by {nextPlayer}
DEFENDING_FREE_KICK,{player} to take the free kick for {team} in their own third
INJURY,Looks like {player} has picked up an injury