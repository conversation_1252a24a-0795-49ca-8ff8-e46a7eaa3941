// Define notification categories
export enum NotificationCategory {
  TRANSFERS = 'transfers',
  TRAINING = 'training',
  PRE_MATCH = 'preMatch',
  SCOUTING_RESULTS = 'scoutingResults',
  POST_MATCH = 'postMatch',
  SYSTEM_ANNOUNCEMENTS = 'announcements',
}

// Define notification channels
export enum NotificationChannel {
  PUSH = 'push',
  EMAIL = 'email',
}

// Notification preferences type
export type NotificationPreferences = {
  [key in NotificationCategory]?: {
    [channel in NotificationChannel]?: boolean;
  };
};
