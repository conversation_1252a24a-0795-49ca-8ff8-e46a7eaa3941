import { Migration } from '@mikro-orm/migrations';

export class Migration20250801195115 extends Migration {

  override async up(): Promise<void> {
    this.addSql(`alter table "manager" add column "is_guest" boolean not null default false, add column "guest_device_id" varchar(255) null, add column "migrated_from_guest_id" varchar(255) null, add column "migration_completed" boolean not null default false;`);
  }

  override async down(): Promise<void> {
    this.addSql(`alter table "manager" drop column "is_guest", drop column "guest_device_id", drop column "migrated_from_guest_id", drop column "migration_completed";`);
  }

}
