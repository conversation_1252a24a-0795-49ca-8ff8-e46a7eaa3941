import { Migration } from '@mikro-orm/migrations';

export class Migration20250623210115 extends Migration {
  override async up(): Promise<void> {
    this.addSql(
      `alter table "player_attributes" add column "is_goalkeeper" boolean not null default false;`
    );
  }

  override async down(): Promise<void> {
    this.addSql(`alter table "player_attributes" drop column "is_goalkeeper";`);
  }
}
