import { Migration } from '@mikro-orm/migrations';

export class Migration20250605191649 extends Migration {
  override async up(): Promise<void> {
    this.addSql(
      `alter table "player_match_history" drop constraint "player_match_history_player_id_foreign";`
    );
    this.addSql(
      `alter table "player_match_history" add constraint "player_match_history_player_id_foreign" foreign key ("player_id") references "players" ("player_id") on delete cascade;`
    );
  }

  override async down(): Promise<void> {
    this.addSql(
      `alter table "player_match_history" drop constraint "player_match_history_player_id_foreign";`
    );
    this.addSql(
      `alter table "player_match_history" add constraint "player_match_history_player_id_foreign" foreign key ("player_id") references "players" ("player_id") on update cascade;`
    );
  }
}
