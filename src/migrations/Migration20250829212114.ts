import { Migration } from '@mikro-orm/migrations';

export class Migration20250829212114 extends Migration {

  override async up(): Promise<void> {
    this.addSql(`alter table "archived_players" drop constraint "archived_players_archived_team_archived_team_id_foreign";`);

    this.addSql(`alter table "archived_players" alter column "archived_team_archived_team_id" drop default;`);
    this.addSql(`alter table "archived_players" alter column "archived_team_archived_team_id" type uuid using ("archived_team_archived_team_id"::text::uuid);`);
    this.addSql(`alter table "archived_players" alter column "archived_team_archived_team_id" drop not null;`);
    this.addSql(`alter table "archived_players" add constraint "archived_players_archived_team_archived_team_id_foreign" foreign key ("archived_team_archived_team_id") references "archived_team" ("archived_team_id") on update cascade on delete set null;`);
    this.addSql(`create index "archived_players_archived_team_archived_team_id_index" on "archived_players" ("archived_team_archived_team_id");`);

    this.addSql(`alter table "transfer_list" alter column "created_at" type bigint using ("created_at"::bigint);`);
    this.addSql(`alter table "transfer_list" alter column "created_at" set default 1756502474637;`);
  }

  override async down(): Promise<void> {
    this.addSql(`alter table "archived_players" drop constraint "archived_players_archived_team_archived_team_id_foreign";`);

    this.addSql(`drop index "archived_players_archived_team_archived_team_id_index";`);

    this.addSql(`alter table "archived_players" alter column "archived_team_archived_team_id" drop default;`);
    this.addSql(`alter table "archived_players" alter column "archived_team_archived_team_id" type uuid using ("archived_team_archived_team_id"::text::uuid);`);
    this.addSql(`alter table "archived_players" alter column "archived_team_archived_team_id" set not null;`);
    this.addSql(`alter table "archived_players" add constraint "archived_players_archived_team_archived_team_id_foreign" foreign key ("archived_team_archived_team_id") references "archived_team" ("archived_team_id") on update cascade;`);

    this.addSql(`alter table "transfer_list" alter column "created_at" type bigint using ("created_at"::bigint);`);
    this.addSql(`alter table "transfer_list" alter column "created_at" set default 1756501524051;`);
  }

}
