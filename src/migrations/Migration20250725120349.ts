import { Migration } from '@mikro-orm/migrations';

export class Migration20250725120349 extends Migration {

  override async up(): Promise<void> {
    this.addSql(`alter table "manager" add column "wins" int not null default 0, add column "defeats" int not null default 0, add column "draws" int not null default 0, add column "goals_scored" int not null default 0, add column "goals_conceded" int not null default 0, add column "highest_transfer_paid" int not null default 0, add column "highest_transfer_received" int not null default 0, add column "trophies" int not null default 0;`);
  }

  override async down(): Promise<void> {
    this.addSql(`alter table "manager" drop column "wins", drop column "defeats", drop column "draws", drop column "goals_scored", drop column "goals_conceded", drop column "highest_transfer_paid", drop column "highest_transfer_received", drop column "trophies";`);
  }

}
