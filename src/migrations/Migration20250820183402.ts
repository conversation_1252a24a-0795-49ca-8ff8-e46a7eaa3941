import { Migration } from '@mikro-orm/migrations';

export class Migration20250820183402 extends Migration {

  override async up(): Promise<void> {
    this.addSql(`alter table "archived_team" drop constraint "archived_team_league_id_foreign";`);

    this.addSql(`drop index "idx_archived_teams_league";`);
  }

  override async down(): Promise<void> {
    this.addSql(`alter table "archived_team" add constraint "archived_team_league_id_foreign" foreign key ("league_id") references "league" ("id") on update cascade;`);
    this.addSql(`create index "idx_archived_teams_league" on "archived_team" ("league_id");`);
  }

}
