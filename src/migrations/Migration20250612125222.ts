import { Migration } from '@mikro-orm/migrations';

export class Migration20250612125222 extends Migration {

  override async up(): Promise<void> {
    this.addSql(`alter table "player_match_history" drop constraint "player_match_history_fixture_fixture_id_foreign";`);

    this.addSql(`alter table "player_match_history" alter column "fixture_fixture_id" drop default;`);
    this.addSql(`alter table "player_match_history" alter column "fixture_fixture_id" type uuid using ("fixture_fixture_id"::text::uuid);`);
    this.addSql(`alter table "player_match_history" alter column "fixture_fixture_id" drop not null;`);
    this.addSql(`alter table "player_match_history" add constraint "player_match_history_fixture_fixture_id_foreign" foreign key ("fixture_fixture_id") references "fixture" ("fixture_id") on delete cascade;`);
  }

  override async down(): Promise<void> {
    this.addSql(`alter table "player_match_history" drop constraint "player_match_history_fixture_fixture_id_foreign";`);

    this.addSql(`alter table "player_match_history" alter column "fixture_fixture_id" drop default;`);
    this.addSql(`alter table "player_match_history" alter column "fixture_fixture_id" type uuid using ("fixture_fixture_id"::text::uuid);`);
    this.addSql(`alter table "player_match_history" alter column "fixture_fixture_id" set not null;`);
    this.addSql(`alter table "player_match_history" add constraint "player_match_history_fixture_fixture_id_foreign" foreign key ("fixture_fixture_id") references "fixture" ("fixture_id") on update cascade;`);
  }

}
