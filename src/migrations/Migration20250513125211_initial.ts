import { Migration } from '@mikro-orm/migrations';

export class Migration20250513125211_initial extends Migration {
  override async up(): Promise<void> {
    this.addSql(`CREATE EXTENSION IF NOT EXISTS "uuid-ossp";`);

    this.addSql(
      `create table "available_team" ("id" uuid not null, "gameworld_id" uuid not null, "team_id" uuid not null, constraint "available_team_pkey" primary key ("id"));`
    );
    this.addSql(
      `alter table "available_team" add constraint "available_team_gameworld_id_team_id_unique" unique ("gameworld_id", "team_id");`
    );

    this.addSql(
      `create table "gameworld" ("id" uuid not null default uuid_generate_v4(), "end_date" bigint not null, constraint "gameworld_pkey" primary key ("id"));`
    );
    this.addSql(`alter table "gameworld" add constraint "gameworld_id_unique" unique ("id");`);

    this.addSql(
      `create table "league" ("id" uuid not null default uuid_generate_v4(), "gameworld_id" uuid not null, "name" varchar(100) not null, "tier" int not null, "parent_league_id" uuid null, constraint "league_pkey" primary key ("id"));`
    );
    this.addSql(`create index "idx_leagues_tier" on "league" ("tier");`);
    this.addSql(`create index "idx_leagues_parent" on "league" ("parent_league_id");`);
    this.addSql(`alter table "league" add constraint "league_id_unique" unique ("id");`);

    this.addSql(
      `create table "league_children" ("parent_league_id" uuid not null, "child_league_id" uuid not null, constraint "league_children_pkey" primary key ("parent_league_id", "child_league_id"));`
    );

    this.addSql(
      `create table "league_rules" ("league_id" uuid not null, "promotion_spots" int not null, "relegation_spots" int not null, "team_count" int not null, constraint "league_rules_pkey" primary key ("league_id"));`
    );

    this.addSql(
      `create table "team" ("team_id" uuid not null, "gameworld_id" uuid not null, "league_id" uuid not null, "tier" int not null, "team_name" varchar(100) not null, "manager_id" uuid null, "balance" int not null default 300000, "played" int not null default 0, "points" int not null default 0, "goals_for" int not null default 0, "goals_against" int not null default 0, "wins" int not null default 0, "draws" int not null default 0, "losses" int not null default 0, "selection_order" text[] not null, constraint "team_pkey" primary key ("team_id"));`
    );
    this.addSql(`create index "idx_teams_league" on "team" ("league_id");`);
    this.addSql(
      `alter table "team" add constraint "teams_gameworld_id_team_id_key" unique ("gameworld_id", "team_id");`
    );

    this.addSql(
      `create table "scouting_requests" ("request_id" uuid not null default uuid_generate_v4(), "gameworld_id" uuid not null, "team_team_id" uuid not null, "type" text check ("type" in ('player', 'team', 'league')) not null, "target_id" varchar(255) not null, "process_after" bigint not null, "processed_at" bigint null, "processed" boolean not null default false, "created_at" timestamptz not null default now(), constraint "scouting_requests_pkey" primary key ("request_id"));`
    );
    this.addSql(
      `alter table "scouting_requests" add constraint "scouting_requests_request_id_unique" unique ("request_id");`
    );

    this.addSql(
      `create table "players" ("player_id" uuid not null, "gameworld_id" uuid not null, "team_id" uuid null, "age" int not null, "seed" bigint not null, "first_name" varchar(255) not null, "surname" varchar(255) not null, "value" numeric(15,2) not null, "energy" int not null, "last_match_played" bigint not null, "injured_until" bigint null, "suspended_for_games" int not null, constraint "players_pkey" primary key ("player_id"));`
    );
    this.addSql(
      `alter table "players" add constraint "players_gameworld_id_player_id_key" unique ("gameworld_id", "player_id");`
    );

    this.addSql(
      `create table "scouted_players" ("team_team_id" uuid not null, "player_player_id" uuid not null, "gameworld_id" uuid not null, "scouted_at" bigint not null, constraint "scouted_players_pkey" primary key ("team_team_id", "player_player_id"));`
    );
    this.addSql(
      `alter table "scouted_players" add constraint "scouted_players_gameworld_id_team_team_id_player__1708a_unique" unique ("gameworld_id", "team_team_id", "player_player_id");`
    );

    this.addSql(
      `create table "player_overall_stats" ("player_id" uuid not null, "yellow_cards" int not null default 0, "red_cards" int not null default 0, "passes_completed" int not null default 0, "passes_attempted" int not null default 0, "successful_ball_carries" int not null default 0, "ball_carries_attempted" int not null default 0, "shots" int not null default 0, "shots_on_target" int not null default 0, "goals" int not null default 0, "saves" int not null default 0, "tackles" int not null default 0, "fouls" int not null default 0, constraint "player_overall_stats_pkey" primary key ("player_id"));`
    );

    this.addSql(
      `create table "player_attributes" ("player_id" uuid not null, "reflexes_current" int not null, "reflexes_potential" int not null, "positioning_current" int not null, "positioning_potential" int not null, "shot_stopping_current" int not null, "shot_stopping_potential" int not null, "tackling_current" int not null, "tackling_potential" int not null, "marking_current" int not null, "marking_potential" int not null, "heading_current" int not null, "heading_potential" int not null, "finishing_current" int not null, "finishing_potential" int not null, "pace_current" int not null, "pace_potential" int not null, "crossing_current" int not null, "crossing_potential" int not null, "passing_current" int not null, "passing_potential" int not null, "vision_current" int not null, "vision_potential" int not null, "ball_control_current" int not null, "ball_control_potential" int not null, "stamina" real not null, constraint "player_attributes_pkey" primary key ("player_id"));`
    );

    this.addSql(
      `create table "manager" ("manager_id" uuid not null, "created_at" bigint not null, "last_active" bigint not null, "first_name" varchar(100) null, "last_name" varchar(100) null, "email" varchar(255) null, "team_id" uuid null, "gameworld_id" uuid null, "scout_tokens" int not null default 0, "super_scout_tokens" int not null default 0, constraint "manager_pkey" primary key ("manager_id"));`
    );
    this.addSql(
      `alter table "manager" add constraint "manager_team_id_unique" unique ("team_id");`
    );
    this.addSql(`alter table "manager" add constraint "manager_id_key" unique ("manager_id");`);

    this.addSql(
      `create table "fixture" ("fixture_id" uuid not null, "gameworld_id" uuid not null, "league_id" uuid not null, "home_team_team_id" uuid not null, "away_team_team_id" uuid not null, "date" bigint not null, "stats_possession" text[] null, "stats_shots" text[] null, "stats_shots_on_target" text[] null, "stats_corners" text[] null, "stats_fouls" text[] null, "stats_yellow_cards" text[] null, "stats_red_cards" text[] null, "stats_passes" text[] null, "stats_pass_accuracy" text[] null, "stats_tackles" text[] null, "stats_interceptions" text[] null, "stats_score" text[] null, "stats_scorers" jsonb null, "events" jsonb null, "played" boolean not null default false, "simulated_at" bigint null, "seed" bigint null, constraint "fixture_pkey" primary key ("fixture_id"));`
    );
    this.addSql(
      `alter table "fixture" add constraint "fixture_gameworld_id_league_id_fixture_id_unique" unique ("gameworld_id", "league_id", "fixture_id");`
    );

    this.addSql(
      `create table "player_match_history" ("player_id" uuid not null, "fixture_id" uuid not null, "fixture_fixture_id" uuid not null, "yellow_cards" int not null default 0, "red_cards" int not null default 0, "passes_completed" int not null default 0, "passes_attempted" int not null default 0, "successful_ball_carries" int not null default 0, "ball_carries_attempted" int not null default 0, "shots" int not null default 0, "shots_on_target" int not null default 0, "goals" int not null default 0, "saves" int not null default 0, "tackles" int not null default 0, "fouls" int not null default 0, constraint "player_match_history_pkey" primary key ("player_id", "fixture_id"));`
    );

    this.addSql(
      `create table "transfer_list" ("id" uuid not null default uuid_generate_v4(), "player_id" uuid not null, "gameworld_id" uuid not null, "player_player_id" uuid not null, "auction_start_price" numeric(15,2) not null, "auction_current_price" numeric(15,2) not null, "auction_end_time" bigint not null, "auction_listing_counter" int not null default 0, "created_at" bigint not null, constraint "transfer_list_pkey" primary key ("id"));`
    );
    this.addSql(`create index "transfer_list_player_id_index" on "transfer_list" ("player_id");`);
    this.addSql(
      `create index "transfer_list_gameworld_id_index" on "transfer_list" ("gameworld_id");`
    );
    this.addSql(
      `create index "transfer_list_auction_end_time_index" on "transfer_list" ("auction_end_time");`
    );
    this.addSql(
      `alter table "transfer_list" add constraint "transfer_list_player_player_id_gameworld_id_unique" unique ("player_player_id", "gameworld_id");`
    );

    this.addSql(
      `create table "bid_history" ("id" uuid not null default uuid_generate_v4(), "transfer_listing_id" uuid not null, "team_id" uuid not null, "maximum_bid" numeric(15,2) not null, "bid_time" bigint not null, "is_winning_bid" boolean not null default false, constraint "bid_history_pkey" primary key ("id"));`
    );
    this.addSql(
      `create index "bid_history_transfer_listing_id_index" on "bid_history" ("transfer_listing_id");`
    );
    this.addSql(`create index "bid_history_team_id_index" on "bid_history" ("team_id");`);
    this.addSql(
      `alter table "bid_history" add constraint "bid_history_transfer_listing_id_team_id_unique" unique ("transfer_listing_id", "team_id");`
    );

    this.addSql(
      `create table "transfer_request" ("id" uuid not null default uuid_generate_v4(), "date" bigint not null, "player_id" uuid not null, "buyerTeam" uuid not null, "sellerTeam" uuid not null, "value" bigint not null, "counter_offer_time" bigint not null default 0, "counter_offer_value" bigint not null default 0, constraint "transfer_request_pkey" primary key ("id"));`
    );
    this.addSql(
      `alter table "transfer_request" add constraint "transfer_request_player_id_buyerTeam_unique" unique ("player_id", "buyerTeam");`
    );

    this.addSql(
      `alter table "league" add constraint "league_gameworld_id_foreign" foreign key ("gameworld_id") references "gameworld" ("id") on update cascade;`
    );
    this.addSql(
      `alter table "league" add constraint "league_parent_league_id_foreign" foreign key ("parent_league_id") references "league" ("id") on update cascade on delete set null;`
    );

    this.addSql(
      `alter table "league_children" add constraint "league_children_parent_league_id_foreign" foreign key ("parent_league_id") references "league" ("id") on update cascade on delete cascade;`
    );
    this.addSql(
      `alter table "league_children" add constraint "league_children_child_league_id_foreign" foreign key ("child_league_id") references "league" ("id") on update cascade on delete cascade;`
    );

    this.addSql(
      `alter table "league_rules" add constraint "league_rules_league_id_foreign" foreign key ("league_id") references "league" ("id") on update cascade on delete cascade;`
    );

    this.addSql(
      `alter table "team" add constraint "team_league_id_foreign" foreign key ("league_id") references "league" ("id") on update cascade;`
    );

    this.addSql(
      `alter table "scouting_requests" add constraint "scouting_requests_team_team_id_foreign" foreign key ("team_team_id") references "team" ("team_id") on update cascade;`
    );

    this.addSql(
      `alter table "players" add constraint "players_team_id_foreign" foreign key ("team_id") references "team" ("team_id") on update cascade on delete set null;`
    );

    this.addSql(
      `alter table "scouted_players" add constraint "scouted_players_team_team_id_foreign" foreign key ("team_team_id") references "team" ("team_id") on update cascade;`
    );
    this.addSql(
      `alter table "scouted_players" add constraint "scouted_players_player_player_id_foreign" foreign key ("player_player_id") references "players" ("player_id") on update cascade;`
    );

    this.addSql(
      `alter table "player_overall_stats" add constraint "player_overall_stats_player_id_foreign" foreign key ("player_id") references "players" ("player_id") on update cascade on delete cascade;`
    );

    this.addSql(
      `alter table "player_attributes" add constraint "player_attributes_player_id_foreign" foreign key ("player_id") references "players" ("player_id") on update cascade on delete cascade;`
    );

    this.addSql(
      `alter table "manager" add constraint "manager_team_id_foreign" foreign key ("team_id") references "team" ("team_id") on update cascade on delete set null;`
    );

    this.addSql(
      `alter table "fixture" add constraint "fixture_league_id_foreign" foreign key ("league_id") references "league" ("id") on update cascade;`
    );
    this.addSql(
      `alter table "fixture" add constraint "fixture_home_team_team_id_foreign" foreign key ("home_team_team_id") references "team" ("team_id") on update cascade;`
    );
    this.addSql(
      `alter table "fixture" add constraint "fixture_away_team_team_id_foreign" foreign key ("away_team_team_id") references "team" ("team_id") on update cascade;`
    );

    this.addSql(
      `alter table "player_match_history" add constraint "player_match_history_player_id_foreign" foreign key ("player_id") references "players" ("player_id") on update cascade;`
    );
    this.addSql(
      `alter table "player_match_history" add constraint "player_match_history_fixture_fixture_id_foreign" foreign key ("fixture_fixture_id") references "fixture" ("fixture_id") on update cascade;`
    );

    this.addSql(
      `alter table "transfer_list" add constraint "transfer_list_player_player_id_foreign" foreign key ("player_player_id") references "players" ("player_id") on update cascade;`
    );

    this.addSql(
      `alter table "bid_history" add constraint "bid_history_transfer_listing_id_foreign" foreign key ("transfer_listing_id") references "transfer_list" ("id") on update cascade;`
    );
    this.addSql(
      `alter table "bid_history" add constraint "bid_history_team_id_foreign" foreign key ("team_id") references "team" ("team_id") on update cascade;`
    );

    this.addSql(
      `alter table "transfer_request" add constraint "transfer_request_player_id_foreign" foreign key ("player_id") references "players" ("player_id") on update cascade;`
    );
    this.addSql(
      `alter table "transfer_request" add constraint "transfer_request_buyerTeam_foreign" foreign key ("buyerTeam") references "team" ("team_id") on update cascade;`
    );
    this.addSql(
      `alter table "transfer_request" add constraint "transfer_request_sellerTeam_foreign" foreign key ("sellerTeam") references "team" ("team_id") on update cascade;`
    );
  }

  override async down(): Promise<void> {
    this.addSql(`alter table "league" drop constraint "league_gameworld_id_foreign";`);

    this.addSql(`alter table "league" drop constraint "league_parent_league_id_foreign";`);

    this.addSql(
      `alter table "league_children" drop constraint "league_children_parent_league_id_foreign";`
    );

    this.addSql(
      `alter table "league_children" drop constraint "league_children_child_league_id_foreign";`
    );

    this.addSql(`alter table "league_rules" drop constraint "league_rules_league_id_foreign";`);

    this.addSql(`alter table "team" drop constraint "team_league_id_foreign";`);

    this.addSql(`alter table "fixture" drop constraint "fixture_league_id_foreign";`);

    this.addSql(
      `alter table "scouting_requests" drop constraint "scouting_requests_team_team_id_foreign";`
    );

    this.addSql(`alter table "players" drop constraint "players_team_id_foreign";`);

    this.addSql(
      `alter table "scouted_players" drop constraint "scouted_players_team_team_id_foreign";`
    );

    this.addSql(`alter table "manager" drop constraint "manager_team_id_foreign";`);

    this.addSql(`alter table "fixture" drop constraint "fixture_home_team_team_id_foreign";`);

    this.addSql(`alter table "fixture" drop constraint "fixture_away_team_team_id_foreign";`);

    this.addSql(`alter table "bid_history" drop constraint "bid_history_team_id_foreign";`);

    this.addSql(
      `alter table "transfer_request" drop constraint "transfer_request_buyerTeam_foreign";`
    );

    this.addSql(
      `alter table "transfer_request" drop constraint "transfer_request_sellerTeam_foreign";`
    );

    this.addSql(
      `alter table "scouted_players" drop constraint "scouted_players_player_player_id_foreign";`
    );

    this.addSql(
      `alter table "player_overall_stats" drop constraint "player_overall_stats_player_id_foreign";`
    );

    this.addSql(
      `alter table "player_attributes" drop constraint "player_attributes_player_id_foreign";`
    );

    this.addSql(
      `alter table "player_match_history" drop constraint "player_match_history_player_id_foreign";`
    );

    this.addSql(
      `alter table "transfer_list" drop constraint "transfer_list_player_player_id_foreign";`
    );

    this.addSql(
      `alter table "transfer_request" drop constraint "transfer_request_player_id_foreign";`
    );

    this.addSql(
      `alter table "player_match_history" drop constraint "player_match_history_fixture_fixture_id_foreign";`
    );

    this.addSql(
      `alter table "bid_history" drop constraint "bid_history_transfer_listing_id_foreign";`
    );

    this.addSql(`drop table if exists "available_team" cascade;`);

    this.addSql(`drop table if exists "gameworld" cascade;`);

    this.addSql(`drop table if exists "league" cascade;`);

    this.addSql(`drop table if exists "league_children" cascade;`);

    this.addSql(`drop table if exists "league_rules" cascade;`);

    this.addSql(`drop table if exists "team" cascade;`);

    this.addSql(`drop table if exists "scouting_requests" cascade;`);

    this.addSql(`drop table if exists "players" cascade;`);

    this.addSql(`drop table if exists "scouted_players" cascade;`);

    this.addSql(`drop table if exists "player_overall_stats" cascade;`);

    this.addSql(`drop table if exists "player_attributes" cascade;`);

    this.addSql(`drop table if exists "manager" cascade;`);

    this.addSql(`drop table if exists "fixture" cascade;`);

    this.addSql(`drop table if exists "player_match_history" cascade;`);

    this.addSql(`drop table if exists "transfer_list" cascade;`);

    this.addSql(`drop table if exists "bid_history" cascade;`);

    this.addSql(`drop table if exists "transfer_request" cascade;`);
  }
}
