import { Migration } from '@mikro-orm/migrations';

export class Migration20250731202646 extends Migration {

  override async up(): Promise<void> {
    this.addSql(`alter table "player_match_history" alter column "match_rating" drop default;`);
    this.addSql(`alter table "player_match_history" alter column "match_rating" type real using ("match_rating"::real);`);
    this.addSql(`alter table "player_match_history" alter column "match_rating" drop not null;`);
  }

  override async down(): Promise<void> {
    this.addSql(`alter table "player_match_history" alter column "match_rating" type real using ("match_rating"::real);`);
    this.addSql(`alter table "player_match_history" alter column "match_rating" set default 0;`);
    this.addSql(`alter table "player_match_history" alter column "match_rating" set not null;`);
  }

}
