import { Migration } from '@mikro-orm/migrations';

export class Migration20250605124949 extends Migration {

  override async up(): Promise<void> {
    // Drop the foreign key constraint first
    this.addSql(`alter table "player_match_history" drop constraint "player_match_history_player_id_foreign";`);

    // Add the foreign key constraint back with cascade delete
    this.addSql(`alter table "player_match_history" add constraint "player_match_history_player_id_foreign" foreign key ("player_id") references "players" ("player_id") on delete cascade;`);
  }

  override async down(): Promise<void> {
    // Drop the foreign key constraint
    this.addSql(`alter table "player_match_history" drop constraint "player_match_history_player_id_foreign";`);

    // Add the foreign key constraint back without cascade delete
    this.addSql(`alter table "player_match_history" add constraint "player_match_history_player_id_foreign" foreign key ("player_id") references "players" ("player_id") on update cascade;`);
  }

}
