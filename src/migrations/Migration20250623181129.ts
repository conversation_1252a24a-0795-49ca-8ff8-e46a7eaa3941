import { Migration } from '@mikro-orm/migrations';

export class Migration20250623181129 extends Migration {

  override async up(): Promise<void> {
    this.addSql(`alter table "transfer_request" drop constraint "transfer_request_player_id_foreign";`);

    this.addSql(`alter table "team" drop column "manager_id";`);

    this.addSql(`alter table "transfer_request" alter column "player_id" drop default;`);
    this.addSql(`alter table "transfer_request" alter column "player_id" type uuid using ("player_id"::text::uuid);`);
    this.addSql(`alter table "transfer_request" alter column "player_id" set not null;`);
    this.addSql(`alter table "transfer_request" add constraint "transfer_request_player_id_foreign" foreign key ("player_id") references "players" ("player_id") on update cascade;`);
  }

  override async down(): Promise<void> {
    this.addSql(`alter table "transfer_request" drop constraint "transfer_request_player_id_foreign";`);

    this.addSql(`alter table "team" add column "manager_id" uuid null;`);

    this.addSql(`alter table "transfer_request" alter column "player_id" drop default;`);
    this.addSql(`alter table "transfer_request" alter column "player_id" type uuid using ("player_id"::text::uuid);`);
    this.addSql(`alter table "transfer_request" alter column "player_id" drop not null;`);
    this.addSql(`alter table "transfer_request" add constraint "transfer_request_player_id_foreign" foreign key ("player_id") references "players" ("player_id") on delete cascade;`);
  }

}
