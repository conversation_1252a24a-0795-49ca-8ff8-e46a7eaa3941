import { Migration } from '@mikro-orm/migrations';

export class Migration20250601200228 extends Migration {

  override async up(): Promise<void> {
    this.addSql(`alter table "team_training_slots" alter column "id" drop default;`);
    this.addSql(`alter table "team_training_slots" alter column "id" type uuid using ("id"::text::uuid);`);
    this.addSql(`alter table "team_training_slots" alter column "id" set default uuid_generate_v4();`);
  }

  override async down(): Promise<void> {
    this.addSql(`alter table "team_training_slots" alter column "id" type text using ("id"::text);`);

    this.addSql(`alter table "team_training_slots" alter column "id" drop default;`);
    this.addSql(`alter table "team_training_slots" alter column "id" type varchar(255) using ("id"::varchar(255));`);
  }

}
