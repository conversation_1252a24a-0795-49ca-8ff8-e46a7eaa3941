import { Migration } from '@mikro-orm/migrations';

export class Migration20250823083906 extends Migration {

  override async up(): Promise<void> {
    this.addSql(`alter table "archived_players" drop constraint "archived_players_gameworld_id_player_id_key";`);
    this.addSql(`alter table "archived_players" add constraint "archived_players_gameworld_id_archived_player_id_key" unique ("gameworld_id", "archived_player_id");`);

    this.addSql(`alter table "archived_player_attributes" drop constraint "archived_player_attributes_pkey";`);
    this.addSql(`alter table "archived_player_attributes" drop column "archived_player_id";`);

    this.addSql(`alter table "archived_player_attributes" add column "player_id" uuid null;`);
    this.addSql(`alter table "archived_player_attributes" add constraint "archived_player_attributes_player_id_foreign" foreign key ("player_id") references "archived_players" ("archived_player_id") on delete cascade;`);
    this.addSql(`alter table "archived_player_attributes" add constraint "archived_player_attributes_pkey" primary key ("player_id");`);

    this.addSql(`alter table "archived_player_overall_stats" drop constraint "archived_player_overall_stats_pkey";`);
    this.addSql(`alter table "archived_player_overall_stats" drop column "archived_player_id";`);

    this.addSql(`alter table "archived_player_overall_stats" add column "player_id" uuid not null;`);
    this.addSql(`alter table "archived_player_overall_stats" add constraint "archived_player_overall_stats_player_id_foreign" foreign key ("player_id") references "archived_players" ("archived_player_id") on update cascade on delete cascade;`);
    this.addSql(`alter table "archived_player_overall_stats" add constraint "archived_player_overall_stats_pkey" primary key ("player_id");`);
  }

  override async down(): Promise<void> {
    this.addSql(`alter table "archived_player_attributes" drop constraint "archived_player_attributes_player_id_foreign";`);

    this.addSql(`alter table "archived_player_overall_stats" drop constraint "archived_player_overall_stats_player_id_foreign";`);

    this.addSql(`alter table "archived_players" drop constraint "archived_players_gameworld_id_archived_player_id_key";`);
    this.addSql(`alter table "archived_players" add constraint "archived_players_gameworld_id_player_id_key" unique ("gameworld_id", "archived_player_id");`);

    this.addSql(`alter table "archived_player_attributes" drop constraint "archived_player_attributes_pkey";`);
    this.addSql(`alter table "archived_player_attributes" drop column "player_id";`);

    this.addSql(`alter table "archived_player_attributes" add column "archived_player_id" uuid not null;`);
    this.addSql(`alter table "archived_player_attributes" add constraint "archived_player_attributes_pkey" primary key ("archived_player_id");`);

    this.addSql(`alter table "archived_player_overall_stats" drop constraint "archived_player_overall_stats_pkey";`);
    this.addSql(`alter table "archived_player_overall_stats" drop column "player_id";`);

    this.addSql(`alter table "archived_player_overall_stats" add column "archived_player_id" uuid not null;`);
    this.addSql(`alter table "archived_player_overall_stats" add constraint "archived_player_overall_stats_pkey" primary key ("archived_player_id");`);
  }

}
