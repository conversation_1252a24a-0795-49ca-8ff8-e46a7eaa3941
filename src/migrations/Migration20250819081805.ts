import { Migration } from '@mikro-orm/migrations';

export class Migration20250819081805 extends Migration {

  override async up(): Promise<void> {
    this.addSql(`create table "archived_team" ("archived_team_id" uuid not null, "gameworld_id" uuid not null, "archived_at" bigint not null, "league_id" uuid not null, "tier" int not null, "team_name" varchar(100) not null, "balance" int not null default 0, "played" int not null default 0, "points" int not null default 0, "goals_for" int not null default 0, "goals_against" int not null default 0, "wins" int not null default 0, "draws" int not null default 0, "losses" int not null default 0, "selection_order" text[] not null, "training_level" int not null default 1, "original_team_id" uuid not null, constraint "archived_team_pkey" primary key ("archived_team_id"));`);
    this.addSql(`create index "archived_team_gameworld_id_index" on "archived_team" ("gameworld_id");`);
    this.addSql(`create index "idx_archived_teams_league" on "archived_team" ("league_id");`);
    this.addSql(`alter table "archived_team" add constraint "archived_teams_gameworld_id_team_id_key" unique ("gameworld_id", "archived_team_id");`);

    this.addSql(`create table "archived_players" ("archived_player_id" uuid not null, "gameworld_id" uuid not null, "archived_at" bigint not null, "archived_team_id" uuid null, "age" int not null, "seed" bigint not null, "first_name" varchar(255) not null, "surname" varchar(255) not null, "value" numeric(15,2) not null, "energy" int not null, "last_match_played" bigint not null, "injured_until" bigint null, "suspended_for_games" int not null, "is_transfer_listed" boolean not null default false, "retiring_at_end_of_season" boolean not null default false, "original_player_id" uuid not null, constraint "archived_players_pkey" primary key ("archived_player_id"));`);
    this.addSql(`create index "archived_players_archived_team_id_index" on "archived_players" ("archived_team_id");`);
    this.addSql(`alter table "archived_players" add constraint "archived_players_gameworld_id_player_id_key" unique ("gameworld_id", "archived_player_id");`);

    this.addSql(`create table "archived_player_overall_stats" ("archived_player_id" uuid not null, "archived_at" bigint not null, "yellow_cards" int not null default 0, "red_cards" int not null default 0, "passes_completed" int not null default 0, "passes_attempted" int not null default 0, "successful_ball_carries" int not null default 0, "ball_carries_attempted" int not null default 0, "shots" int not null default 0, "shots_on_target" int not null default 0, "goals" int not null default 0, "saves" int not null default 0, "tackles" int not null default 0, "fouls" int not null default 0, constraint "archived_player_overall_stats_pkey" primary key ("archived_player_id"));`);

    this.addSql(`create table "archived_player_attributes" ("archived_player_id" uuid null, "archived_at" bigint not null, "is_goalkeeper" boolean not null default false, "reflexes_current" float not null, "reflexes_potential" float not null, "positioning_current" float not null, "positioning_potential" float not null, "shot_stopping_current" float not null, "shot_stopping_potential" float not null, "tackling_current" float not null, "tackling_potential" float not null, "marking_current" float not null, "marking_potential" float not null, "heading_current" float not null, "heading_potential" float not null, "finishing_current" float not null, "finishing_potential" float not null, "pace_current" float not null, "pace_potential" float not null, "crossing_current" float not null, "crossing_potential" float not null, "passing_current" float not null, "passing_potential" float not null, "vision_current" float not null, "vision_potential" float not null, "ball_control_current" float not null, "ball_control_potential" float not null, "stamina" real not null, constraint "archived_player_attributes_pkey" primary key ("archived_player_id"));`);

    this.addSql(`create table "archived_manager" ("manager_id" varchar(255) not null, "created_at" bigint not null, "last_active" bigint not null, "archived_at" bigint not null, "first_name" varchar(100) null, "last_name" varchar(100) null, "email" varchar(255) null, "archived_team_id" uuid null, "gameworld_id" uuid null, "scout_tokens" int not null default 2, "super_scout_tokens" int not null default 0, "magic_sponges" int not null default 1, "card_appeals" int not null default 0, "training_boosts" int not null default 0, "notification_preferences" jsonb null, "push_token" varchar(255) null, "login_streak" int not null default 0, "role" varchar(255) not null default 'user', "changed_team_name" boolean not null default false, "wins" int not null default 0, "defeats" int not null default 0, "is_guest" boolean not null default false, "migrated_from_guest_id" varchar(255) null, "migration_completed" boolean not null default false, "draws" int not null default 0, "goals_scored" int not null default 0, "goals_conceded" int not null default 0, "highest_transfer_paid" int not null default 0, "highest_transfer_received" int not null default 0, "trophies" int not null default 0, "original_team_id" uuid null, constraint "archived_manager_pkey" primary key ("manager_id"));`);
    this.addSql(`alter table "archived_manager" add constraint "archived_manager_archived_team_id_unique" unique ("archived_team_id");`);
    this.addSql(`alter table "archived_manager" add constraint "archived_manager_id_key" unique ("manager_id");`);

    this.addSql(`alter table "archived_team" add constraint "archived_team_league_id_foreign" foreign key ("league_id") references "league" ("id") on update cascade;`);

    this.addSql(`alter table "archived_players" add constraint "archived_players_archived_team_id_foreign" foreign key ("archived_team_id") references "archived_team" ("archived_team_id") on update cascade on delete set null;`);

    this.addSql(`alter table "archived_player_overall_stats" add constraint "archived_player_overall_stats_archived_player_id_foreign" foreign key ("archived_player_id") references "archived_players" ("archived_player_id") on update cascade on delete cascade;`);

    this.addSql(`alter table "archived_player_attributes" add constraint "archived_player_attributes_archived_player_id_foreign" foreign key ("archived_player_id") references "archived_players" ("archived_player_id") on delete cascade;`);

    this.addSql(`alter table "archived_manager" add constraint "archived_manager_archived_team_id_foreign" foreign key ("archived_team_id") references "archived_team" ("archived_team_id") on update cascade on delete set null;`);

    this.addSql(`alter table "team_training_slots" alter column "start_value" type real using ("start_value"::real);`);
  }

  override async down(): Promise<void> {
    this.addSql(`alter table "archived_players" drop constraint "archived_players_archived_team_id_foreign";`);

    this.addSql(`alter table "archived_manager" drop constraint "archived_manager_archived_team_id_foreign";`);

    this.addSql(`alter table "archived_player_overall_stats" drop constraint "archived_player_overall_stats_archived_player_id_foreign";`);

    this.addSql(`alter table "archived_player_attributes" drop constraint "archived_player_attributes_archived_player_id_foreign";`);

    this.addSql(`drop table if exists "archived_team" cascade;`);

    this.addSql(`drop table if exists "archived_players" cascade;`);

    this.addSql(`drop table if exists "archived_player_overall_stats" cascade;`);

    this.addSql(`drop table if exists "archived_player_attributes" cascade;`);

    this.addSql(`drop table if exists "archived_manager" cascade;`);

    this.addSql(`alter table "team_training_slots" alter column "start_value" type int using ("start_value"::int);`);
  }

}
