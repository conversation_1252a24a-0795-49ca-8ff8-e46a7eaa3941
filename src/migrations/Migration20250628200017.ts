import { Migration } from '@mikro-orm/migrations';

export class Migration20250628200017 extends Migration {

  override async up(): Promise<void> {
    this.addSql(`alter table "fixture" alter column "score" type text[] using ("score"::text[]);`);
    this.addSql(`alter table "fixture" alter column "score" drop not null;`);
  }

  override async down(): Promise<void> {
    this.addSql(`alter table "fixture" alter column "score" type text[] using ("score"::text[]);`);
    this.addSql(`alter table "fixture" alter column "score" set not null;`);
  }

}
