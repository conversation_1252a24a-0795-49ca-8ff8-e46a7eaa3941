import { Migration } from '@mikro-orm/migrations';

export class Migration20250802073820 extends Migration {
  override async up(): Promise<void> {
    // Drop the foreign key constraint first
    this.addSql(`alter table "purchases" drop constraint "purchases_manager_id_foreign";`);

    // Change the column types
    this.addSql(
      `alter table "manager" alter column "manager_id" type text using ("manager_id"::text);`
    );
    this.addSql(
      `alter table "purchases" alter column "manager_id" type text using ("manager_id"::text);`
    );

    // Drop the column after type changes
    this.addSql(`alter table "manager" drop column "guest_device_id";`);

    // Change to varchar(255) if needed
    this.addSql(
      `alter table "manager" alter column "manager_id" type varchar(255) using ("manager_id"::varchar(255));`
    );
    this.addSql(
      `alter table "purchases" alter column "manager_id" type varchar(255) using ("manager_id"::varchar(255));`
    );

    // Re-add the foreign key constraint
    this.addSql(
      `alter table "purchases" add constraint "purchases_manager_id_foreign" foreign key ("manager_id") references "manager" ("manager_id") on update cascade;`
    );
  }

  override async down(): Promise<void> {
    this.addSql(`alter table "purchases" drop constraint "purchases_manager_id_foreign";`);

    this.addSql(`alter table "manager" add column "guest_device_id" varchar(255) null;`);
    this.addSql(`alter table "manager" alter column "manager_id" drop default;`);
    this.addSql(
      `alter table "manager" alter column "manager_id" type uuid using ("manager_id"::text::uuid);`
    );

    this.addSql(`alter table "purchases" alter column "manager_id" drop default;`);
    this.addSql(
      `alter table "purchases" alter column "manager_id" type uuid using ("manager_id"::text::uuid);`
    );
    this.addSql(
      `alter table "purchases" add constraint "purchases_manager_id_foreign" foreign key ("manager_id") references "manager" ("manager_id") on update cascade;`
    );
  }
}
