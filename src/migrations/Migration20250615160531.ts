import { Migration } from '@mikro-orm/migrations';

export class Migration20250615160531 extends Migration {

  override async up(): Promise<void> {
    this.addSql(`create table "transactions" ("id" uuid not null default uuid_generate_v4(), "gameworld_id" uuid not null, "team_id" uuid not null, "date" bigint not null, "amount" numeric(15,2) not null, "type" text not null, "details" jsonb not null, constraint "transactions_pkey" primary key ("id"));`);

    this.addSql(`alter table "transactions" add constraint "transactions_team_id_foreign" foreign key ("team_id") references "team" ("team_id") on update cascade;`);
  }

  override async down(): Promise<void> {
    this.addSql(`drop table if exists "transactions" cascade;`);
  }

}
