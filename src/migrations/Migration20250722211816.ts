import { Migration } from '@mikro-orm/migrations';

export class Migration20250722211816 extends Migration {
  override async up(): Promise<void> {
    this.addSql(
      `create table "purchases" ("id" uuid not null default uuid_generate_v4(), "manager_id" uuid not null, "product_id" varchar(255) not null, "transaction_id" varchar(255) not null, "original_transaction_id" varchar(255) not null, "revenue_cat_event_id" varchar(255) not null, "store" varchar(255) not null, "environment" varchar(255) not null, "purchase_type" text check ("purchase_type" in ('SUBSCRIPTION', 'NON_RENEWING', 'CONSUMABLE')) not null, "status" text check ("status" in ('ACTIVE', 'EXPIRED', 'CANCELLED', 'PAUSED', 'REFUNDED', 'BILLING_ISSUE')) not null, "purchased_at" bigint not null, "expiration_at" bigint null, "currency" varchar(255) not null, "price" numeric(10,2) not null, "price_in_purchased_currency" numeric(10,2) not null, "country_code" varchar(255) null, "entitlement_ids" jsonb null, "cancel_reason" varchar(255) null, "expiration_reason" varchar(255) null, "created_at" bigint not null, "updated_at" bigint not null, "raw_event_data" jsonb null, constraint "purchases_pkey" primary key ("id"));`
    );
    this.addSql(
      `alter table "purchases" add constraint "purchases_transaction_id_key" unique ("transaction_id");`
    );

    this.addSql(
      `alter table "purchases" add constraint "purchases_manager_id_foreign" foreign key ("manager_id") references "manager" ("manager_id") on update cascade;`
    );

    this.addSql(
      `create index "bid_history_transfer_listing_id_team_id_bid_time_index" on "bid_history" ("transfer_listing_id", "team_id", "bid_time");`
    );
  }

  override async down(): Promise<void> {
    this.addSql(`drop table if exists "purchases" cascade;`);

    this.addSql(`drop index "bid_history_transfer_listing_id_team_id_bid_time_index";`);
  }
}
