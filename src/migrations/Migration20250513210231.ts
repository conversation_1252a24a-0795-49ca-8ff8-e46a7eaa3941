import { Migration } from '@mikro-orm/migrations';

export class Migration20250513210231 extends Migration {
  override async up(): Promise<void> {
    // Check if the column exists before trying to add it
    this.addSql(`
      DO $$
      BEGIN
        IF NOT EXISTS (
          SELECT 1 FROM information_schema.columns
          WHERE table_name = 'players' AND column_name = 'is_transfer_listed'
        ) THEN
          ALTER TABLE "players" ADD COLUMN "is_transfer_listed" boolean NOT NULL DEFAULT false;
        END IF;
      END
      $$;
    `);
  }

  override async down(): Promise<void> {
    // Only drop the column if it exists
    this.addSql(`
      DO $$
      BEGIN
        IF EXISTS (
          SELECT 1 FROM information_schema.columns
          WHERE table_name = 'players' AND column_name = 'is_transfer_listed'
        ) THEN
          ALTER TABLE "players" DROP COLUMN "is_transfer_listed";
        END IF;
      END
      $$;
    `);
  }
}
