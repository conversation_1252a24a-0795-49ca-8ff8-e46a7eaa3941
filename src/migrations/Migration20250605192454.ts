import { Migration } from '@mikro-orm/migrations';

export class Migration20250605192454 extends Migration {
  override async up(): Promise<void> {
    this.addSql(
      `alter table "scouted_players" drop constraint "scouted_players_player_player_id_foreign";`
    );
    this.addSql(
      `alter table "scouted_players" add constraint "scouted_players_player_player_id_foreign" foreign key ("player_player_id") references "players" ("player_id") on delete cascade;`
    );

    this.addSql(
      `alter table "player_attributes" drop constraint "player_attributes_player_id_foreign";`
    );
    this.addSql(
      `alter table "player_attributes" add constraint "player_attributes_player_id_foreign" foreign key ("player_id") references "players" ("player_id") on delete cascade;`
    );

    this.addSql(
      `alter table "team_training_slots" drop constraint "team_training_slots_player_player_id_foreign";`
    );
    this.addSql(
      `alter table "team_training_slots" add constraint "team_training_slots_player_player_id_foreign" foreign key ("player_player_id") references "players" ("player_id") on delete cascade;`
    );

    this.addSql(
      `alter table "transfer_request" drop constraint "transfer_request_player_id_foreign";`
    );
    this.addSql(
      `alter table "transfer_request" add constraint "transfer_request_player_id_foreign" foreign key ("player_id") references "players" ("player_id") on delete cascade;`
    );
  }

  override async down(): Promise<void> {
    this.addSql(
      `alter table "scouted_players" drop constraint "scouted_players_player_player_id_foreign";`
    );
    this.addSql(
      `alter table "scouted_players" add constraint "scouted_players_player_player_id_foreign" foreign key ("player_player_id") references "players" ("player_id") on update cascade;`
    );

    this.addSql(
      `alter table "player_attributes" drop constraint "player_attributes_player_id_foreign";`
    );
    this.addSql(
      `alter table "player_attributes" add constraint "player_attributes_player_id_foreign" foreign key ("player_id") references "players" ("player_id") on update cascade;`
    );

    this.addSql(
      `alter table "team_training_slots" drop constraint "team_training_slots_player_player_id_foreign";`
    );
    this.addSql(
      `alter table "team_training_slots" add constraint "team_training_slots_player_player_id_foreign" foreign key ("player_player_id") references "players" ("player_id") on update cascade;`
    );

    this.addSql(
      `alter table "transfer_request" drop constraint "transfer_request_player_id_foreign";`
    );
    this.addSql(
      `alter table "transfer_request" add constraint "transfer_request_player_id_foreign" foreign key ("player_id") references "players" ("player_id") on update cascade;`
    );
  }
}
