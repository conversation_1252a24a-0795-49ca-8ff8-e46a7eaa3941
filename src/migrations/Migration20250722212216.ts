import { Migration } from '@mikro-orm/migrations';

export class Migration20250722212216 extends Migration {

  override async up(): Promise<void> {
    this.addSql(`create table "app_version" ("platform" varchar(255) not null, "latest_version" int not null, "force_update" boolean not null, constraint "app_version_pkey" primary key ("platform"));`);
  }

  override async down(): Promise<void> {
    this.addSql(`drop table if exists "app_version" cascade;`);
  }

}
