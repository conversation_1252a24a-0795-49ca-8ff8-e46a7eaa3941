import { Migration } from '@mikro-orm/migrations';

export class Migration20250725194215 extends Migration {

  override async up(): Promise<void> {
    this.addSql(`create index "team_gameworld_id_index" on "team" ("gameworld_id");`);

    this.addSql(`create index "players_team_id_index" on "players" ("team_id");`);
  }

  override async down(): Promise<void> {
    this.addSql(`drop index "team_gameworld_id_index";`);

    this.addSql(`drop index "players_team_id_index";`);
  }

}
