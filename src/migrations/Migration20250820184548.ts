import { Migration } from '@mikro-orm/migrations';

export class Migration20250820184548 extends Migration {

  override async up(): Promise<void> {
    this.addSql(`alter table "archived_players" drop constraint "archived_players_archived_team_id_foreign";`);
  }

  override async down(): Promise<void> {
    this.addSql(`alter table "archived_players" add constraint "archived_players_archived_team_id_foreign" foreign key ("archived_team_id") references "archived_team" ("archived_team_id") on update cascade on delete set null;`);
  }

}
