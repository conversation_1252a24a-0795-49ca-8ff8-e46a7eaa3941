import { Migration } from '@mikro-orm/migrations';

export class Migration20250605130043 extends Migration {

  override async up(): Promise<void> {
    this.addSql(`alter table "player_match_history" drop constraint "player_match_history_player_id_foreign";`);

    this.addSql(`alter table "player_match_history" alter column "player_id" drop default;`);
    this.addSql(`alter table "player_match_history" alter column "player_id" type uuid using ("player_id"::text::uuid);`);
    this.addSql(`alter table "player_match_history" alter column "player_id" set not null;`);
    this.addSql(`alter table "player_match_history" add constraint "player_match_history_player_id_foreign" foreign key ("player_id") references "players" ("player_id") on update cascade;`);
  }

  override async down(): Promise<void> {
    this.addSql(`alter table "player_match_history" drop constraint "player_match_history_player_id_foreign";`);

    this.addSql(`alter table "player_match_history" alter column "player_id" drop default;`);
    this.addSql(`alter table "player_match_history" alter column "player_id" type uuid using ("player_id"::text::uuid);`);
    this.addSql(`alter table "player_match_history" alter column "player_id" drop not null;`);
    this.addSql(`alter table "player_match_history" add constraint "player_match_history_player_id_foreign" foreign key ("player_id") references "players" ("player_id") on delete cascade;`);
  }

}
