import { Migration } from '@mikro-orm/migrations';

export class Migration20250612190801 extends Migration {

  override async up(): Promise<void> {
    this.addSql(`alter table "transfer_list" drop constraint "transfer_list_player_player_id_foreign";`);

    this.addSql(`alter table "bid_history" drop constraint "bid_history_transfer_listing_id_foreign";`);

    this.addSql(`alter table "transfer_list" alter column "player_player_id" drop default;`);
    this.addSql(`alter table "transfer_list" alter column "player_player_id" type uuid using ("player_player_id"::text::uuid);`);
    this.addSql(`alter table "transfer_list" alter column "player_player_id" drop not null;`);
    this.addSql(`alter table "transfer_list" add constraint "transfer_list_player_player_id_foreign" foreign key ("player_player_id") references "players" ("player_id") on delete cascade;`);

    this.addSql(`alter table "bid_history" alter column "transfer_listing_id" drop default;`);
    this.addSql(`alter table "bid_history" alter column "transfer_listing_id" type uuid using ("transfer_listing_id"::text::uuid);`);
    this.addSql(`alter table "bid_history" alter column "transfer_listing_id" drop not null;`);
    this.addSql(`alter table "bid_history" add constraint "bid_history_transfer_listing_id_foreign" foreign key ("transfer_listing_id") references "transfer_list" ("id") on delete cascade;`);
  }

  override async down(): Promise<void> {
    this.addSql(`alter table "transfer_list" drop constraint "transfer_list_player_player_id_foreign";`);

    this.addSql(`alter table "bid_history" drop constraint "bid_history_transfer_listing_id_foreign";`);

    this.addSql(`alter table "transfer_list" alter column "player_player_id" drop default;`);
    this.addSql(`alter table "transfer_list" alter column "player_player_id" type uuid using ("player_player_id"::text::uuid);`);
    this.addSql(`alter table "transfer_list" alter column "player_player_id" set not null;`);
    this.addSql(`alter table "transfer_list" add constraint "transfer_list_player_player_id_foreign" foreign key ("player_player_id") references "players" ("player_id") on update cascade;`);

    this.addSql(`alter table "bid_history" alter column "transfer_listing_id" drop default;`);
    this.addSql(`alter table "bid_history" alter column "transfer_listing_id" type uuid using ("transfer_listing_id"::text::uuid);`);
    this.addSql(`alter table "bid_history" alter column "transfer_listing_id" set not null;`);
    this.addSql(`alter table "bid_history" add constraint "bid_history_transfer_listing_id_foreign" foreign key ("transfer_listing_id") references "transfer_list" ("id") on update cascade;`);
  }

}
