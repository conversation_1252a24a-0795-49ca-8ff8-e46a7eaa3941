import { Migration } from '@mikro-orm/migrations';

export class Migration20250829210524 extends Migration {

  override async up(): Promise<void> {
    this.addSql(`drop index "archived_players_archived_team_id_index";`);
    this.addSql(`alter table "archived_players" drop column "archived_team_id";`);

    this.addSql(`alter table "transfer_list" alter column "created_at" type bigint using ("created_at"::bigint);`);
    this.addSql(`alter table "transfer_list" alter column "created_at" set default 1756501524051;`);
  }

  override async down(): Promise<void> {
    this.addSql(`alter table "archived_players" add column "archived_team_id" uuid null;`);
    this.addSql(`create index "archived_players_archived_team_id_index" on "archived_players" ("archived_team_id");`);

    this.addSql(`alter table "transfer_list" alter column "created_at" type bigint using ("created_at"::bigint);`);
    this.addSql(`alter table "transfer_list" alter column "created_at" set default 1756501264687;`);
  }

}
