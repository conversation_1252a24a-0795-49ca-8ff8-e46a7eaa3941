import { Migration } from '@mikro-orm/migrations';

export class Migration20250601134812 extends Migration {

  override async up(): Promise<void> {
    this.addSql(`create table "team_training_slots" ("id" varchar(255) not null, "team_team_id" uuid not null, "slot_index" int not null, "player_player_id" uuid null, "attribute" varchar(255) null, "assigned_at" bigint null, constraint "team_training_slots_pkey" primary key ("id"));`);

    this.addSql(`alter table "team_training_slots" add constraint "team_training_slots_team_team_id_foreign" foreign key ("team_team_id") references "team" ("team_id") on update cascade;`);
    this.addSql(`alter table "team_training_slots" add constraint "team_training_slots_player_player_id_foreign" foreign key ("player_player_id") references "players" ("player_id") on update cascade on delete set null;`);
  }

  override async down(): Promise<void> {
    this.addSql(`drop table if exists "team_training_slots" cascade;`);
  }

}
