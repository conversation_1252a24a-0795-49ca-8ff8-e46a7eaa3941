import { Migration } from '@mikro-orm/migrations';

export class Migration20250820184148 extends Migration {

  override async up(): Promise<void> {
    this.addSql(`alter table "archived_player_attributes" drop constraint "archived_player_attributes_archived_player_id_foreign";`);

    this.addSql(`alter table "archived_player_overall_stats" drop constraint "archived_player_overall_stats_archived_player_id_foreign";`);

    this.addSql(`alter table "archived_player_attributes" alter column "archived_player_id" drop default;`);
    this.addSql(`alter table "archived_player_attributes" alter column "archived_player_id" type uuid using ("archived_player_id"::text::uuid);`);
    this.addSql(`alter table "archived_player_attributes" alter column "archived_player_id" set not null;`);
  }

  override async down(): Promise<void> {
    this.addSql(`alter table "archived_player_overall_stats" add constraint "archived_player_overall_stats_archived_player_id_foreign" foreign key ("archived_player_id") references "archived_players" ("archived_player_id") on update cascade on delete cascade;`);

    this.addSql(`alter table "archived_player_attributes" alter column "archived_player_id" drop default;`);
    this.addSql(`alter table "archived_player_attributes" alter column "archived_player_id" type uuid using ("archived_player_id"::text::uuid);`);
    this.addSql(`alter table "archived_player_attributes" alter column "archived_player_id" drop not null;`);
    this.addSql(`alter table "archived_player_attributes" add constraint "archived_player_attributes_archived_player_id_foreign" foreign key ("archived_player_id") references "archived_players" ("archived_player_id") on delete cascade;`);
  }

}
