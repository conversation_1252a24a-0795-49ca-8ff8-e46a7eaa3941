import { Migration } from '@mikro-orm/migrations';

export class Migration20250829210104 extends Migration {
  override async up(): Promise<void> {
    this.addSql(`alter table "archived_players" add column "archived_team_archived_team_id" uuid;`);
    this.addSql(
      `update "archived_players" set "archived_team_archived_team_id" = "archived_team_id";`
    );
    this.addSql(
      `alter table "archived_players" alter column "archived_team_archived_team_id" set not null;`
    );
    this.addSql(
      `alter table "archived_players" add constraint "archived_players_archived_team_archived_team_id_foreign" foreign key ("archived_team_archived_team_id") references "archived_team" ("archived_team_id") on update cascade;`
    );

    this.addSql(
      `alter table "transfer_list" alter column "created_at" type bigint using ("created_at"::bigint);`
    );
    this.addSql(`alter table "transfer_list" alter column "created_at" set default 1756501264687;`);
  }

  override async down(): Promise<void> {
    this.addSql(
      `alter table "archived_players" drop constraint "archived_players_archived_team_archived_team_id_foreign";`
    );

    this.addSql(`alter table "archived_players" drop column "archived_team_archived_team_id";`);

    this.addSql(`alter table "transfer_list" alter column "created_at" drop default;`);
    this.addSql(
      `alter table "transfer_list" alter column "created_at" type bigint using ("created_at"::bigint);`
    );
  }
}
