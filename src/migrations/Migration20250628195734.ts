import { Migration } from '@mikro-orm/migrations';

export class Migration20250628195734 extends Migration {
  override async up(): Promise<void> {
    this.addSql(
      `alter table "player_attributes" alter column "is_goalkeeper" type boolean using ("is_goalkeeper"::boolean);`
    );
    this.addSql(`alter table "player_attributes" alter column "is_goalkeeper" set default false;`);

    this.addSql(
      `alter table "fixture" drop column "stats_possession", drop column "stats_shots", drop column "stats_shots_on_target", drop column "stats_corners", drop column "stats_fouls", drop column "stats_yellow_cards", drop column "stats_red_cards", drop column "stats_passes", drop column "stats_pass_accuracy", drop column "stats_tackles", drop column "stats_interceptions", drop column "stats_score", drop column "stats_scorers", drop column "events";`
    );

    this.addSql(
      `alter table "fixture" add column "score" text[] null, add column "scorers" jsonb null;`
    );
  }

  override async down(): Promise<void> {
    this.addSql(`alter table "player_attributes" alter column "is_goalkeeper" drop default;`);
    this.addSql(
      `alter table "player_attributes" alter column "is_goalkeeper" type boolean using ("is_goalkeeper"::boolean);`
    );

    this.addSql(`alter table "fixture" drop column "score";`);

    this.addSql(
      `alter table "fixture" add column "stats_possession" text[] null, add column "stats_shots" text[] null, add column "stats_shots_on_target" text[] null, add column "stats_corners" text[] null, add column "stats_fouls" text[] null, add column "stats_yellow_cards" text[] null, add column "stats_red_cards" text[] null, add column "stats_passes" text[] null, add column "stats_pass_accuracy" text[] null, add column "stats_tackles" text[] null, add column "stats_interceptions" text[] null, add column "stats_score" text[] null, add column "events" jsonb null;`
    );
    this.addSql(`alter table "fixture" rename column "scorers" to "stats_scorers";`);
  }
}
