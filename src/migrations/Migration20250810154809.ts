import { Migration } from '@mikro-orm/migrations';

export class Migration20250810154809 extends Migration {

  override async up(): Promise<void> {
    this.addSql(`alter table "team_training_slots" alter column "start_value" type real using ("start_value"::real);`);
  }

  override async down(): Promise<void> {
    this.addSql(`alter table "team_training_slots" alter column "start_value" type int using ("start_value"::int);`);
  }

}
