import { Migration } from '@mikro-orm/migrations';

export class Migration20250602150210 extends Migration {

  override async up(): Promise<void> {
    this.addSql(`alter table "manager" add column "magic_sponges" int not null default 1, add column "card_appeals" int not null default 0, add column "training_boosts" int not null default 0;`);
    this.addSql(`alter table "manager" alter column "scout_tokens" type int using ("scout_tokens"::int);`);
    this.addSql(`alter table "manager" alter column "scout_tokens" set default 2;`);
  }

  override async down(): Promise<void> {
    this.addSql(`alter table "manager" drop column "magic_sponges", drop column "card_appeals", drop column "training_boosts";`);

    this.addSql(`alter table "manager" alter column "scout_tokens" type int using ("scout_tokens"::int);`);
    this.addSql(`alter table "manager" alter column "scout_tokens" set default 0;`);
  }

}
