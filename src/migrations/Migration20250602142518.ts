import { Migration } from '@mikro-orm/migrations';

export class Migration20250602142518 extends Migration {

  override async up(): Promise<void> {
    this.addSql(`alter table "player_attributes" alter column "reflexes_current" type float using ("reflexes_current"::float);`);
    this.addSql(`alter table "player_attributes" alter column "reflexes_potential" type float using ("reflexes_potential"::float);`);
    this.addSql(`alter table "player_attributes" alter column "positioning_current" type float using ("positioning_current"::float);`);
    this.addSql(`alter table "player_attributes" alter column "positioning_potential" type float using ("positioning_potential"::float);`);
    this.addSql(`alter table "player_attributes" alter column "shot_stopping_current" type float using ("shot_stopping_current"::float);`);
    this.addSql(`alter table "player_attributes" alter column "shot_stopping_potential" type float using ("shot_stopping_potential"::float);`);
    this.addSql(`alter table "player_attributes" alter column "tackling_current" type float using ("tackling_current"::float);`);
    this.addSql(`alter table "player_attributes" alter column "tackling_potential" type float using ("tackling_potential"::float);`);
    this.addSql(`alter table "player_attributes" alter column "marking_current" type float using ("marking_current"::float);`);
    this.addSql(`alter table "player_attributes" alter column "marking_potential" type float using ("marking_potential"::float);`);
    this.addSql(`alter table "player_attributes" alter column "heading_current" type float using ("heading_current"::float);`);
    this.addSql(`alter table "player_attributes" alter column "heading_potential" type float using ("heading_potential"::float);`);
    this.addSql(`alter table "player_attributes" alter column "finishing_current" type float using ("finishing_current"::float);`);
    this.addSql(`alter table "player_attributes" alter column "finishing_potential" type float using ("finishing_potential"::float);`);
    this.addSql(`alter table "player_attributes" alter column "pace_current" type float using ("pace_current"::float);`);
    this.addSql(`alter table "player_attributes" alter column "pace_potential" type float using ("pace_potential"::float);`);
    this.addSql(`alter table "player_attributes" alter column "crossing_current" type float using ("crossing_current"::float);`);
    this.addSql(`alter table "player_attributes" alter column "crossing_potential" type float using ("crossing_potential"::float);`);
    this.addSql(`alter table "player_attributes" alter column "passing_current" type float using ("passing_current"::float);`);
    this.addSql(`alter table "player_attributes" alter column "passing_potential" type float using ("passing_potential"::float);`);
    this.addSql(`alter table "player_attributes" alter column "vision_current" type float using ("vision_current"::float);`);
    this.addSql(`alter table "player_attributes" alter column "vision_potential" type float using ("vision_potential"::float);`);
    this.addSql(`alter table "player_attributes" alter column "ball_control_current" type float using ("ball_control_current"::float);`);
    this.addSql(`alter table "player_attributes" alter column "ball_control_potential" type float using ("ball_control_potential"::float);`);
  }

  override async down(): Promise<void> {
    this.addSql(`alter table "player_attributes" alter column "reflexes_current" type int using ("reflexes_current"::int);`);
    this.addSql(`alter table "player_attributes" alter column "reflexes_potential" type int using ("reflexes_potential"::int);`);
    this.addSql(`alter table "player_attributes" alter column "positioning_current" type int using ("positioning_current"::int);`);
    this.addSql(`alter table "player_attributes" alter column "positioning_potential" type int using ("positioning_potential"::int);`);
    this.addSql(`alter table "player_attributes" alter column "shot_stopping_current" type int using ("shot_stopping_current"::int);`);
    this.addSql(`alter table "player_attributes" alter column "shot_stopping_potential" type int using ("shot_stopping_potential"::int);`);
    this.addSql(`alter table "player_attributes" alter column "tackling_current" type int using ("tackling_current"::int);`);
    this.addSql(`alter table "player_attributes" alter column "tackling_potential" type int using ("tackling_potential"::int);`);
    this.addSql(`alter table "player_attributes" alter column "marking_current" type int using ("marking_current"::int);`);
    this.addSql(`alter table "player_attributes" alter column "marking_potential" type int using ("marking_potential"::int);`);
    this.addSql(`alter table "player_attributes" alter column "heading_current" type int using ("heading_current"::int);`);
    this.addSql(`alter table "player_attributes" alter column "heading_potential" type int using ("heading_potential"::int);`);
    this.addSql(`alter table "player_attributes" alter column "finishing_current" type int using ("finishing_current"::int);`);
    this.addSql(`alter table "player_attributes" alter column "finishing_potential" type int using ("finishing_potential"::int);`);
    this.addSql(`alter table "player_attributes" alter column "pace_current" type int using ("pace_current"::int);`);
    this.addSql(`alter table "player_attributes" alter column "pace_potential" type int using ("pace_potential"::int);`);
    this.addSql(`alter table "player_attributes" alter column "crossing_current" type int using ("crossing_current"::int);`);
    this.addSql(`alter table "player_attributes" alter column "crossing_potential" type int using ("crossing_potential"::int);`);
    this.addSql(`alter table "player_attributes" alter column "passing_current" type int using ("passing_current"::int);`);
    this.addSql(`alter table "player_attributes" alter column "passing_potential" type int using ("passing_potential"::int);`);
    this.addSql(`alter table "player_attributes" alter column "vision_current" type int using ("vision_current"::int);`);
    this.addSql(`alter table "player_attributes" alter column "vision_potential" type int using ("vision_potential"::int);`);
    this.addSql(`alter table "player_attributes" alter column "ball_control_current" type int using ("ball_control_current"::int);`);
    this.addSql(`alter table "player_attributes" alter column "ball_control_potential" type int using ("ball_control_potential"::int);`);
  }

}
