import createEvent from '@/testing/aws-mocks/index.js';
import type { PartialHttpEvent } from './types.js';

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export default function createHttpEvent(event: PartialHttpEvent = {}): any {
  const defaultEvent = createEvent('aws:apiGateway', {});
  const clonedEvent = structuredClone(event);
  return {
    ...defaultEvent,
    ...clonedEvent,
    headers: {
      ...defaultEvent.headers,
      ...clonedEvent.headers,
      ...(clonedEvent.body && { 'Content-Type': 'application/json' }),
    },
    body: clonedEvent.body ? JSON.stringify(clonedEvent.body) : null,
  };
}
