import { resolve as resolveTs } from 'ts-node/esm';
import * as tsConfigPaths from 'tsconfig-paths';
import { pathToFileURL } from 'url';

const { absoluteBaseUrl, paths } = tsConfigPaths.loadConfig();
const matchPath = tsConfigPaths.createMatchPath(absoluteBaseUrl, paths);

export function resolve(specifier, ctx, defaultResolve) {
  try {
    // Handle the extension mismatch: replace .js with .ts
    let updatedSpecifier = specifier;
    if (specifier.endsWith('.js')) {
      updatedSpecifier = specifier.replace(/\.js$/, '.ts');
      console.log(`[Resolver] Replaced extension: ${specifier} -> ${updatedSpecifier}`);
    }

    const match = matchPath(updatedSpecifier);
    return match
      ? resolveTs(pathToFileURL(`${match}`).href, ctx, defaultResolve)
      : resolveTs(specifier, ctx, defaultResolve);
  } catch (e) {
    console.log('Something went wrong', e);
    return defaultResolve(specifier, ctx, defaultResolve);
  }
}

export { load, transformSource } from 'ts-node/esm';
