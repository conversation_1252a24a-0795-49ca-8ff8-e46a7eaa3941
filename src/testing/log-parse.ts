/* eslint-disable @typescript-eslint/no-unsafe-assignment */
import * as fs from 'fs';
import * as path from 'path';

interface LogEntry {
  events: {
    timestamp: number;
    message: string;
    ingestionTime: number;
  }[];
}

interface LambdaEvent {
  timestamp: string;
  event: any;
  xray_trace_id?: string;
  message?: string;
}

function parseLambdaLogs(filePath: string, outputPath: string): void {
  const TRACE_ID = '1-67ce00f0-d2bbbc510dd0146094958333';

  // Clear/create the output file
  fs.writeFileSync(outputPath, '');

  function writeToLog(message: string) {
    fs.appendFileSync(outputPath, message + '\n');
  }

  try {
    // Read the file as a buffer first
    const buffer = fs.readFileSync(filePath);

    // Convert buffer to string, skipping potential BOM characters
    let fileContent = '';
    if (buffer[0] === 0xef && buffer[1] === 0xbb && buffer[2] === 0xbf) {
      fileContent = buffer.toString('utf-8', 3);
    } else if (buffer[0] === 0xfe && buffer[1] === 0xff) {
      fileContent = buffer.toString('utf16le', 2);
    } else if (buffer[0] === 0xff && buffer[1] === 0xfe) {
      fileContent = buffer.toString('utf16le', 2);
    } else {
      fileContent = buffer.toString('utf-8');
    }

    // Clean up any remaining special characters at the start
    fileContent = fileContent.replace(/^\s+/, '');

    // Parse the JSON content
    const logData: LogEntry = JSON.parse(fileContent);

    writeToLog('=== Lambda Invocation Events ===\n');

    // First pass: Process Lambda invocation events
    logData.events.forEach((event) => {
      try {
        const messageObj = JSON.parse(event.message) as LambdaEvent;

        if (messageObj.message === 'Lambda invocation event') {
          const timestamp = new Date(event.timestamp).toLocaleString();
          writeToLog('\n-------------------');
          writeToLog(`Timestamp: ${timestamp}`);
          writeToLog(`Event payload: ${JSON.stringify(messageObj.event, null, 2)}`);
        }
      } catch (parseError) {
        // Skip non-JSON messages
      }
    });

    writeToLog('\n\n=== X-Ray Trace Events ===\n');

    // Second pass: Process X-Ray trace events
    logData.events.forEach((event) => {
      try {
        const messageObj = JSON.parse(event.message) as LambdaEvent;

        if (messageObj.xray_trace_id === TRACE_ID) {
          const timestamp = new Date(event.timestamp).toLocaleString();
          writeToLog('\n-------------------');
          writeToLog(`Timestamp: ${timestamp}`);
          writeToLog(`Message: ${JSON.stringify(messageObj, null, 2)}`);
        }
      } catch (parseError) {
        // Skip non-JSON messages
      }
    });
  } catch (error) {
    const errorMessage = `Error processing log file: ${error}\n`;
    writeToLog(errorMessage);
    if (error instanceof Error) {
      writeToLog(`Error details: ${error.message}\n`);
      writeToLog(
        `First 100 characters of file: ${Buffer.from(fs.readFileSync(filePath))
          .toString('hex')
          .substring(0, 100)}\n`
      );
    }
  }
}

// Execute the function
const logFilePath = path.join('C:', 'Users', 'davec', 'WebstormProjects', 'lambda-log.txt');
const outputFilePath = path.join('C:', 'Users', 'davec', 'WebstormProjects', 'parsed-logs.txt');
parseLambdaLogs(logFilePath, outputFilePath);
