import type { APIGatewayProxyEvent, SQSMessageAttributes, SQSRecordAttributes } from 'aws-lambda';

export interface PartialHttpEvent extends Omit<Partial<APIGatewayProxyEvent>, 'body'> {
  body?: object | null;
}

export interface PartialSqsRecord {
  messageId?: string;
  receiptHandle?: string;
  body?: object;
  attributes?: Partial<SQSRecordAttributes>;
  messageAttributes?: SQSMessageAttributes;
  md5OfBody?: string;
  eventSource?: string;
  eventSourceARN?: string;
  awsRegion?: string;
}

/*export interface PartialSnsRecord {
  EventVersion?: string;
  EventSubscriptionArn?: string;
  EventSource?: string;
  Sns?: Partial<SNSMessage<object>>;
}

export interface PartialDynamoDbRecord {
  awsRegion?: string;
  dynamodb?: Partial<DynamoDbStreamRecord<object>>;
  eventID?: string;
  eventName?: 'INSERT' | 'MODIFY' | 'REMOVE';
  eventSource?: string;
  eventSourceARN?: string;
  eventVersion?: string;
  userIdentity?: unknown;
}*/
