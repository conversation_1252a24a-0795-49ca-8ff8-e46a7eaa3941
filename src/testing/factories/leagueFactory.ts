import { League } from '@/entities/League.js';
import { LeagueRules } from '@/entities/LeagueRules.js';
import { Team } from '@/entities/Team.js';
import { GameworldFactory } from '@/testing/factories/gameworldFactory.js';
import { Collection } from '@mikro-orm/core';
import { Factory } from 'interface-forge';

export const LeagueFactory = new Factory<League>((factory: Factory<League>, iteration: number) => {
  return {
    id: factory.string.uuid(),
    gameworld: GameworldFactory.build(),
    name: factory.string.alphanumeric({ length: 10 }),
    tier: factory.number.int({ min: 1, max: 10 }),
    parentLeague: undefined, // Can be set to another League reference if needed
    leagueChildren: new Collection<League>({}),
    leagueRules: new LeagueRules(), // Can be set to a LeagueRules reference if needed
    teams: new Collection<Team>({}),
  };
});
