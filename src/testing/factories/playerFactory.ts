import { League } from '@/entities/League.js';
import { Player } from '@/entities/Player.js';
import { PlayerMatchHistory } from '@/entities/PlayerMatchHistory.js';
import { PlayerAttributesFactory } from '@/testing/factories/playerAttributesFactory.js';
import { TeamsFactory } from '@/testing/factories/teamFactory.js';
import { Collection, Rel } from '@mikro-orm/core';
import { Factory } from 'interface-forge';

export const PlayerFactory = new Factory<Player>((factory: Factory<Player>, iteration: number) => {
  return {
    playerId: factory.string.uuid(),
    gameworldId: factory.string.uuid(),
    team: TeamsFactory.build(),
    leagueId: {
      id: factory.string.uuid(),
    } as Rel<League>,
    firstName: factory.person.firstName(),
    surname: factory.person.lastName(),
    age: factory.number.int({ min: 17, max: 39 }),
    seed: factory.number.bigInt({ min: 0, max: 1000000 }),
    value: factory.number.int({ min: 100000, max: 10000000 }),
    energy: factory.number.int({ min: 0, max: 100 }),
    lastMatchPlayed: BigInt(factory.date.recent({ days: 1 }).getTime()),
    suspendedForGames: factory.number.int({ min: 0, max: 2 }),
    injuredUntil: BigInt(factory.date.soon({ days: 1 }).getTime()),
    isTransferListed: factory.datatype.boolean(),
    attributes: PlayerAttributesFactory.build(),
    matchHistory: new Collection<PlayerMatchHistory>({}),
    retiringAtEndOfSeason: factory.datatype.boolean(),
  };
});
