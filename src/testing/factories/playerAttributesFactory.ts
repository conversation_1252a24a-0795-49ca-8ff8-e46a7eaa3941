// filepath: c:\Users\<USER>\WebstormProjects\jumpers-for-goalposts-backend\src\testing\factories\playerAttributesFactory.ts
import { Player } from '@/entities/Player.js';
import { PlayerAttributes } from '@/entities/PlayerAttributes.js';
import { Rel } from '@mikro-orm/core';
import { Factory } from 'interface-forge';

export const PlayerAttributesFactory = new Factory<PlayerAttributes>(
  (factory: Factory<PlayerAttributes>, iteration: number) => {
    const reflexesCurrent = factory.number.int({ min: 1, max: 40 });
    const positioningCurrent = factory.number.int({ min: 1, max: 40 });
    const shotStoppingCurrent = factory.number.int({ min: 1, max: 40 });
    const tacklingCurrent = factory.number.int({ min: 1, max: 40 });
    const markingCurrent = factory.number.int({ min: 1, max: 40 });
    const headingCurrent = factory.number.int({ min: 1, max: 40 });
    const finishingCurrent = factory.number.int({ min: 1, max: 40 });
    const paceCurrent = factory.number.int({ min: 1, max: 40 });
    const crossingCurrent = factory.number.int({ min: 1, max: 40 });
    const passingCurrent = factory.number.int({ min: 1, max: 40 });
    const visionCurrent = factory.number.int({ min: 1, max: 40 });
    const ballControlCurrent = factory.number.int({ min: 1, max: 40 });

    return {
      player: {} as Rel<Player>,
      isGoalkeeper: factory.datatype.boolean(),
      reflexesCurrent,
      reflexesPotential: factory.number.int({ min: reflexesCurrent, max: 40 }),
      positioningCurrent,
      positioningPotential: factory.number.int({ min: positioningCurrent, max: 40 }),
      shotStoppingCurrent,
      shotStoppingPotential: factory.number.int({ min: shotStoppingCurrent, max: 40 }),
      tacklingCurrent,
      tacklingPotential: factory.number.int({ min: tacklingCurrent, max: 40 }),
      markingCurrent,
      markingPotential: factory.number.int({ min: markingCurrent, max: 40 }),
      headingCurrent,
      headingPotential: factory.number.int({ min: headingCurrent, max: 40 }),
      finishingCurrent,
      finishingPotential: factory.number.int({ min: finishingCurrent, max: 40 }),
      paceCurrent,
      pacePotential: factory.number.int({ min: paceCurrent, max: 40 }),
      crossingCurrent,
      crossingPotential: factory.number.int({ min: crossingCurrent, max: 40 }),
      passingCurrent,
      passingPotential: factory.number.int({ min: passingCurrent, max: 40 }),
      visionCurrent,
      visionPotential: factory.number.int({ min: visionCurrent, max: 40 }),
      ballControlCurrent,
      ballControlPotential: factory.number.int({ min: ballControlCurrent, max: 40 }),
      stamina: factory.number.float({ min: 0.1, max: 1.0 }),
    };
  }
);
