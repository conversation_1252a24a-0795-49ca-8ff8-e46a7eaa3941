import { Repositories } from '@/middleware/database/types.js';
import { vi } from 'vitest';

/**
 * Mock repositories for testing
 *
 * These mocks implement all methods from the repository interfaces
 * to ensure type safety and complete mocking for tests.
 */

export const mockEntityManager = {
  nativeUpdate: vi.fn(),
  nativeDelete: vi.fn(),
  find: vi.fn(),
  count: vi.fn().mockResolvedValue(0),
  insertMany: vi.fn(),
  transactional: vi.fn().mockImplementation(async (callback) => {
    return await callback(mockEntityManager);
  }),
} as any;
/**
 * Mock implementation of TeamRepository
 */
export const mockTeamRepository = {
  batchInsertTeams: vi.fn(),
  batchInsertAvailableTeams: vi.fn(),
  getTeamsByGameworld: vi.fn(),
  getTeamsByLeague: vi.fn(),
  updateTeamLeague: vi.fn(),
  resetTeamStandings: vi.fn(),
  updateTeamLeagues: vi.fn(),
  getRandomAvailableTeam: vi.fn(),
  deleteAvailableTeam: vi.fn(),
  updateTeamStandings: vi.fn(),
  getTeam: vi.fn(),
  getTeamForSimulation: vi.fn(),
  findByIds: vi.fn(),
  findByIdsForAI: vi.fn(),
  getTeamsWithoutManager: vi.fn(),
  updateTeamBalance: vi.fn(),
  updateTeamSelectionOrder: vi.fn(),
  updateTeamName: vi.fn(),
  getTeamAndNextMatch: vi.fn(),
  flush: vi.fn(),
  createFromPK: vi.fn().mockImplementation((id: string) => ({
    teamId: id,
  })),
  getEntityManager: vi.fn().mockReturnValue(mockEntityManager),
  incrementTrainingLevel: vi.fn(),
  getTransactions: vi.fn(),
  updateTeamBalances: vi.fn(),
  countAvailableTeams: vi.fn(),
  removeAvailableTeamByTeamId: vi.fn(),
  addAvailableTeam: vi.fn(),
  getAvailableTeamByTeamId: vi.fn(),
  reinitialiseTeam: vi.fn(),
  getAndDeleteAvailableTeam: vi.fn(),
  releaseTeamToAvailable: vi.fn(),
};

/**
 * Mock implementation of ManagerRepositoryInterface
 */
export const mockManagerRepository = {
  createManager: vi.fn(),
  getManagerById: vi.fn(),
  updateManagerById: vi.fn(),
  updateManager: vi.fn(),
  deleteManager: vi.fn(),
  getManagerByTeamId: vi.fn(),
  getEntityManager: vi.fn().mockReturnValue(mockEntityManager),
  createFromPK: vi.fn(),
  updateMagicSpongeCount: vi.fn(),
  updateCardAppealCount: vi.fn(),
  findIdleManagers: vi.fn(),
};

/**
 * Mock implementation of PlayerRepository
 */
export const mockPlayerRepository = {
  batchCreatePlayers: vi.fn(),
  getPlayer: vi.fn(),
  getPlayersByTeam: vi.fn(),
  getPlayersByLeague: vi.fn(),
  getPlayersWithoutTeam: vi.fn(),
  getPlayersByGameworld: vi.fn(),
  updatePlayer: vi.fn(),
  updatePlayerStats: vi.fn(),
  assignPlayerToTeam: vi.fn(),
  removePlayerFromTeam: vi.fn(),
  removePlayer: vi.fn(),
  addPlayerMatchHistory: vi.fn(),
  getPlayerMatchHistory: vi.fn(),
  batchCreateTransferListedPlayers: vi.fn(),
  getTransferListedPlayers: vi.fn(),
  isPlayerScoutedByTeam: vi.fn(),
  getPlayersScoutedByTeam: vi.fn(),
  getRandomPlayersFromLeague: vi.fn(),
  createFromPK: vi.fn().mockImplementation((id: string) => ({
    playerId: id,
  })),
  mikroOrmService: vi.fn(),
  getEntityManager: vi.fn().mockReturnValue(mockEntityManager),
  updatePlayerAttributesBatch: vi.fn(),
  getPlayerWithStats: vi.fn(),
};

/**
 * Mock implementation of LeagueRepository
 */
export const mockLeagueRepository = {
  batchCreateLeagues: vi.fn(),
  getLeaguesByGameworld: vi.fn(),
  getLeague: vi.fn(),
  getLeagueHierarchy: vi.fn(),
  updateLeague: vi.fn(),
  createFromPK: vi.fn(),
};

/**
 * Mock implementation of FixtureRepository
 */
export const mockFixtureRepository = {
  batchInsertFixtures: vi.fn(),
  getFixture: vi.fn(),
  getFixturesByLeague: vi.fn(),
  getFixturesByTeam: vi.fn(),
  getDueFixtures: vi.fn(),
  getAllUnplayedFixtures: vi.fn(),
  updateFixtureResult: vi.fn(),
  deleteCompletedFixtures: vi.fn(),
  getEntityManager: vi.fn().mockReturnValue(mockEntityManager),
  createFromPK: vi.fn(),
};

/**
 * Mock implementation of ScoutingRepository
 */
export const mockScoutingRepository = {
  scoutRandomPlayersFromLeague: vi.fn(),
  scoutPlayersFromTeam: vi.fn(),
  isPlayerScoutedByTeam: vi.fn(),
  getPlayersScoutedByTeam: vi.fn(),
  saveScoutedPlayers: vi.fn(),
};

/**
 * Mock implementation of ScoutingRequestRepository
 */
export const mockScoutingRequestRepository = {
  createScoutingRequest: vi.fn(),
  getPendingScoutingRequests: vi.fn(),
  markScoutingRequestAsProcessed: vi.fn(),
  getScoutingRequestsByTeam: vi.fn(),
};

/**
 * Mock implementation of TransferRepository
 */
export const mockTransferRepository = {
  getTransferListedPlayers: vi.fn(),
  getTransferListedPlayersForAI: vi.fn(),
  submitOffer: vi.fn(),
  getTransferRequest: vi.fn(),
  getTransferRequestsToAI: vi.fn(),
  getTransferRequestsBySeller: vi.fn(),
  getTransferRequestsByBuyer: vi.fn(),
  getTransferRequestsByPlayer: vi.fn(),
  submitCounterOffer: vi.fn(),
  deleteTransferRequest: vi.fn(),
  submitBid: vi.fn(),
  getCompletedAuctions: vi.fn(),
  updateTransferListedPlayer: vi.fn(),
  deleteTransferListedPlayer: vi.fn(),
  getActiveAuctions: vi.fn(),
  addTransferListedPlayer: vi.fn(),
  getTransferListedPlayersWithTeamBids: vi.fn(),
  flush: vi.fn(),
};

/**
 * Mock implementation of GameworldRepository
 */
export const mockGameworldRepository = {
  getGameworld: vi.fn(),
  getAllGameworlds: vi.fn(),
  getCompletedSeasons: vi.fn(),
  createGameworld: vi.fn(),
  updateGameworld: vi.fn(),
  updateGameworldEndDate: vi.fn(),
};

/**
 * Mock implementation of InboxRepository
 */
export const mockInboxRepository = {
  createMessage: vi.fn(),
  getMessage: vi.fn(),
  getAllMessages: vi.fn(),
  updateMessage: vi.fn(),
  deleteMessage: vi.fn(),
  getMessagesByGameworldAndTeam: vi.fn(),
  flush: vi.fn(),
};

export const mockTrainingRepository = {
  assignPlayerToSlot: vi.fn(),
  clearSlot: vi.fn(),
  getSlotsByTeam: vi.fn(),
  createFromPK: vi.fn(),
  getEntityManager: vi.fn().mockReturnValue(mockEntityManager),
  createSlot: vi.fn(),
  getSlotById: vi.fn(),
  getAllFilledSlots: vi.fn(),
  lockIAPSlot: vi.fn(),
};

/**
 * Mock implementation of PurchaseRepository
 */
export const mockPurchaseRepository = {
  createPurchase: vi.fn(),
  getPurchaseByTransactionId: vi.fn(),
  getPurchaseByEventId: vi.fn(),
  getPurchasesByManagerId: vi.fn(),
  getActiveSubscriptionsByManagerId: vi.fn(),
  updatePurchaseStatus: vi.fn(),
  getPurchasesByManagerAndProduct: vi.fn(),
  getExpiredSubscriptions: vi.fn(),
  upsertPurchase: vi.fn(),
  createFromPK: vi.fn(),
  getEntityManager: vi.fn().mockReturnValue(mockEntityManager),
};

/**
 * Creates a complete mock repositories object that can be used in tests
 * @returns A repositories object with all repository mocks
 */
export const createMockRepositories = (): Repositories => ({
  leagueRepository: mockLeagueRepository,
  teamRepository: mockTeamRepository,
  fixtureRepository: mockFixtureRepository,
  playerRepository: mockPlayerRepository,
  managerRepository: mockManagerRepository,
  purchaseRepository: mockPurchaseRepository,
  scoutingRepository: mockScoutingRepository,
  scoutingRequestRepository: mockScoutingRequestRepository,
  transferRepository: mockTransferRepository,
  gameworldRepository: mockGameworldRepository,
  inboxRepository: mockInboxRepository,
  trainingRepository: mockTrainingRepository,
});

/**
 * Default mock repositories instance for convenience
 */
export const mockRepositories = createMockRepositories();

/**
 * Sets up custom mock implementations and return values for repository mocks
 */
export const setupRepositoryMocks = (): void => {
  // TeamRepository
  mockTeamRepository.createFromPK.mockImplementation((id: string) => ({ teamId: id }));
  mockTeamRepository.getEntityManager.mockReturnValue(mockEntityManager);
  // PlayerRepository
  mockPlayerRepository.createFromPK.mockImplementation((id: string) => ({ playerId: id }));
  mockPlayerRepository.getEntityManager.mockReturnValue(mockEntityManager);
  // ManagerRepository
  mockManagerRepository.getEntityManager.mockReturnValue(mockEntityManager);
  // FixtureRepository
  mockFixtureRepository.getEntityManager.mockReturnValue(mockEntityManager);
  // TrainingRepository
  mockTrainingRepository.getEntityManager.mockReturnValue(mockEntityManager);
  // PurchaseRepository
  mockPurchaseRepository.getEntityManager.mockReturnValue(mockEntityManager);
};

/**
 * Resets all mock repositories
 * Useful for cleaning up between tests
 */
export const resetAllRepositoryMocks = (): void => {
  // Reset all mocks from each repository
  Object.values(mockLeagueRepository).forEach((mock) => mock.mockReset && mock.mockReset());
  Object.values(mockTeamRepository).forEach((mock) => mock.mockReset && mock.mockReset());
  Object.values(mockFixtureRepository).forEach((mock) => mock.mockReset && mock.mockReset());
  Object.values(mockPlayerRepository).forEach((mock) => mock.mockReset && mock.mockReset());
  Object.values(mockManagerRepository).forEach((mock) => mock.mockReset && mock.mockReset());
  Object.values(mockScoutingRepository).forEach((mock) => mock.mockReset && mock.mockReset());
  Object.values(mockScoutingRequestRepository).forEach(
    (mock) => mock.mockReset && mock.mockReset()
  );
  Object.values(mockTransferRepository).forEach((mock) => mock.mockReset && mock.mockReset());
  Object.values(mockGameworldRepository).forEach((mock) => mock.mockReset && mock.mockReset());
  Object.values(mockInboxRepository).forEach((mock) => mock.mockReset && mock.mockReset());
  Object.values(mockTrainingRepository).forEach((mock) => mock.mockReset && mock.mockReset());
  Object.values(mockPurchaseRepository).forEach((mock) => mock.mockReset && mock.mockReset());

  // Re-apply custom mock implementations/return values
  setupRepositoryMocks();
};
