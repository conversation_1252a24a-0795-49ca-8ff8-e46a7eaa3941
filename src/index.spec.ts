import { someTestFunction } from '@/index.js';
import { describe, expect, it } from 'vitest';

describe('Test', () => {
  describe('Function: someTestFunction', () => {
    it('should subtract 1 from the input if it is less than 0', () => {
      expect(someTestFunction(-1)).toEqual(-2);
    });
    it('should add 1 to the input if it is greater than 0', () => {
      expect(someTestFunction(1)).toEqual(2);
    });
    it('should return 0 if the input is 0', () => {
      expect(someTestFunction(0)).toEqual(0);
    });
  });
});
