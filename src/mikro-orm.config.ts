import { defineConfig } from '@mikro-orm/core';
import { EntityGenerator } from '@mikro-orm/entity-generator';
import { Migrator } from '@mikro-orm/migrations';
import { PostgreSqlDriver } from '@mikro-orm/postgresql';
import { TsMorphMetadataProvider } from '@mikro-orm/reflection';
import 'dotenv/config';
import path from 'path';
import { AppVersion } from './entities/AppVersion.js';
import { ArchivedManager } from './entities/ArchivedManager.js';
import { ArchivedPlayer } from './entities/ArchivedPlayer.js';
import { ArchivedPlayerAttributes } from './entities/ArchivedPlayerAttributes.js';
import { ArchivedPlayerOverallStats } from './entities/ArchivedPlayerOverallStats.js';
import { ArchivedTeam } from './entities/ArchivedTeam.js';
import { AvailableTeam } from './entities/AvailableTeam.js';
import { BidHistory } from './entities/BidHistory.js';
import { Fixture } from './entities/Fixture.js';
import { Gameworld } from './entities/Gameworld.js';
import { League } from './entities/League.js';
import { LeagueRules } from './entities/LeagueRules.js';
import { Manager } from './entities/Manager.js';
import { Player } from './entities/Player.js';
import { PlayerAttributes } from './entities/PlayerAttributes.js';
import { PlayerMatchHistory } from './entities/PlayerMatchHistory.js';
import { PlayerOverallStats } from './entities/PlayerOverallStats.js';
import { Purchases } from './entities/Purchases.js';
import { ScoutedPlayer } from './entities/ScoutedPlayer.js';
import { ScoutingRequest } from './entities/ScoutingRequest.js';
import { Team } from './entities/Team.js';
import { TeamTrainingSlot } from './entities/TeamTrainingSlot.js';
import { Transactions } from './entities/Transactions.js';
import { TransferListedPlayer } from './entities/TransferListedPlayer.js';
import { TransferRequest } from './entities/TransferRequest.js';

// Determine if we're running in AWS Lambda environment
const isLambda = !!process.env.AWS_LAMBDA_FUNCTION_NAME;

// Use /tmp directory in Lambda environment
const tempDir = isLambda ? '/tmp' : process.cwd() + '/temp';

export default defineConfig({
  entities: [
    AppVersion,
    ArchivedManager,
    ArchivedPlayer,
    ArchivedPlayerAttributes,
    ArchivedPlayerOverallStats,
    ArchivedTeam,
    AvailableTeam,
    BidHistory,
    Fixture,
    League,
    LeagueRules,
    Manager,
    PlayerAttributes,
    PlayerMatchHistory,
    PlayerOverallStats,
    Player,
    Purchases,
    ScoutedPlayer,
    ScoutingRequest,
    Gameworld,
    Team,
    TransferListedPlayer,
    TransferRequest,
    TeamTrainingSlot,
    Transactions,
  ],
  //clientUrl,
  dbName: process.env.DATABASE_NAME || 'jfg',
  driver: PostgreSqlDriver,
  host: process.env.DATABASE_URL,
  port: Number(process.env.DATABASE_PORT) || 5432,
  user: process.env.DATABASE_USER,
  password: process.env.DATABASE_PASSWORD,
  debug: process.env.STAGE === 'local',
  metadataProvider: TsMorphMetadataProvider,
  // Disable logging in production for performance
  logger: process.env.STAGE === 'local' ? (msg) => console.log(msg) : () => {},
  migrations: {
    path: path.join(process.cwd(), 'src/migrations'),
    glob: '!(*.d).{js,ts}',
  },
  discovery:
    process.env.STAGE === 'local'
      ? {
          warnWhenNoEntities: true,
          requireEntitiesArray: true, // Always require explicit entity list
          alwaysAnalyseProperties: false, // Don't analyze properties when not needed
          disableDynamicFileAccess: isLambda, // Disable dynamic file access in Lambda
        }
      : {
          warnWhenNoEntities: false,
          requireEntitiesArray: true,
          alwaysAnalyseProperties: false,
          disableDynamicFileAccess: true, // Always disable for faster startup
        },
  strict: true,
  validate: false, // Disable validation for performance in Lambda
  extensions: [EntityGenerator, Migrator],
  // Use /tmp directory for temporary files in Lambda environment
  baseDir: isLambda ? tempDir : undefined,
  // Set metadata cache directory path for MikroORM
  metadataCache: {
    enabled: false,
    options: { cacheDir: tempDir, adapter: isLambda ? 'memory' : 'memory' },
  },
  forceEntityConstructor: true,
  validateRequired: false,
  // Disable schema validation in production
  ensureDatabase: false,
});
