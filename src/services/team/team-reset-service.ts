import { Manager } from '@/entities/Manager.js';
import { Player } from '@/entities/Player.js';
import { PlayerAttributes } from '@/entities/PlayerAttributes.js';
import { PlayerMatchHistory } from '@/entities/PlayerMatchHistory.js';
import { PlayerOverallStats } from '@/entities/PlayerOverallStats.js';
import { Team } from '@/entities/Team.js';
import { generateRandomTeamName } from '@/functions/generate/random-team-name.js';
import { EntityManager } from '@mikro-orm/core';
import { logger } from '@/utils/logger.js';

export interface TeamResetService {
  resetTeamForAI(team: Team, manager: Manager): Promise<void>;
  removeManagerFromTeam(manager: Manager): Promise<void>;
  cleanupPlayerData(players: Player[]): Promise<void>;
}

export class TeamResetServiceImpl implements TeamResetService {
  constructor(private readonly em: EntityManager) {}

  /**
   * Reset a team for AI management after archiving the manager
   * @param team The team to reset
   * @param manager The manager being archived
   */
  async resetTeamForAI(team: Team, manager: Manager): Promise<void> {
    logger.info('Resetting team for AI management', {
      teamId: team.teamId,
      managerId: manager.managerId,
      oldTeamName: team.teamName
    });

    try {
      // Generate a new random team name
      const newTeamName = generateRandomTeamName();
      team.teamName = newTeamName;

      // Remove manager association
      team.manager = undefined;

      // Clear selection order as AI will manage this
      team.selectionOrder = [];

      // Reset training level to default
      team.trainingLevel = 1;

      logger.info('Team reset for AI management', {
        teamId: team.teamId,
        newTeamName,
        managerId: manager.managerId
      });

    } catch (error) {
      logger.error('Error resetting team for AI', {
        teamId: team.teamId,
        managerId: manager.managerId,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * Remove manager from their team
   * @param manager The manager to remove from their team
   */
  async removeManagerFromTeam(manager: Manager): Promise<void> {
    logger.info('Removing manager from team', {
      managerId: manager.managerId,
      teamId: manager.team?.teamId
    });

    try {
      // Remove team association from manager
      manager.team = undefined;
      manager.gameworldId = undefined;

      logger.info('Manager removed from team', {
        managerId: manager.managerId
      });

    } catch (error) {
      logger.error('Error removing manager from team', {
        managerId: manager.managerId,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * Clean up player data that doesn't need to be preserved
   * @param players The players to clean up
   */
  async cleanupPlayerData(players: Player[]): Promise<void> {
    logger.info('Cleaning up player data', {
      playerCount: players.length
    });

    try {
      // Remove PlayerMatchHistory for all players (as specified in requirements)
      for (const player of players) {
        // Delete match history
        await this.em.nativeDelete(PlayerMatchHistory, {
          player: player.playerId
        });

        // Reset transfer listing status
        player.isTransferListed = false;

        // Reset retirement status
        player.retiringAtEndOfSeason = false;

        // Reset energy to full
        player.energy = 100;

        // Clear any injuries
        player.injuredUntil = undefined;

        // Clear suspensions
        player.suspendedForGames = 0;
      }

      logger.info('Player data cleanup completed', {
        playerCount: players.length
      });

    } catch (error) {
      logger.error('Error cleaning up player data', {
        playerCount: players.length,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * Complete team reset process including all cleanup
   * @param team The team to reset
   * @param manager The manager being archived
   * @param players The players to clean up
   */
  async completeTeamReset(team: Team, manager: Manager, players: Player[]): Promise<void> {
    logger.info('Starting complete team reset process', {
      teamId: team.teamId,
      managerId: manager.managerId,
      playerCount: players.length
    });

    try {
      // Reset the team for AI management
      await this.resetTeamForAI(team, manager);

      // Remove manager from team
      await this.removeManagerFromTeam(manager);

      // Clean up player data
      await this.cleanupPlayerData(players);

      logger.info('Complete team reset process finished', {
        teamId: team.teamId,
        managerId: manager.managerId,
        newTeamName: team.teamName
      });

    } catch (error) {
      logger.error('Error in complete team reset process', {
        teamId: team.teamId,
        managerId: manager.managerId,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }
}
