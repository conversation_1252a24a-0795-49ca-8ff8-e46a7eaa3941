import { NotificationCategory } from '@/model/manager.js';
import { NotificationManager } from '@/services/notifications/NotificationManager.js';
import { ManagerFactory } from '@/testing/factories/managerFactory.js';
import { beforeEach, describe, expect, it, vi } from 'vitest';

// Mock the SQS service
vi.mock('../sqs/sqs.ts', () => ({
  SQS: vi.fn().mockImplementation(() => ({
    send: vi.fn().mockResolvedValue(undefined),
  })),
}));

// Mock Expo
vi.mock('expo-server-sdk', () => ({
  Expo: vi.fn().mockImplementation(() => ({
    sendPushNotificationsAsync: vi.fn().mockResolvedValue([{ status: 'ok', id: 'test-id' }]),
  })),
  isExpoPushToken: vi.fn().mockReturnValue(true),
}));

describe('NotificationManager - Outbid Notifications', () => {
  let notificationManager: NotificationManager;
  let mockRepositories: any;

  // Mock manager with notification preferences
  const mockManager = ManagerFactory.build({
    managerId: 'test-manager-id',
    gameworldId: 'test-gameworld',
  });

  beforeEach(() => {
    vi.clearAllMocks();
    notificationManager = NotificationManager.getInstance();

    mockRepositories = {
      inboxRepository: {
        createMessage: vi.fn().mockResolvedValue(undefined),
      },
    };

    notificationManager.assignManagerPreferences(mockManager, mockRepositories);
  });

  describe('outbidNotification', () => {
    it('should create correct notification content for outbid alert', async () => {
      const playerName = 'John Smith';
      const newAuctionPrice = 250000;
      const outbiddingTeamName = 'Manchester United';

      // Spy on the private sendNotification method
      const sendNotificationSpy = vi.spyOn(notificationManager as any, 'sendNotification');

      await notificationManager.outbidNotification(playerName, newAuctionPrice, outbiddingTeamName);

      expect(sendNotificationSpy).toHaveBeenCalledWith({
        email: {
          subject: `You've been outbid on ${playerName}!`,
          content: `<p>Bad news! You've been outbid on ${playerName}.</p>
              <p>The auction price has increased to Q250,000 thanks to a bid from ${outbiddingTeamName}.</p>
              <p>If you still want this player, you'll need to place a higher bid before the auction ends. Time to dig deeper into those pockets!</p>`,
          title: 'Outbid Alert!',
        },
        push: {
          title: 'Outbid Alert!',
          body: `You've been outbid on ${playerName}. The auction price has increased to Q250,000.`,
        },
        inbox: {
          title: 'Outbid Alert!',
          content: `You've been outbid on ${playerName}. The auction price has increased to Q250,000 thanks to a bid from ${outbiddingTeamName}. 
        If you still want this player, you'll need to place a higher bid before the auction ends. Time to dig deeper into those pockets!`,
        },
        category: NotificationCategory.TRANSFERS,
      });
    });

    it('should format large numbers correctly in notification', async () => {
      const playerName = 'Lionel Messi';
      const newAuctionPrice = 1500000; // 1.5 million
      const outbiddingTeamName = 'Real Madrid';

      const sendNotificationSpy = vi.spyOn(NotificationManager.prototype, 'outbidNotification');

      await notificationManager.outbidNotification(playerName, newAuctionPrice, outbiddingTeamName);

      const calledWith = sendNotificationSpy.mock.calls[0];
      expect(calledWith).toEqual(['Lionel Messi', 1500000, 'Real Madrid']);
    });

    it('should store notification in inbox', async () => {
      const playerName = 'Test Player';
      const newAuctionPrice = 100000;
      const outbiddingTeamName = 'Test Team';

      await notificationManager.outbidNotification(playerName, newAuctionPrice, outbiddingTeamName);

      expect(mockRepositories.inboxRepository.createMessage).toHaveBeenCalledWith(
        mockManager.gameworldId,
        mockManager.team?.teamId,
        expect.any(Number),
        expect.stringContaining("You've been outbid on Test Player"),
        expect.stringContaining('Outbid Alert!')
      );
    });
  });
});
