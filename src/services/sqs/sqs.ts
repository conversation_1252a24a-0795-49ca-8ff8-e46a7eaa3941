import { logger } from '@/utils/logger.js';
import type { Tracer } from '@aws-lambda-powertools/tracer';
import {
  DeleteMessageCommand,
  ReceiveMessageCommand,
  ReceiveMessageCommandInput,
  SendMessageBatchCommand,
  SendMessageCommand,
  SendMessageCommandInput,
  SQSClient,
  SQSClientConfig,
} from '@aws-sdk/client-sqs';
import { SendMessageBatchRequestEntry } from '@aws-sdk/client-sqs/dist-types/models/models_0.js';

export interface SQSServiceOptions {
  configuration?: SQSClientConfig;
  tracer?: Tracer;
}

export class SQS {
  private readonly sqs: SQSClient;

  constructor(protected readonly _options: SQSServiceOptions) {
    const sqsOptions: SQSClientConfig = {
      ..._options.configuration,
      // Add this configuration
      useQueueUrlAsEndpoint: true,
    };

    this.sqs = new SQSClient(sqsOptions);

    if (_options.tracer) {
      this.sqs = _options.tracer.captureAWSv3Client(this.sqs);
    }
  }

  async send(
    queueUrl: string,
    payload: string,
    additionalOpts: Partial<SendMessageCommandInput> = {}
  ) {
    try {
      return await this.sqs.send(
        new SendMessageCommand({
          QueueUrl: queueUrl,
          MessageBody: payload,
          ...additionalOpts,
        })
      );
    } catch (error) {
      logger.error('Error sending message to SQS', { error, queueUrl });
      throw error;
    }
  }

  async sendBatch(
    queueUrl: string,
    entries: SendMessageBatchRequestEntry[],
    additionalOpts: Partial<SendMessageCommandInput> = {}
  ) {
    const MAX_BATCH_SIZE = 10;

    try {
      for (let i = 0; i < entries.length; i += MAX_BATCH_SIZE) {
        const batch = entries.slice(i, i + MAX_BATCH_SIZE);
        await this.sqs.send(
          new SendMessageBatchCommand({
            QueueUrl: queueUrl,
            Entries: batch,
            ...additionalOpts,
          })
        );
      }
    } catch (error) {
      logger.error('Error sending batch to SQS', { error, queueUrl });
      throw error;
    }
  }

  async delete(queueUrl: string, receiptHandle: string) {
    return this.sqs.send(
      new DeleteMessageCommand({
        QueueUrl: queueUrl,
        ReceiptHandle: receiptHandle,
      })
    );
  }

  async receive(
    queueUrl: string,
    maxMessages: number,
    waitTimeSeconds: number,
    additionalOpts: Partial<ReceiveMessageCommandInput> = {}
  ) {
    return this.sqs.send(
      new ReceiveMessageCommand({
        QueueUrl: queueUrl,
        MaxNumberOfMessages: maxMessages,
        WaitTimeSeconds: waitTimeSeconds,
        ...additionalOpts,
      })
    );
  }
}
