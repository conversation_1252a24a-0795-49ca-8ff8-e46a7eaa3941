import { Manager } from '@/entities/Manager.js';
import { Team } from '@/entities/Team.js';
import { TeamMovement } from '@/functions/league/logic/LeagueProcessorV2.js';
import {
  LeagueMovementData,
  LeagueMovementService,
} from '@/services/gameworld/league-movement-service.js';
import { SQS } from '@/services/sqs/sqs.js';
import { GeneratePlayersEvent } from '@/types/generated/generate-players-event.js';
import { logger } from '@/utils/logger.js';
import { tracer } from '@/utils/tracer.js';
import { raw } from '@mikro-orm/core';
import { EntityManager } from '@mikro-orm/postgresql';

const sqs = new SQS({ tracer });

/**
 * Service responsible for handling all database operations related to league movement
 */
export class LeagueMovementDatabaseService {
  constructor() {}

  /**
   * Execute all league movement database operations within a transaction
   */
  async executeLeagueMovement(
    em: EntityManager,
    data: LeagueMovementData,
    leagueMovementService: LeagueMovementService
  ): Promise<void> {
    const { gameworldId, leagues, allTeams, movements, sortedLeagues } = data;

    logger.debug('Executing league movement database operations', {
      gameworldId,
      totalTeams: allTeams.length,
      totalMovements: movements.length,
    });

    // 0. Add 1 to trophy count for the manager that was top of the league
    await em.nativeUpdate(
      Manager,
      {
        team: {
          $in: Array.from(sortedLeagues.values())
            .at(0)!
            .map((t) => t.teamId),
        },
      },
      { trophies: raw(`trophies + 1`) }
    );

    // 1. Award prize money to all teams
    await leagueMovementService.awardPrizeMoney(em, sortedLeagues, leagues);

    // 2. Update team leagues and reset standings
    await this.updateTeamLeaguesOptimized(em, allTeams, movements, gameworldId);

    // 3. Manage AvailableTeam records
    await leagueMovementService.manageAvailableTeams(em, gameworldId, movements, allTeams, leagues);

    // 4. Optimize fixture deletion (do this before generating new fixtures)
    await leagueMovementService.optimizeFixtureDeletion(em, gameworldId);

    logger.debug('All league movement database operations completed', { gameworldId });
  }

  /**
   * Optimized version of updateTeamLeagues that uses batch operations
   */
  private async updateTeamLeaguesOptimized(
    em: EntityManager,
    teams: Team[],
    movements: TeamMovement[],
    gameworldId: string
  ): Promise<void> {
    logger.debug('Updating team leagues with optimized batch operations', {
      totalTeams: teams.length,
      totalMovements: movements.length,
    });

    // Create a map of movements for quick lookup
    const movementMap = new Map(movements.map((m) => [m.teamId, m]));

    // Separate teams into those that move and those that stay
    const movingTeams: Array<{ teamId: string; newLeagueId: string }> = [];
    const stationaryTeamIds: string[] = [];

    for (const team of teams) {
      const movement = movementMap.get(team.teamId);
      if (movement) {
        movingTeams.push({
          teamId: team.teamId,
          newLeagueId: movement.toLeagueId,
        });
      } else {
        stationaryTeamIds.push(team.teamId);
      }
    }

    // Batch update moving teams (league change + standings reset)
    if (movingTeams.length > 0) {
      const updatePromises = movingTeams.map(({ teamId, newLeagueId }) =>
        em.nativeUpdate(
          Team,
          { teamId, gameworldId },
          {
            league: newLeagueId,
            points: 0,
            goalsFor: 0,
            goalsAgainst: 0,
            wins: 0,
            draws: 0,
            losses: 0,
            played: 0,
          }
        )
      );

      await Promise.all(updatePromises);

      logger.debug('Updated moving teams', {
        count: movingTeams.length,
        teams: movingTeams.map((t) => ({ teamId: t.teamId, newLeague: t.newLeagueId })),
      });
    }

    // Batch reset standings for stationary teams
    if (stationaryTeamIds.length > 0) {
      await em.nativeUpdate(
        Team,
        { teamId: { $in: stationaryTeamIds }, gameworldId },
        {
          points: 0,
          goalsFor: 0,
          goalsAgainst: 0,
          wins: 0,
          draws: 0,
          losses: 0,
          played: 0,
        }
      );

      logger.debug('Reset standings for stationary teams', {
        count: stationaryTeamIds.length,
      });
    }

    logger.info('Successfully updated team leagues and reset standings', {
      totalTeams: teams.length,
      movedTeams: movingTeams.length,
      stationaryTeams: stationaryTeamIds.length,
    });
  }

  /**
   * Generate fixtures for all leagues using SQS queue for better retry handling
   */
  async generateFixturesForAllLeagues(leagues: any[]): Promise<void> {
    logger.debug('Generating fixtures for all leagues via SQS', { leagueCount: leagues.length });

    const queueUrl = process.env.FIXTURE_GENERATION_QUEUE_URL;

    if (!queueUrl) {
      throw new Error('FIXTURE_GENERATION_QUEUE_URL environment variable not set');
    }

    // Create fixture generation requests
    const fixtureGenerationRequests = leagues.map((league) => ({
      gameworldId: league.gameworld.id,
      leagueId: league.id,
      attempt: 1,
      requestedAt: Date.now(),
    }));

    // Send requests to SQS queue for processing
    await sqs.sendBatch(
      queueUrl,
      fixtureGenerationRequests.map((event) => ({
        Id: event.leagueId,
        MessageBody: JSON.stringify(event),
      }))
    );

    logger.info('Fixture generation requests sent to queue', {
      leagueCount: leagues.length,
      requestCount: fixtureGenerationRequests.length,
    });
  }

  /**
   * Process youth player requests for all teams (outside of transaction)
   */
  async processYouthPlayerRequests(sortedLeagues: Map<string, Team[]>): Promise<void> {
    logger.debug('Processing youth player requests');

    // Import SQS utility
    const { SQS } = await import('@/services/sqs/sqs.js');
    const { tracer } = await import('@/utils/tracer.js');

    const sqs = new SQS({ tracer });
    const allTeams: Team[] = [];

    // Collect all teams from all leagues
    for (const teams of sortedLeagues.values()) {
      allTeams.push(...teams);
    }

    if (allTeams.length === 0) {
      logger.debug('No teams found for youth player requests');
      return;
    }

    // Create youth player generation requests
    const youthPlayerRequests = allTeams.map(
      (team) =>
        ({
          gameworldId: team.gameworldId,
          leagueId: team.league.id,
          tier: team.tier,
          teamId: team.teamId,
          managerId: team.manager?.managerId,
          requiredPlayers: 3,
        }) as GeneratePlayersEvent
    );

    // Send requests to SQS queue
    const queueUrl = process.env.GENERATE_PLAYERS_QUEUE_URL!;
    if (!queueUrl) {
      throw new Error('GENERATE_PLAYERS_QUEUE_URL environment variable not set');
    }

    await sqs.sendBatch(
      queueUrl,
      youthPlayerRequests.map((event) => ({
        Id: event.teamId,
        MessageBody: JSON.stringify(event),
      }))
    );

    logger.info('Youth player requests sent to queue', {
      teamCount: allTeams.length,
      requestCount: youthPlayerRequests.length,
    });
  }
}
