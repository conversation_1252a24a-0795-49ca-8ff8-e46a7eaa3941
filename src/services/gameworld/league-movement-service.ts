import { AvailableTeam } from '@/entities/AvailableTeam.js';
import { Fixture } from '@/entities/Fixture.js';
import { Team } from '@/entities/Team.js';
import { LeagueProcessor, TeamMovement } from '@/functions/league/logic/LeagueProcessorV2.js';
import { Repositories } from '@/middleware/database/types.js';
import { logger } from '@/utils/logger.js';
import { raw } from '@mikro-orm/core';
import { EntityManager } from '@mikro-orm/postgresql';

export interface LeagueMovementData {
  gameworldId: string;
  leagues: any[];
  allTeams: Team[];
  movements: TeamMovement[];
  sortedLeagues: Map<string, Team[]>;
}

/**
 * Service responsible for processing end-of-season league movements
 */
export class LeagueMovementService {
  constructor(private repositories: Repositories) {}

  /**
   * Prepare all data needed for league movement processing
   */
  async prepareLeagueMovementData(gameworldId: string): Promise<LeagueMovementData> {
    logger.debug('Preparing league movement data', { gameworldId });

    // Get all leagues with their teams in a single request
    const leagues = await this.repositories.leagueRepository.getLeaguesByGameworld(
      gameworldId,
      true
    );

    logger.debug('Retrieved leagues with teams', {
      leaguesCount: leagues.length,
      teamsCount: leagues.reduce((count, league) => count + (league.teams?.length || 0), 0),
    });

    if (leagues.length === 0) {
      throw new Error(`No leagues found for gameworld: ${gameworldId}`);
    }

    // Create a map of sorted teams by league
    const sortedLeagues = new Map<string, Team[]>();
    const allTeams: Team[] = [];

    // Process each league and sort its teams
    for (const league of leagues) {
      if (!league.teams || league.teams.length === 0) {
        continue;
      }

      const teams = league.teams.getItems();
      allTeams.push(...teams);
      const sortedTeams = LeagueProcessor.sortTeamsInLeague(teams);
      sortedLeagues.set(league.id, sortedTeams);

      logger.debug('Sorted teams in league', {
        leagueId: league.id,
        teamCount: sortedTeams.length,
        standings: sortedTeams.map(LeagueProcessor.getTeamStandings),
      });
    }

    if (allTeams.length === 0) {
      throw new Error(`No teams found for gameworld: ${gameworldId}`);
    }

    const movements = LeagueProcessor.processPromotionsAndRelegations(sortedLeagues, leagues);

    return {
      gameworldId,
      leagues,
      allTeams,
      movements,
      sortedLeagues,
    };
  }

  /**
   * Award prize money to all teams based on their final league positions
   */
  async awardPrizeMoney(
    em: EntityManager,
    sortedLeagues: Map<string, Team[]>,
    leagues: any[]
  ): Promise<void> {
    logger.debug('Awarding prize money to teams');

    const leagueMap = new Map(leagues.map((league) => [league.id, league]));
    const prizeUpdates: Array<{ teamId: string; gameworldId: string; amount: number }> = [];

    // Calculate prize money for all teams
    for (const [leagueId, sortedTeams] of sortedLeagues) {
      const league = leagueMap.get(leagueId);
      if (!league?.leagueRules) {
        logger.warn('League rules not found for league', { leagueId });
        continue;
      }

      const leagueRules = league.leagueRules;
      const prizeDifference = leagueRules.maximumPrize - leagueRules.minimumPrize;
      const prizeDifferencePerPosition = prizeDifference / (leagueRules.teamCount - 1);

      sortedTeams.forEach((team, index) => {
        const prizeMoney =
          leagueRules.minimumPrize +
          (leagueRules.teamCount - 1 - index) * prizeDifferencePerPosition;

        if (prizeMoney > 0) {
          prizeUpdates.push({
            teamId: team.teamId,
            gameworldId: team.gameworldId,
            amount: prizeMoney,
          });
        }
      });
    }

    // Batch update all team balances using native SQL for better performance
    if (prizeUpdates.length > 0) {
      const updatePromises = prizeUpdates.map(({ teamId, gameworldId, amount }) =>
        em.nativeUpdate(Team, { teamId, gameworldId }, { balance: raw(`balance + ${amount}`) })
      );

      await Promise.all(updatePromises);

      logger.debug('Prize money awarded to teams', {
        teamsUpdated: prizeUpdates.length,
        totalPrizeMoney: prizeUpdates.reduce((sum, update) => sum + update.amount, 0),
      });
    }
  }

  /**
   * Manage AvailableTeam records based on team movements and manageable tiers
   */
  async manageAvailableTeams(
    em: EntityManager,
    gameworldId: string,
    movements: TeamMovement[],
    allTeams: Team[],
    leagues: any[]
  ): Promise<void> {
    // Get the gameworld to determine the highest manageable tier
    const gameworld = await this.repositories.gameworldRepository.getGameworld(gameworldId);
    if (!gameworld) {
      logger.warn('Gameworld not found, skipping AvailableTeam management', { gameworldId });
      return;
    }

    const highestManageableTier = gameworld.highestManageableTier;
    logger.debug('Managing AvailableTeam records', {
      gameworldId,
      highestManageableTier,
      movementsCount: movements.length,
    });

    // Create maps for quick lookup
    const teamMap = new Map(allTeams.map((team) => [team.teamId, team]));
    const leagueMap = new Map(leagues.map((league) => [league.id, league]));

    const teamsToRemove: string[] = [];
    const teamsToAdd: string[] = [];

    // Process each team movement
    for (const movement of movements) {
      const team = teamMap.get(movement.teamId);
      if (!team || team.manager) {
        continue; // Skip teams with managers
      }

      const fromLeague = leagueMap.get(movement.fromLeagueId);
      const toLeague = leagueMap.get(movement.toLeagueId);

      if (!fromLeague || !toLeague) {
        logger.warn('League not found for movement', {
          teamId: movement.teamId,
          fromLeagueId: movement.fromLeagueId,
          toLeagueId: movement.toLeagueId,
        });
        continue;
      }

      const oldTier = fromLeague.tier;
      const newTier = toLeague.tier;

      // Check if team moved from manageable to non-manageable tier
      if (oldTier >= highestManageableTier && newTier < highestManageableTier) {
        // Team promoted above manageable tier - remove from AvailableTeam
        teamsToRemove.push(team.teamId);
        logger.debug('Team to be removed from AvailableTeam (promoted above manageable tier)', {
          teamId: team.teamId,
          teamName: team.teamName,
          oldTier,
          newTier,
          highestManageableTier,
        });
      }
      // Check if team moved from non-manageable to manageable tier
      else if (oldTier < highestManageableTier && newTier >= highestManageableTier) {
        // Team relegated into manageable tier - add to AvailableTeam
        teamsToAdd.push(team.teamId);
        logger.debug('Team to be added to AvailableTeam (relegated into manageable tier)', {
          teamId: team.teamId,
          teamName: team.teamName,
          oldTier,
          newTier,
          highestManageableTier,
        });
      }
    }

    // Batch remove teams from AvailableTeam
    if (teamsToRemove.length > 0) {
      await em.nativeDelete(AvailableTeam, {
        gameworldId,
        teamId: { $in: teamsToRemove },
      });
      logger.debug('Removed teams from AvailableTeam', {
        count: teamsToRemove.length,
        teamIds: teamsToRemove,
      });
    }

    // Batch add teams to AvailableTeam
    if (teamsToAdd.length > 0) {
      // First check which teams are not already in the table
      const existingTeams = await em.find(AvailableTeam, {
        gameworldId,
        teamId: { $in: teamsToAdd },
      });
      const existingTeamIds = new Set(existingTeams.map((t: any) => t.teamId));
      const newTeamIds = teamsToAdd.filter((teamId) => !existingTeamIds.has(teamId));

      if (newTeamIds.length > 0) {
        const insertValues = newTeamIds.map((teamId) => ({
          id: require('uuid').v4(),
          gameworldId,
          teamId,
        }));

        await em.insertMany(AvailableTeam, insertValues);
        logger.debug('Added teams to AvailableTeam', {
          count: newTeamIds.length,
          teamIds: newTeamIds,
        });
      }
    }
  }

  /**
   * Optimize fixture deletion by using a more efficient approach
   */
  async optimizeFixtureDeletion(em: EntityManager, gameworldId: string): Promise<void> {
    logger.debug('Optimizing fixture deletion for gameworld', { gameworldId });

    const currentTime = Date.now();
    const batchSize = 500; // Smaller batch size to avoid timeouts

    let totalDeleted = 0;
    let hasMore = true;

    while (hasMore) {
      const remaining = await em.count(Fixture, {
        gameworldId,
        date: { $lt: currentTime },
      });
      if (remaining === 0) {
        logger.debug('No more fixtures to delete');
        hasMore = false;
        break;
      }

      // Get a batch of fixtures to delete
      const fixturesToDelete = await em.find(
        Fixture,
        {
          gameworldId,
          date: { $lt: currentTime },
        },
        {
          limit: batchSize,
          fields: ['fixtureId'], // Only select the ID to minimize memory usage
        }
      );

      if (fixturesToDelete.length === 0) {
        logger.debug('No more fixtures to delete');
        hasMore = false;
        break;
      }

      logger.debug('Fixtures to delete', {
        gameworldId,
        count: fixturesToDelete.length,
      });

      // Delete the batch
      const deletedCount = await em.nativeDelete(Fixture, {
        fixtureId: { $in: fixturesToDelete.map((f) => f.fixtureId) },
      });

      totalDeleted += deletedCount;

      logger.debug('Deleted batch of fixtures', {
        gameworldId,
        batchSize: deletedCount,
        totalDeleted,
      });

      // If we have deleted all the remaining
      if (remaining - deletedCount < 0) {
        logger.debug('Deleted last fixtures');
        hasMore = false;
      }
    }

    logger.debug('Completed fixture deletion', {
      gameworldId,
      totalDeleted,
    });
  }
}
