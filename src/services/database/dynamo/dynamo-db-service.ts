/* eslint-disable @typescript-eslint/no-explicit-any */
import { Configurable } from '@/services/config/configurable.js';
import { logger } from '@/utils/logger.js';
import { tracer } from '@/utils/tracer.js';
import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import {
  BatchGetCommand,
  BatchWriteCommand,
  DeleteCommand,
  DeleteCommandInput,
  DynamoDBDocumentClient,
  GetCommand,
  PutCommand,
  QueryCommand,
  QueryCommandInput,
  ScanCommand,
  TranslateConfig,
  UpdateCommand,
} from '@aws-sdk/lib-dynamodb';
import { BatchWriteResult, DatabaseService } from '../shared/database-service.js';
import { DynamoDbConfig } from './dynamo-db-config.js';

interface QueryResult<T> {
  items: T[];
  lastEvaluatedKey?: Record<string, any>;
}

export default class DynamoDbService implements DatabaseService, Configurable {
  readonly requiredEnvironmentVariables: string[] = ['REGION', 'DYNAMODB_PREFIX'];

  private readonly dbConfig: DynamoDbConfig;
  private readonly dynamoDbClient: DynamoDBClient;

  private documentClient: DynamoDBDocumentClient;

  constructor() {
    this.dbConfig = DynamoDbService.buildDatabaseConfig(process.env);

    if (process.env.NODE_ENV === 'development') {
      this.dynamoDbClient = new DynamoDBClient({
        region: this.dbConfig.region,
        endpoint: this.dbConfig.endpoint,
        credentials: {
          accessKeyId: this.dbConfig.accessKeyId!,
          secretAccessKey: this.dbConfig.secretAccessKey!,
        },
      });
    } else {
      this.dynamoDbClient = new DynamoDBClient({
        region: this.dbConfig.region,
      });
    }

    const translateConfig: TranslateConfig = {
      marshallOptions: { removeUndefinedValues: true },
    };
    this.documentClient = DynamoDBDocumentClient.from(this.dynamoDbClient, translateConfig);

    tracer.captureAWSv3Client(this.documentClient);
  }

  getRequiredEnvironmentVariables(): string[] {
    return this.requiredEnvironmentVariables;
  }

  getConfig(): { db: DynamoDbConfig } {
    return {
      db: this.dbConfig,
    };
  }

  async insert<T extends Record<string, any> | undefined>(
    tableName: string,
    objectToInsert: T
  ): Promise<string | null> {
    await this.documentClient.send(
      new PutCommand({ TableName: this.getTableName(tableName), Item: objectToInsert })
    );
    return null;
  }

  async insertWithId<T extends Record<string, any> | undefined>(
    tableName: string,
    key: { [key: string]: any },
    objectToInsert: T
  ): Promise<void> {
    await this.documentClient.send(new PutCommand({ TableName: tableName, Item: objectToInsert }));
  }

  async batchInsert<T extends Record<string, any>>(
    tableName: string,
    items: T[]
  ): Promise<BatchWriteResult<T>> {
    const BATCH_SIZE = 25;
    const results = {
      successful: [] as T[],
      failed: [] as Array<{ item: T; error: Error }>,
    };
    const formattedTableName = this.getTableName(tableName);

    // Early return if no items to process
    if (!items.length) {
      return results;
    }

    // Process items in chunks
    for (let i = 0; i < items.length; i += BATCH_SIZE) {
      const batchStart = Date.now();
      const chunk = items.slice(i, i + BATCH_SIZE);
      const writeRequests = chunk.map((item) => ({
        PutRequest: { Item: item },
      }));

      try {
        // Only send BatchWrite if we have items to process
        if (writeRequests.length === 0) continue;

        const response = await this.documentClient.send(
          new BatchWriteCommand({
            RequestItems: {
              [formattedTableName]: writeRequests,
            },
          })
        );

        // Mark successfully processed items
        const processedItems = chunk.slice(
          0,
          chunk.length - (response.UnprocessedItems?.[formattedTableName]?.length || 0)
        );
        results.successful.push(...processedItems);

        // Handle unprocessed items with retries
        let unprocessedItems = response.UnprocessedItems?.[formattedTableName] || [];
        let retryCount = 0;
        const MAX_RETRIES = 3;

        while (unprocessedItems.length > 0 && retryCount < MAX_RETRIES) {
          retryCount++;
          await new Promise((resolve) => setTimeout(resolve, 100 * Math.pow(2, retryCount - 1)));

          const retryResponse = await this.documentClient.send(
            new BatchWriteCommand({
              RequestItems: {
                [formattedTableName]: unprocessedItems,
              },
            })
          );

          const newlyProcessedCount =
            unprocessedItems.length -
            (retryResponse.UnprocessedItems?.[formattedTableName]?.length || 0);

          if (newlyProcessedCount > 0) {
            const newlyProcessedItems = chunk.slice(
              chunk.length - unprocessedItems.length,
              chunk.length - unprocessedItems.length + newlyProcessedCount
            );
            results.successful.push(...newlyProcessedItems);
          }

          unprocessedItems = retryResponse.UnprocessedItems?.[formattedTableName] || [];
        }

        // Add remaining unprocessed items to failed results
        if (unprocessedItems.length > 0) {
          const failedItems = unprocessedItems
            .filter((req) => req.PutRequest?.Item)
            .map((req) => req.PutRequest!.Item as T);

          results.failed.push(
            ...failedItems.map((item) => ({
              item,
              error: new Error('Failed after max retries'),
            }))
          );
        }

        logger.debug('Batch insert chunk completed', {
          chunkSize: chunk.length,
          duration: Date.now() - batchStart,
          retries: retryCount,
        });
      } catch (error) {
        logger.error('Batch insert chunk failed', {
          error,
          chunkSize: chunk.length,
          duration: Date.now() - batchStart,
        });

        results.failed.push(
          ...chunk.map((item) => ({
            item,
            error: error instanceof Error ? error : new Error(String(error)),
          }))
        );
      }
    }

    logger.info('Batch insert operation completed', {
      totalItems: items.length,
      successful: results.successful.length,
      failed: results.failed.length,
    });

    return results;
  }

  private getTableName(tableName: string) {
    if (this.dbConfig.tablePrefix) return `${this.dbConfig.tablePrefix}-${tableName}`;
    return tableName;
  }

  async update<T>(tableName: string, key: { [key: string]: any }, partialObject: Partial<T>) {
    const formattedTableName = this.getTableName(tableName);

    // Build update expression for only the fields we want to update
    let updateExpression = 'set';
    const ExpressionAttributeNames: Record<string, string> = {};
    const ExpressionAttributeValues: Record<string, any> = {};

    for (const [topKey, value] of Object.entries(partialObject)) {
      updateExpression += ` #${topKey} = :${topKey},`;
      ExpressionAttributeNames[`#${topKey}`] = topKey;
      ExpressionAttributeValues[`:${topKey}`] = value;
    }

    // Remove trailing comma
    updateExpression = updateExpression.slice(0, -1);

    logger.debug('Update expression', {
      updateExpression,
      ExpressionAttributeNames,
      ExpressionAttributeValues,
    });

    try {
      await this.documentClient.send(
        new UpdateCommand({
          TableName: formattedTableName,
          Key: key,
          UpdateExpression: updateExpression,
          ExpressionAttributeNames,
          ExpressionAttributeValues,
          // Add the following to prevent DynamoDB from removing unspecified attributes
          ReturnValues: 'UPDATED_NEW',
        })
      );
    } catch (error) {
      logger.error(`Failed to update item in table ${formattedTableName}`, { error });
      throw error;
    }
  }

  async get<T>(tableName: string, key: { [key: string]: any }): Promise<T> {
    const input = { TableName: this.getTableName(tableName), Key: key };
    logger.debug('Get item', { input });
    const result = await this.documentClient.send(new GetCommand(input));
    return result.Item as T;
  }

  async batchGet<T>(tableName: string, keys: Array<{ [key: string]: any }>): Promise<T[]> {
    if (!keys.length) return [];

    const formattedTableName = this.getTableName(tableName);
    const BATCH_SIZE = 25; // DynamoDB limit for BatchGetItem
    const results: T[] = [];

    // Process in batches of 25 (DynamoDB limit)
    for (let i = 0; i < keys.length; i += BATCH_SIZE) {
      const batchStart = Date.now();
      const batchKeys = keys.slice(i, i + BATCH_SIZE);

      try {
        const response = await this.documentClient.send(
          new BatchGetCommand({
            RequestItems: {
              [formattedTableName]: {
                Keys: batchKeys,
              },
            },
          })
        );

        // Add successfully retrieved items to results
        const items = (response.Responses?.[formattedTableName] as T[]) || [];
        results.push(...items);

        // Handle unprocessed keys with retries
        let unprocessedKeys = response.UnprocessedKeys?.[formattedTableName]?.Keys || [];
        let retryCount = 0;
        const MAX_RETRIES = 3;

        while (unprocessedKeys.length > 0 && retryCount < MAX_RETRIES) {
          retryCount++;

          // Exponential backoff
          const delay = Math.pow(2, retryCount) * 100;
          await new Promise((resolve) => setTimeout(resolve, delay));

          logger.debug('Retrying unprocessed keys', {
            retryCount,
            unprocessedKeysCount: unprocessedKeys.length,
          });

          const retryResponse = await this.documentClient.send(
            new BatchGetCommand({
              RequestItems: {
                [formattedTableName]: {
                  Keys: unprocessedKeys,
                },
              },
            })
          );

          // Add newly retrieved items to results
          const retryItems = (retryResponse.Responses?.[formattedTableName] as T[]) || [];
          results.push(...retryItems);

          // Update unprocessed keys for next iteration
          unprocessedKeys = retryResponse.UnprocessedKeys?.[formattedTableName]?.Keys || [];
        }

        if (unprocessedKeys.length > 0) {
          logger.warn('Some keys could not be retrieved after max retries', {
            unprocessedKeysCount: unprocessedKeys.length,
          });
        }

        logger.debug('Batch get completed', {
          batchSize: batchKeys.length,
          retrievedCount: items.length,
          duration: Date.now() - batchStart,
          retries: retryCount,
        });
      } catch (error) {
        logger.error('Batch get failed', {
          error,
          batchSize: batchKeys.length,
          duration: Date.now() - batchStart,
        });
        // Continue with the next batch even if this one failed
      }
    }

    return results;
  }

  async query<T>(
    tableName: string,
    keyConditions: {
      hashKey: { name: string; value: string };
      rangeKey?: { name: string; value: string };
    },
    indexName?: string,
    options?: {
      limit?: number;
      exclusiveStartKey?: Record<string, any>;
      projection?: string[];
      filterExpression?: string;
      expressionAttributeValues?: Record<string, any>;
      expressionAttributeNames?: Record<string, string>;
    }
  ): Promise<QueryResult<T>> {
    const params: QueryCommandInput = {
      TableName: this.getTableName(tableName),
      KeyConditionExpression: '#hk = :hv' + (keyConditions.rangeKey ? ' AND #rk = :rv' : ''),
      ExpressionAttributeNames: {
        '#hk': keyConditions.hashKey.name,
        ...(keyConditions.rangeKey && { '#rk': keyConditions.rangeKey.name }),
        // Add projection attribute names if provided
        ...(options?.projection &&
          options.projection.reduce(
            (acc, attr) => ({
              ...acc,
              [`#${attr}`]: attr,
            }),
            {}
          )),
        ...options?.expressionAttributeNames,
      },
      ExpressionAttributeValues: {
        ':hv': keyConditions.hashKey.value,
        ...(keyConditions.rangeKey && { ':rv': keyConditions.rangeKey.value }),
        ...options?.expressionAttributeValues,
      },
      FilterExpression: options?.filterExpression,
      Limit: options?.limit,
      ExclusiveStartKey: options?.exclusiveStartKey,
    };

    // Add ProjectionExpression if projection is provided
    if (options?.projection) {
      params.ProjectionExpression = options.projection.map((attr) => `#${attr}`).join(', ');
    }

    if (indexName) {
      params.IndexName = indexName;
    }

    logger.debug('Querying table', { params });
    const result = await this.documentClient.send(new QueryCommand(params));
    return {
      items: (result.Items || []) as T[],
      lastEvaluatedKey: result.LastEvaluatedKey,
    };
  }

  async delete(
    tableName: string,
    key: { [key: string]: any },
    options?: Partial<DeleteCommandInput>
  ) {
    try {
      await this.documentClient.send(
        new DeleteCommand({ TableName: this.getTableName(tableName), Key: key, ...options })
      );
    } catch (e) {
      logger.error('Error deleting item', { error: e, tableName, key, options });
      throw e;
    }
  }

  async scan<T>(
    tableName: string,
    options?: {
      filterExpression?: string;
      expressionAttributeNames?: Record<string, string>;
      expressionAttributeValues?: Record<string, any>;
      limit?: number;
      exclusiveStartKey?: Record<string, any>;
    }
  ): Promise<{ items: T[]; lastEvaluatedKey?: Record<string, any> }> {
    try {
      const scanCommand = {
        TableName: this.getTableName(tableName),
        Limit: options?.limit,
        FilterExpression: options?.filterExpression,
        ExpressionAttributeNames: options?.expressionAttributeNames,
        ExpressionAttributeValues: options?.expressionAttributeValues,
        LastEvaluatedKey: options?.exclusiveStartKey,
      };

      const result = await this.documentClient.send(new ScanCommand(scanCommand));
      return {
        items: (result.Items || []) as T[],
        lastEvaluatedKey: result.LastEvaluatedKey,
      };
    } catch (e) {
      logger.error('Error scanning table', { error: e, tableName, options });
      throw e;
    }
  }

  async batchUpdate<T>(
    tableName: string,
    updates: Array<{
      key: { [key: string]: any };
      updates: Partial<T>;
    }>
  ): Promise<BatchWriteResult<T>> {
    const results: BatchWriteResult<T> = {
      successful: [],
      failed: [],
    };

    // Process each update individually but in parallel
    const updatePromises = updates.map(async (update) => {
      try {
        await this.update(tableName, update.key, update.updates);
        return { success: true, item: update as T };
      } catch (error) {
        return {
          success: false,
          item: update as T,
          error: error instanceof Error ? error : new Error(String(error)),
        };
      }
    });

    const updateResults = await Promise.all(updatePromises);

    // Categorize results
    updateResults.forEach((result) => {
      if (result.success) {
        results.successful.push(result.item);
      } else {
        results.failed.push({
          item: result.item,
          error: result.error!,
        });
      }
    });

    if (results.failed.length > 0) {
      logger.error(`BatchUpdate: ${results.failed.length} items failed to update`);
    }

    return results;
  }

  async batchDelete<T extends Record<string, any>>(
    tableName: string,
    keys: Array<{ [key: string]: any }>
  ): Promise<BatchWriteResult<T>> {
    const BATCH_SIZE = 25;
    const results: BatchWriteResult<T> = { successful: [], failed: [] };
    const formattedTableName = this.getTableName(tableName);

    for (let i = 0; i < keys.length; i += BATCH_SIZE) {
      const chunk = keys.slice(i, i + BATCH_SIZE);
      const deleteRequests = chunk.map((key) => ({
        DeleteRequest: { Key: key },
      }));

      try {
        const response = await this.documentClient.send(
          new BatchWriteCommand({
            RequestItems: { [formattedTableName]: deleteRequests },
          })
        );

        // Handle unprocessed items with retries (optional, similar to batchInsert)
        // For simplicity, mark all as successful here
        // @ts-ignore
        results.successful.push(...chunk);
      } catch (error) {
        results.failed.push(
          ...chunk.map((key) => ({
            item: key as T,
            error: error instanceof Error ? error : new Error(String(error)),
          }))
        );
      }
    }

    return results;
  }

  private static buildDatabaseConfig(environment: typeof process.env): DynamoDbConfig {
    return {
      type: 'dynamodb',
      region: environment.REGION!,
      endpoint: environment.DB_ENDPOINT,
      tablePrefix: environment.DYNAMODB_PREFIX!,
      accessKeyId: environment.AWS_ACCESS_KEY_ID,
      secretAccessKey: environment.AWS_SECRET_ACCESS_KEY,
    };
  }
}
