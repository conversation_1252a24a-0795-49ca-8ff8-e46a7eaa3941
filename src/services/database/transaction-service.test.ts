import { TransactionService } from '@/services/database/transaction-service.js';
import { MikroOrmService } from '@/storage-interface/mikro-orm-service.js';
import { EntityManager } from '@mikro-orm/postgresql';
import { beforeEach, describe, expect, it, vi } from 'vitest';

describe('TransactionService', () => {
  let service: TransactionService;
  let mockMikroOrmService: MikroOrmService;
  let mockEntityManager: EntityManager;

  beforeEach(() => {
    mockEntityManager = {
      transactional: vi.fn()
    } as any;

    mockMikroOrmService = {
      getEntityManager: vi.fn().mockReturnValue(mockEntityManager)
    } as any;

    service = new TransactionService(mockMikroOrmService);
  });

  describe('executeInTransaction', () => {
    it('should execute operation within a transaction successfully', async () => {
      // Arrange
      const mockOperation = vi.fn().mockResolvedValue('success');
      const mockTxEm = {} as EntityManager;
      
      mockEntityManager.transactional = vi.fn().mockImplementation(async (callback) => {
        return await callback(mockTxEm);
      });

      // Act
      const result = await service.executeInTransaction(mockOperation);

      // Assert
      expect(result).toBe('success');
      expect(mockEntityManager.transactional).toHaveBeenCalledOnce();
      expect(mockOperation).toHaveBeenCalledWith(mockTxEm);
    });

    it('should rollback transaction when operation fails', async () => {
      // Arrange
      const error = new Error('Operation failed');
      const mockOperation = vi.fn().mockRejectedValue(error);
      const mockTxEm = {} as EntityManager;
      
      mockEntityManager.transactional = vi.fn().mockImplementation(async (callback) => {
        return await callback(mockTxEm);
      });

      // Act & Assert
      await expect(service.executeInTransaction(mockOperation)).rejects.toThrow('Operation failed');
      expect(mockEntityManager.transactional).toHaveBeenCalledOnce();
      expect(mockOperation).toHaveBeenCalledWith(mockTxEm);
    });

    it('should log transaction start and completion', async () => {
      // Arrange
      const mockOperation = vi.fn().mockResolvedValue('success');
      const mockTxEm = {} as EntityManager;
      
      mockEntityManager.transactional = vi.fn().mockImplementation(async (callback) => {
        return await callback(mockTxEm);
      });

      // Act
      await service.executeInTransaction(mockOperation);

      // Assert
      expect(mockEntityManager.transactional).toHaveBeenCalledOnce();
    });
  });

  describe('executeMultipleInTransaction', () => {
    it('should execute multiple operations within a single transaction', async () => {
      // Arrange
      const mockOperation1 = vi.fn().mockResolvedValue('result1');
      const mockOperation2 = vi.fn().mockResolvedValue('result2');
      const mockOperation3 = vi.fn().mockResolvedValue('result3');
      const operations = [mockOperation1, mockOperation2, mockOperation3];
      const mockTxEm = {} as EntityManager;
      
      mockEntityManager.transactional = vi.fn().mockImplementation(async (callback) => {
        return await callback(mockTxEm);
      });

      // Act
      const results = await service.executeMultipleInTransaction(operations);

      // Assert
      expect(results).toEqual(['result1', 'result2', 'result3']);
      expect(mockEntityManager.transactional).toHaveBeenCalledOnce();
      expect(mockOperation1).toHaveBeenCalledWith(mockTxEm);
      expect(mockOperation2).toHaveBeenCalledWith(mockTxEm);
      expect(mockOperation3).toHaveBeenCalledWith(mockTxEm);
    });

    it('should rollback all operations if one fails', async () => {
      // Arrange
      const mockOperation1 = vi.fn().mockResolvedValue('result1');
      const mockOperation2 = vi.fn().mockRejectedValue(new Error('Operation 2 failed'));
      const mockOperation3 = vi.fn().mockResolvedValue('result3');
      const operations = [mockOperation1, mockOperation2, mockOperation3];
      const mockTxEm = {} as EntityManager;
      
      mockEntityManager.transactional = vi.fn().mockImplementation(async (callback) => {
        return await callback(mockTxEm);
      });

      // Act & Assert
      await expect(service.executeMultipleInTransaction(operations)).rejects.toThrow('Operation 2 failed');
      expect(mockEntityManager.transactional).toHaveBeenCalledOnce();
      expect(mockOperation1).toHaveBeenCalledWith(mockTxEm);
      expect(mockOperation2).toHaveBeenCalledWith(mockTxEm);
      expect(mockOperation3).not.toHaveBeenCalled(); // Should not reach this operation
    });

    it('should handle empty operations array', async () => {
      // Arrange
      const operations: Array<(em: EntityManager) => Promise<any>> = [];
      const mockTxEm = {} as EntityManager;
      
      mockEntityManager.transactional = vi.fn().mockImplementation(async (callback) => {
        return await callback(mockTxEm);
      });

      // Act
      const results = await service.executeMultipleInTransaction(operations);

      // Assert
      expect(results).toEqual([]);
      expect(mockEntityManager.transactional).toHaveBeenCalledOnce();
    });
  });
});
