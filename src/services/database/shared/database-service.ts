export interface BatchWriteResult<T> {
  successful: T[];
  failed: Array<{ item: T; error: Error }>;
}

export interface QueryResult<T> {
  items: T[];
  lastEvaluatedKey?: Record<string, any>;
}

export interface DatabaseService {
  insert<T extends Record<string, any> | undefined>(
    tableName: string,
    objectToInsert: T
  ): Promise<string | null>;

  get<T>(tableName: string, key: { [key: string]: any }): Promise<T>;

  batchGet<T>(tableName: string, keys: Array<{ [key: string]: any }>): Promise<T[]>;

  update<T>(
    tableName: string,
    key: { [key: string]: any },
    partialObject: Partial<T>
  ): Promise<void>;

  delete(tableName: string, key: { [key: string]: any }): Promise<void>;

  insertWithId<T extends Record<string, any> | undefined>(
    collectionPath: string,
    key: { [key: string]: any },
    objectToInsert: T
  ): Promise<void>;

  batchInsert<T extends Record<string, any>>(
    tableName: string,
    items: T[]
  ): Promise<BatchWriteResult<T>>;

  query<T>(
    tableName: string,
    keyConditions: {
      hashKey: { name: string; value: string };
      rangeKey?: { name: string; value: string };
    },
    indexName?: string,
    options?: {
      limit?: number;
      exclusiveStartKey?: Record<string, any>;
      projection?: string[];
    }
  ): Promise<QueryResult<T>>;

  batchDelete<T extends Record<string, any>>(
    tableName: string,
    keys: Array<{ [key: string]: any }>
  ): Promise<BatchWriteResult<T>>;
}
