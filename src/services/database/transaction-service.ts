import { MikroOrmService } from '@/storage-interface/mikro-orm-service.js';
import { logger } from '@/utils/logger.js';
import { EntityManager } from '@mikro-orm/postgresql';

/**
 * Service for managing database transactions
 */
export class TransactionService {
  constructor(private mikroOrmService: MikroOrmService) {}

  /**
   * Execute a function within a database transaction
   * @param operation The operation to execute within the transaction
   * @returns The result of the operation
   */
  async executeInTransaction<T>(
    operation: (em: EntityManager) => Promise<T>
  ): Promise<T> {
    const em = this.mikroOrmService.getEntityManager();
    
    return em.transactional(async (txEm) => {
      try {
        logger.debug('Starting database transaction');
        const result = await operation(txEm);
        logger.debug('Database transaction completed successfully');
        return result;
      } catch (error) {
        logger.error('Database transaction failed, rolling back', { error });
        throw error;
      }
    });
  }

  /**
   * Execute multiple operations within a single transaction
   * @param operations Array of operations to execute
   * @returns Array of results from each operation
   */
  async executeMultipleInTransaction<T>(
    operations: Array<(em: EntityManager) => Promise<T>>
  ): Promise<T[]> {
    return this.executeInTransaction(async (em) => {
      const results: T[] = [];
      for (const operation of operations) {
        const result = await operation(em);
        results.push(result);
      }
      return results;
    });
  }
}
