import { Manager } from '@/entities/Manager.js';
import { PurchaseStatus } from '@/entities/Purchases.js';
import {
  RevenueCatCancelReason,
  RevenueCatEventType,
  WebhookEvent,
} from '@/functions/iap/types.js';
import { createMockRepositories, mockManagerRepository } from '@/testing/mockRepositories.js';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { IAPService } from './IAPService.js';

// Mock the NotificationManager
vi.mock('@/services/notifications/NotificationManager.js', () => ({
  NotificationManager: {
    getInstance: vi.fn().mockReturnValue({
      assignManagerPreferences: vi.fn(),
    }),
  },
}));

describe('IAPService', () => {
  let iapService: IAPService;
  let mockRepositories: ReturnType<typeof createMockRepositories>;
  let mockManager: Manager;

  beforeEach(() => {
    mockRepositories = createMockRepositories();
    iapService = new IAPService(mockRepositories);

    mockManager = new Manager();
    mockManager.managerId = 'test-manager-id';
    mockManager.magicSponges = 1;
    mockManager.scoutTokens = 2;
    mockManager.cardAppeals = 0;

    mockManagerRepository.getManagerById.mockResolvedValue(mockManager);

    vi.clearAllMocks();
  });

  describe('processWebhookEvent', () => {
    it('should handle INITIAL_PURCHASE event', async () => {
      const webhookEvent: WebhookEvent = {
        event: {
          event_timestamp_ms: Date.now(),
          product_id: 'jfg_superfan_1:sf1',
          transaction_id: '123456789012345',
          original_transaction_id: '123456789012345',
          app_user_id: 'test-manager-id',
          type: RevenueCatEventType.INITIAL_PURCHASE,
          id: 'event-id-123',
          app_id: 'app-123',
          environment: 'PRODUCTION',
          store: 'APP_STORE',
          purchased_at_ms: Date.now(),
          currency: 'USD',
          price: 4.99,
          price_in_purchased_currency: 4.99,
        },
        api_version: '1.0',
      };

      await iapService.processWebhookEvent(webhookEvent);

      expect(mockRepositories.managerRepository.getManagerById).toHaveBeenCalledWith(
        'test-manager-id'
      );
      expect(mockRepositories.purchaseRepository.upsertPurchase).toHaveBeenCalled();
      expect(mockRepositories.managerRepository.updateMagicSpongeCount).toHaveBeenCalledWith(
        'test-manager-id',
        5
      );
    });

    it('should handle CANCELLATION event', async () => {
      const webhookEvent: WebhookEvent = {
        event: {
          event_timestamp_ms: Date.now(),
          product_id: 'jfg_superfan_1:sf1',
          transaction_id: '123456789012345',
          app_user_id: 'test-manager-id',
          type: RevenueCatEventType.CANCELLATION,
          id: 'event-id-123',
          app_id: 'app-123',
          environment: 'PRODUCTION',
          cancel_reason: RevenueCatCancelReason.UNSUBSCRIBE,
        },
        api_version: '1.0',
      };

      await iapService.processWebhookEvent(webhookEvent);

      expect(mockRepositories.purchaseRepository.updatePurchaseStatus).toHaveBeenCalledWith(
        '123456789012345',
        PurchaseStatus.CANCELLED,
        undefined,
        'UNSUBSCRIBE'
      );
    });

    it('should handle REFUND event and remove rewards', async () => {
      const webhookEvent: WebhookEvent = {
        event: {
          event_timestamp_ms: Date.now(),
          product_id: 'jfg_superfan_1:sf1',
          transaction_id: '123456789012345',
          app_user_id: 'test-manager-id',
          type: RevenueCatEventType.REFUND,
          id: 'event-id-123',
          app_id: 'app-123',
          environment: 'PRODUCTION',
          cancel_reason: RevenueCatCancelReason.CUSTOMER_SUPPORT,
        },
        api_version: '1.0',
      };

      await iapService.processWebhookEvent(webhookEvent);

      expect(mockRepositories.purchaseRepository.updatePurchaseStatus).toHaveBeenCalledWith(
        '123456789012345',
        PurchaseStatus.REFUNDED,
        undefined,
        'CUSTOMER_SUPPORT'
      );

      // Should remove rewards (but not go below 0)
      expect(mockRepositories.managerRepository.updateMagicSpongeCount).toHaveBeenCalledWith(
        'test-manager-id',
        0 // Current (1) - reward (5) = -4, but will be clamped to 0
      );
    });

    it('should handle consumable purchase with no rewards', async () => {
      const webhookEvent: WebhookEvent = {
        event: {
          event_timestamp_ms: Date.now(),
          product_id: 'jfg_sweetfa_1',
          transaction_id: '123456789012345',
          original_transaction_id: '123456789012345',
          app_user_id: 'test-manager-id',
          type: RevenueCatEventType.NON_RENEWING_PURCHASE,
          id: 'event-id-123',
          app_id: 'app-123',
          environment: 'PRODUCTION',
          store: 'APP_STORE',
          purchased_at_ms: Date.now(),
          currency: 'USD',
          price: 0.99,
          price_in_purchased_currency: 0.99,
        },
        api_version: '1.0',
      };

      await iapService.processWebhookEvent(webhookEvent);

      expect(mockRepositories.purchaseRepository.upsertPurchase).toHaveBeenCalled();
      // Should not award any rewards for this product
      expect(mockRepositories.managerRepository.updateMagicSpongeCount).not.toHaveBeenCalled();
      expect(mockRepositories.managerRepository.updateCardAppealCount).not.toHaveBeenCalled();
    });

    it('should throw error if manager not found', async () => {
      mockManagerRepository.getManagerById.mockResolvedValue(null);

      const webhookEvent: WebhookEvent = {
        event: {
          event_timestamp_ms: Date.now(),
          app_user_id: 'non-existent-manager',
          type: RevenueCatEventType.INITIAL_PURCHASE,
          id: 'event-id-123',
          app_id: 'app-123',
          environment: 'PRODUCTION',
        },
        api_version: '1.0',
      };

      await expect(iapService.processWebhookEvent(webhookEvent)).rejects.toThrow(
        'Manager with ID non-existent-manager not found'
      );
    });
  });
});
