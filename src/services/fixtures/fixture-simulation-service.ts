import { Player } from '@/entities/Player.js';
import { Team } from '@/entities/Team.js';
import { updatePlayerEnergy } from '@/functions/fixtures/fixtureSimulationUtils.js';
import { Repositories } from '@/middleware/database/types.js';
import { MatchReport } from '@/model/matchReport.js';
import { NotificationManager } from '@/services/notifications/NotificationManager.js';
import { MatchEngine } from '@/simulation/match-engine.js';
import { generateTrialist, sortPlayersByPosition } from '@/simulation/player-utils.js';
import { GamePlayer, Team as GameTeam, SimulationResult } from '@/simulation/types.js';
import { TeamRepository } from '@/storage-interface/teams/team-repository.interface.js';
import { SimulateFixturesEvent } from '@/types/generated/index.js';
import { logger } from '@/utils/logger.js';
import { setAndReturnSeededRandom } from '@/utils/seeded-random.js';

const notificationManager = NotificationManager.getInstance();

export interface FixtureSimulationData {
  fixture: SimulateFixturesEvent;
  homeTeam: Team;
  awayTeam: Team;
  seed: number;
  result: SimulationResult;
  homeTeamConverted: GameTeam;
  awayTeamConverted: GameTeam;
}

/**
 * Service responsible for simulating football fixtures
 */
export class FixtureSimulationService {
  constructor(private repositories: Repositories) {}

  /**
   * Prepare teams for simulation by converting them to GameTeam format
   */
  prepareTeamsForSimulation(
    homeTeam: Team,
    awayTeam: Team
  ): {
    homeTeamConverted: GameTeam;
    awayTeamConverted: GameTeam;
  } {
    const homeTeamConverted = this.convertTeam(homeTeam, this.repositories.teamRepository);
    const awayTeamConverted = this.convertTeam(awayTeam, this.repositories.teamRepository);

    return { homeTeamConverted, awayTeamConverted };
  }

  /**
   * Run the match simulation
   */
  runSimulation(homeTeam: GameTeam, awayTeam: GameTeam): SimulationResult {
    const matchEngine = new MatchEngine(homeTeam, awayTeam);
    return matchEngine.simulate();
  }

  /**
   * Simulate a complete fixture and return all the data needed for database updates
   */
  async simulateFixture(fixture: SimulateFixturesEvent): Promise<FixtureSimulationData> {
    logger.debug('Preparing fixture simulation', { fixtureId: fixture.fixtureId });

    // Get teams
    const homeTeam = await this.getTeam(fixture.homeTeamId, fixture.gameworldId);
    const awayTeam = await this.getTeam(fixture.awayTeamId, fixture.gameworldId);

    if (!homeTeam || !awayTeam) {
      throw new Error(`Teams not found for fixture ${fixture.fixtureId}`);
    }

    // Set random seed for deterministic simulation
    const seed = setAndReturnSeededRandom();

    // Prepare teams for simulation
    const { homeTeamConverted, awayTeamConverted } = this.prepareTeamsForSimulation(
      homeTeam,
      awayTeam
    );

    // Run simulation
    logger.debug('Running match simulation', { fixtureId: fixture.fixtureId });
    const result = this.runSimulation(homeTeamConverted, awayTeamConverted);

    return {
      fixture,
      homeTeam,
      awayTeam,
      seed,
      result,
      homeTeamConverted,
      awayTeamConverted,
    };
  }

  /**
   * Send match notifications to managers
   */
  async sendMatchNotifications(
    homeTeam: Team,
    awayTeam: Team,
    result: SimulationResult
  ): Promise<void> {
    const score = result.stats.score;
    const matchReport: MatchReport = {
      homeTeam: homeTeam,
      awayTeam: awayTeam,
      homeScore: score[0],
      awayScore: score[1],
      homeScorers: result.stats.scorers?.filter((scorer) => scorer.team === 0) ?? [],
      awayScorers: result.stats.scorers?.filter((scorer) => scorer.team === 1) ?? [],
    };

    if (homeTeam.manager) {
      notificationManager.assignManagerPreferences(homeTeam.manager, this.repositories);
      await notificationManager.sendMatchReport(matchReport, true);
    }
    if (awayTeam.manager) {
      notificationManager.assignManagerPreferences(awayTeam.manager, this.repositories);
      await notificationManager.sendMatchReport(matchReport, false);
    }
  }

  private async getTeam(teamId: string, gameworldId: string): Promise<Team | null> {
    const team = await this.repositories.teamRepository.getTeamForSimulation(gameworldId, teamId);
    if (!team) {
      logger.error('Failed to get team', { teamId, gameworldId });
      return null;
    }
    return team;
  }

  private createGamePlayer(player: Player): GamePlayer {
    return {
      player: player,
      stats: {
        yellowCards: 0,
        redCards: 0,
        passesCompleted: 0,
        passesAttempted: 0,
        successfulBallCarries: 0,
        ballCarriesAttempted: 0,
        shots: 0,
        shotsOnTarget: 0,
        goals: 0,
        saves: 0,
        tackles: 0,
        fouls: 0,
      },
    };
  }

  private sortPlayersBySortOrder(
    gameworldId: string,
    teamId: string,
    teamRepository: TeamRepository,
    players: Player[],
    selectionOrder: string[]
  ): Player[] {
    const playerMap = new Map(players.map((p) => [p.playerId, p]));

    // Get players in selection order
    const sortedPlayers = selectionOrder
      .map((id) => playerMap.get(id))
      .filter((p): p is Player => !!p);

    // Add remaining players not in selection order
    const remainingPlayers = players.filter((p) => !selectionOrder.includes(p.playerId));
    sortedPlayers.push(...remainingPlayers);

    // Helper function to check if player is available
    const isPlayerAvailable = (player: Player): boolean =>
      (!player.injuredUntil || Number(player.injuredUntil) < Date.now()) &&
      player.suspendedForGames === 0;

    // Split into starting 11 and the rest (potential subs)
    const starting11 = sortedPlayers.slice(0, 11);
    const potentialSubs = sortedPlayers.slice(11);

    // Find available players from potential subs
    const availableSubs = potentialSubs.filter(isPlayerAvailable);

    // Final starting 11 array
    const finalStarting11: Player[] = [];

    // Check each player in starting 11 and replace if unavailable
    for (const player of starting11) {
      if (isPlayerAvailable(player)) {
        finalStarting11.push(player);
      } else {
        // Player is unavailable, try to find a replacement from available subs
        const replacement = availableSubs.shift(); // Get first available sub
        if (replacement) {
          finalStarting11.push(replacement);
        }
        // If no replacement available, we'll handle this later with trialists
      }
    }

    // Get remaining available subs (those not used as replacements)
    const finalSubs = availableSubs.slice(0, 5);

    // Generate trialists if needed for starting 11
    if (finalStarting11.length < 11) {
      const trialistsNeeded = 11 - finalStarting11.length;
      const trialists = generateTrialist(gameworldId, teamId, teamRepository, trialistsNeeded);
      finalStarting11.push(...trialists);
    }

    // Generate trialists if needed for subs
    if (finalSubs.length < 5) {
      const trialistsNeeded = 5 - finalSubs.length;
      const trialists = generateTrialist(gameworldId, teamId, teamRepository, trialistsNeeded);
      finalSubs.push(...trialists);
    }

    return [...finalStarting11, ...finalSubs];
  }

  private convertTeam(team: Team, teamRepository: TeamRepository): GameTeam {
    let players: GamePlayer[];
    const teamPlayers = team.players.getItems();
    updatePlayerEnergy(teamPlayers);

    // if there is no manager, then the team is not managed and we should try to sort the players
    // by logical position
    if (!team.manager) {
      players = sortPlayersByPosition(
        team.gameworldId,
        team.teamId,
        teamRepository,
        team.players.getItems()
      );
    } else {
      // Sort players by selectionOrder
      const selectionOrder = Array.isArray(team.selectionOrder) ? team.selectionOrder : [];
      const sorted = this.sortPlayersBySortOrder(
        team.gameworldId,
        team.teamId,
        teamRepository,
        team.players.getItems(),
        selectionOrder
      );
      players = sorted.map(this.createGamePlayer);
    }

    // remove extra players to make sure players array is 16 long
    players = players.slice(0, 16);
    // set the joinedMatchMinute to 0 for first 11
    players.slice(0, 11).forEach((player) => {
      player.joinedMatchMinute = 0;
    });

    return {
      gameworldId: team.gameworldId,
      teamId: team.teamId,
      teamName: team.teamName,
      standings: {
        points: team.points,
        goalsFor: team.goalsFor,
        goalsAgainst: team.goalsAgainst,
        wins: team.wins,
        draws: team.draws,
        losses: team.losses,
        played: team.played,
      },
      players,
    };
  }
}
