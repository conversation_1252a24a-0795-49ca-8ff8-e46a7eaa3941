import { Fixture } from '@/entities/Fixture.js';
import { Manager } from '@/entities/Manager.js';
import { Player } from '@/entities/Player.js';
import { PlayerMatchHistory } from '@/entities/PlayerMatchHistory.js';
import { PlayerOverallStats } from '@/entities/PlayerOverallStats.js';
import { Team } from '@/entities/Team.js';
import { Transactions } from '@/entities/Transactions.js';
import { Repositories } from '@/middleware/database/types.js';
import { FixtureDetails, MatchEvent, MatchStats } from '@/model/fixture.js';
import { GamePlayer, Team as GameTeam, SimulationResult } from '@/simulation/types.js';
import { calculateMatchExpenses, calculateMatchIncome } from '@/utils/economy.js';
import { logger } from '@/utils/logger.js';
import { seededRandom, seededRandomIntInRange } from '@/utils/seeded-random.js';
import { raw } from '@mikro-orm/core';
import { EntityManager, SqlEntityManager } from '@mikro-orm/postgresql';

const MAX_IMPROVEMENT = 0.5;

/**
 * Service responsible for handling all database operations related to fixture simulation
 */
export class FixtureDatabaseService {
  constructor(private repositories: Repositories) {}

  /**
   * Update all database entities for a completed fixture within a transaction
   */
  async updateFixtureResults(
    em: SqlEntityManager,
    fixtureId: string,
    gameworldId: string,
    leagueId: string,
    seed: number,
    stats: MatchStats,
    homeTeam: Team,
    awayTeam: Team,
    homeTeamConverted: GameTeam,
    awayTeamConverted: GameTeam,
    result: SimulationResult,
    fixtureDate: number
  ): Promise<void> {
    logger.debug('Updating fixture results in database', { fixtureId });

    // 1. Update fixture result
    await this.updateFixtureEntity(em, gameworldId, leagueId, fixtureId, seed, stats);

    // 2. Update team standings
    await this.updateTeamStandings(em, result, homeTeamConverted, awayTeamConverted);

    // 3. Update team balances
    await this.updateTeamBalances(em, homeTeam, awayTeam, homeTeamConverted, awayTeamConverted);

    // 4. Update player stats for those who played
    await this.updatePlayerStats(em, fixtureId, fixtureDate, [
      ...homeTeamConverted.players,
      ...awayTeamConverted.players,
    ]);

    // 5. Update post-match status for ALL players in both teams
    await this.updateAllPlayersPostMatch(em, homeTeam, awayTeam);

    // 6. Update manager stats
    await this.updateManagerStats(em, homeTeam, awayTeam, result);

    logger.debug('All fixture database updates completed', { fixtureId });
  }

  /**
   * Store fixture details in DynamoDB (separate from transaction)
   */
  async storeFixtureDetails(
    fixtureId: string,
    gameworldId: string,
    leagueId: string,
    homeTeamId: string,
    homeTeamName: string,
    awayTeamId: string,
    awayTeamName: string,
    date: number,
    stats: MatchStats,
    events: MatchEvent[],
    homePlayers: GamePlayer[],
    awayPlayers: GamePlayer[]
  ): Promise<void> {
    const fixtureDetails: FixtureDetails = {
      fixtureId,
      gameworldId,
      leagueId,
      homeTeamId,
      homeTeamName,
      awayTeamId,
      awayTeamName,
      date,
      stats,
      events,
      ttl: date + 30 * 24 * 60 * 60 * 1000, // 30 days after the fixture ends
      homePlayers: homePlayers.map((p) => ({
        playerId: p.player.playerId,
        playerName: p.player.firstName + ' ' + p.player.surname,
        joinedMatchMinute: p.joinedMatchMinute,
        leftMatchMinute: p.leftMatchMinute,
        rating: p.rating,
        sentOff: p.sentOff,
        injured: p.injured,
        stats: p.stats,
      })),
      awayPlayers: awayPlayers.map((p) => ({
        playerId: p.player.playerId,
        playerName: p.player.firstName + ' ' + p.player.surname,
        joinedMatchMinute: p.joinedMatchMinute,
        leftMatchMinute: p.leftMatchMinute,
        rating: p.rating,
        sentOff: p.sentOff,
        injured: p.injured,
        stats: p.stats,
      })),
    };

    // Store in DynamoDB via the fixture repository
    const dynamoDb = new (
      await import('@/services/database/dynamo/dynamo-db-service.js')
    ).default();
    await dynamoDb.insert(process.env.FIXTURE_DETAIL_TABLE_NAME!, fixtureDetails);
  }

  private async updateFixtureEntity(
    em: SqlEntityManager,
    gameworldId: string,
    leagueId: string,
    fixtureId: string,
    seed: number,
    stats: MatchStats
  ): Promise<void> {
    const fixture = await em.findOne(Fixture, {
      gameworldId,
      league: { id: leagueId },
      fixtureId,
    });

    if (!fixture) {
      throw new Error(`Fixture not found: ${gameworldId}, ${leagueId}, ${fixtureId}`);
    }

    fixture.score = stats.score;
    fixture.scorers = stats.scorers;
    fixture.played = true;
    fixture.seed = BigInt(seed);
    fixture.simulatedAt = BigInt(Date.now());

    em.persist(fixture);
  }

  private async updateTeamStandings(
    em: EntityManager,
    result: SimulationResult,
    homeGameTeam: GameTeam,
    awayGameTeam: GameTeam
  ): Promise<void> {
    const score = result.stats.score;

    // Calculate home team standings
    const homeStandings = {
      played: (homeGameTeam.standings.played ?? 0) + 1,
      points:
        homeGameTeam.standings.points + (score[0] > score[1] ? 3 : score[0] === score[1] ? 1 : 0),
      goalsFor: homeGameTeam.standings.goalsFor + score[0],
      goalsAgainst: homeGameTeam.standings.goalsAgainst + score[1],
      wins: homeGameTeam.standings.wins + (score[0] > score[1] ? 1 : 0),
      draws: homeGameTeam.standings.draws + (score[0] === score[1] ? 1 : 0),
      losses: homeGameTeam.standings.losses + (score[0] < score[1] ? 1 : 0),
    };

    // Calculate away team standings
    const awayStandings = {
      played: (awayGameTeam.standings.played ?? 0) + 1,
      points:
        awayGameTeam.standings.points + (score[1] > score[0] ? 3 : score[0] === score[1] ? 1 : 0),
      goalsFor: awayGameTeam.standings.goalsFor + score[1],
      goalsAgainst: awayGameTeam.standings.goalsAgainst + score[0],
      wins: awayGameTeam.standings.wins + (score[1] > score[0] ? 1 : 0),
      draws: awayGameTeam.standings.draws + (score[0] === score[1] ? 1 : 0),
      losses: awayGameTeam.standings.losses + (score[1] < score[0] ? 1 : 0),
    };

    // Use individual nativeUpdate calls (simpler and sufficient for 2 teams per fixture)
    await em.nativeUpdate(
      Team,
      { teamId: homeGameTeam.teamId, gameworldId: homeGameTeam.gameworldId },
      homeStandings
    );

    await em.nativeUpdate(
      Team,
      { teamId: awayGameTeam.teamId, gameworldId: awayGameTeam.gameworldId },
      awayStandings
    );
  }

  private async updateTeamBalances(
    em: EntityManager,
    homeTeam: Team,
    awayTeam: Team,
    homeGameTeam: GameTeam,
    awayGameTeam: GameTeam
  ): Promise<void> {
    const homeIncome = calculateMatchIncome(homeTeam);
    const homeExpenditure = calculateMatchExpenses(homeTeam, true);
    const awayExpenditure = calculateMatchExpenses(awayTeam, false);

    // Update home team balance
    if (homeIncome.length > 0 || homeExpenditure.length > 0) {
      const homeTransactions = [...homeIncome, ...homeExpenditure];
      const homeAmount = homeTransactions.reduce((acc, item) => acc + item.amount, 0);

      if (homeAmount !== 0) {
        await em.nativeUpdate(
          Team,
          { teamId: homeGameTeam.teamId, gameworldId: homeGameTeam.gameworldId },
          { balance: raw(` balance + ${homeAmount}`) }
        );
        for (const item of homeTransactions) {
          em.persist(
            new Transactions({
              gameworldId: homeGameTeam.gameworldId,
              team: em.getReference(Team, homeGameTeam.teamId),
              date: BigInt(Date.now()),
              amount: item.amount,
              type: item.type,
              details: JSON.stringify({}),
            })
          );
        }
      }
    }

    // Update away team balance
    if (awayExpenditure.length > 0) {
      const awayAmount = awayExpenditure.reduce((acc, item) => acc + item.amount, 0);

      if (awayAmount !== 0) {
        await em.nativeUpdate(
          Team,
          { teamId: awayGameTeam.teamId, gameworldId: awayGameTeam.gameworldId },
          { balance: raw(` balance + ${awayAmount}`) }
        );
        for (const item of awayExpenditure) {
          em.persist(
            new Transactions({
              gameworldId: awayGameTeam.gameworldId,
              team: em.getReference(Team, awayGameTeam.teamId),
              date: BigInt(Date.now()),
              amount: item.amount,
              type: item.type,
              details: JSON.stringify({}),
            })
          );
        }
      }
    }
  }

  private async updatePlayerStats(
    em: EntityManager,
    fixtureId: string,
    fixtureDate: number,
    players: GamePlayer[]
  ): Promise<void> {
    const eligiblePlayers = players.filter((p) => !p.player.playerId.startsWith('trialist-'));

    if (eligiblePlayers.length === 0) return;

    // FIXME: This is a temporary band aid on an issue thats happening upstream
    // Somehow we are getting duplicate players in the fixture data which shouldn't be possible
    // The stats are different so its as if they should be two different players
    // but they have the same player ID
    // Check for duplicate player IDs and log warnings
    const playerIdCounts = new Map<string, number>();
    const duplicatePlayerIds = new Set<string>();

    eligiblePlayers.forEach((p) => {
      const playerId = p.player.playerId;
      const count = (playerIdCounts.get(playerId) || 0) + 1;
      playerIdCounts.set(playerId, count);

      if (count > 1) {
        duplicatePlayerIds.add(playerId);
      }
    });

    // Log warnings for duplicates
    if (duplicatePlayerIds.size > 0) {
      logger.warn('Duplicate player IDs found in fixture data', {
        fixtureId,
        duplicatePlayerIds: Array.from(duplicatePlayerIds),
        duplicateCounts: Object.fromEntries(
          Array.from(duplicatePlayerIds).map((id) => [id, playerIdCounts.get(id)])
        ),
      });
    }

    // Deduplicate players - keep the first occurrence of each player ID
    const uniquePlayers = new Map<string, GamePlayer>();
    eligiblePlayers.forEach((gamePlayer) => {
      const playerId = gamePlayer.player.playerId;
      if (!uniquePlayers.has(playerId)) {
        uniquePlayers.set(playerId, gamePlayer);
      }
    });

    const deduplicatedPlayers = Array.from(uniquePlayers.values());

    // Get player IDs for bulk operations
    const playerIds = deduplicatedPlayers.map((p) => p.player.playerId);

    // Bulk load existing overall stats
    const existingStats = await em.find(PlayerOverallStats, {
      player: { $in: playerIds },
    });
    const existingStatsMap = new Map(existingStats.map((stat) => [stat.player.playerId, stat]));

    // Batch create match history entries
    const matchHistoryEntries = deduplicatedPlayers.map((gamePlayer) => {
      const player = gamePlayer.player;
      const matchStats = gamePlayer.stats;

      const newMatchHistory = new PlayerMatchHistory();
      newMatchHistory.fixtureId = fixtureId;
      newMatchHistory.fixture = em.getReference(Fixture, fixtureId);
      newMatchHistory.player = em.getReference(Player, player.playerId);
      newMatchHistory.yellowCards = matchStats.yellowCards;
      newMatchHistory.redCards = matchStats.redCards;
      newMatchHistory.passesCompleted = matchStats.passesCompleted;
      newMatchHistory.passesAttempted = matchStats.passesAttempted;
      newMatchHistory.successfulBallCarries = matchStats.successfulBallCarries;
      newMatchHistory.ballCarriesAttempted = matchStats.ballCarriesAttempted;
      newMatchHistory.shots = matchStats.shots;
      newMatchHistory.shotsOnTarget = matchStats.shotsOnTarget;
      newMatchHistory.goals = matchStats.goals;
      newMatchHistory.saves = matchStats.saves;
      newMatchHistory.tackles = matchStats.tackles;
      newMatchHistory.fouls = matchStats.fouls;
      newMatchHistory.matchRating = gamePlayer.rating;

      return newMatchHistory;
    });

    // Batch upsert all match history entries
    if (matchHistoryEntries.length > 0) {
      await em.upsertMany(PlayerMatchHistory, matchHistoryEntries);
    }

    // Collect overall stats updates for batch processing
    const overallStatsUpdates: PlayerOverallStats[] = [];

    const playersToUpdate = deduplicatedPlayers.map((gamePlayer) => {
      const player = gamePlayer.player;
      const matchStats = gamePlayer.stats;

      // Get or create overall stats
      let overallStats = existingStatsMap.get(player.playerId);
      if (!overallStats) {
        overallStats = new PlayerOverallStats();
        overallStats.player = em.getReference(Player, player.playerId);
        // Initialize all stats to 0
        overallStats.yellowCards = 0;
        overallStats.redCards = 0;
        overallStats.passesCompleted = 0;
        overallStats.passesAttempted = 0;
        overallStats.successfulBallCarries = 0;
        overallStats.ballCarriesAttempted = 0;
        overallStats.shots = 0;
        overallStats.shotsOnTarget = 0;
        overallStats.goals = 0;
        overallStats.saves = 0;
        overallStats.tackles = 0;
        overallStats.fouls = 0;
      }

      // Update the stats by adding match stats to existing values
      overallStats.yellowCards += matchStats.yellowCards;
      overallStats.redCards += matchStats.redCards;
      overallStats.passesCompleted += matchStats.passesCompleted;
      overallStats.passesAttempted += matchStats.passesAttempted;
      overallStats.successfulBallCarries += matchStats.successfulBallCarries;
      overallStats.ballCarriesAttempted += matchStats.ballCarriesAttempted;
      overallStats.shots += matchStats.shots;
      overallStats.shotsOnTarget += matchStats.shotsOnTarget;
      overallStats.goals += matchStats.goals;
      overallStats.saves += matchStats.saves;
      overallStats.tackles += matchStats.tackles;
      overallStats.fouls += matchStats.fouls;

      // Add to batch update list
      overallStatsUpdates.push(overallStats);

      player.lastMatchPlayed = BigInt(fixtureDate);

      // Add penalties for players who actually played
      if (matchStats.redCards > 0) {
        player.suspendedForGames = 2;
      }
      if (gamePlayer.injured) {
        // randomize number of hours between 10 and 96
        const random = seededRandom();
        const randomHours = Math.floor(Math.pow(random, 2) * 86) + 10;
        player.injuredUntil = BigInt(fixtureDate + 1000 * 60 * 60 * randomHours);
      }

      this.updatePlayerAttributes(player, gamePlayer.rating);

      return player;
    });

    // Batch upsert all overall stats
    if (overallStatsUpdates.length > 0) {
      await em.upsertMany(PlayerOverallStats, overallStatsUpdates);
    }

    // Persist all player updates
    for (const player of playersToUpdate) {
      em.persist(player);
    }
  }

  private async updateAllPlayersPostMatch(
    em: EntityManager,
    homeTeam: Team,
    awayTeam: Team
  ): Promise<void> {
    const allPlayers = [...homeTeam.players.getItems(), ...awayTeam.players.getItems()].filter(
      (player) => !player.playerId.startsWith('trialist-')
    );

    for (const player of allPlayers) {
      // Decrement suspension for all players (not just those who played)
      if (player.suspendedForGames > 0) {
        player.suspendedForGames -= 1;
      }

      // Clear expired injuries for all players
      if (Number(player.injuredUntil) && Number(player.injuredUntil) < Date.now()) {
        player.injuredUntil = undefined;
      }

      em.persist(player);
    }
  }

  /**
   * A player can improve by up to 0.1 in up to 3 different attributes as a maximum
   * A player's age will impact this maximum, with a 22 and under player improving the most and any player over 20
   * gradually reducing the maximum down to zero as they approach 30
   * @param player the player to update
   * @param rating the rating (1-10) of the player, which is used to determine how much they improve
   */
  private updatePlayerAttributes(player: Player, rating: number | undefined): void {
    if (!player.attributes || !rating) {
      return;
    }
    const attrs = player.attributes;
    const maxImprovement =
      player.age <= 22
        ? MAX_IMPROVEMENT
        : player.age >= 30
          ? 0
          : Math.max(0, MAX_IMPROVEMENT - (player.age - 22) * 0.01);
    const improvement = Math.min(maxImprovement, (rating / 10) * MAX_IMPROVEMENT);
    // get 3 random attributes to improve
    const attributeKeys = Object.keys(attrs).filter((key) => key.endsWith('Current'));
    const randomAttributes = new Set<string>();
    while (randomAttributes.size < 3 && randomAttributes.size < attributeKeys.length) {
      const randomIndex = Math.floor(seededRandomIntInRange(0, attributeKeys.length - 1));
      randomAttributes.add(attributeKeys[randomIndex]!);
    }
    for (const key of randomAttributes) {
      const currentKey = key as keyof typeof attrs;
      const potentialKey = key.replace('Current', 'Potential') as keyof typeof attrs;
      const currentValue = attrs[currentKey] as number;
      const potentialValue = attrs[potentialKey] as number;

      // Calculate the new current value, ensuring it does not exceed the potential
      // @ts-ignore
      attrs[currentKey] = Math.min(currentValue + improvement, potentialValue);
    }
  }

  private async updateManagerStats(
    em: SqlEntityManager,
    homeTeam: Team,
    awayTeam: Team,
    result: SimulationResult
  ) {
    if (!homeTeam.manager && !awayTeam.manager) {
      return;
    }

    const homeScore = result.stats.score[0];
    const awayScore = result.stats.score[1];

    // Reload managers within the transaction context to ensure we have the latest data
    // and that they are properly managed by this entity manager
    const homeManager = homeTeam.manager
      ? await em.findOneOrFail(Manager, { managerId: homeTeam.manager.managerId })
      : undefined;
    const awayManager = awayTeam.manager
      ? await em.findOneOrFail(Manager, { managerId: awayTeam.manager.managerId })
      : undefined;

    // Update home manager stats by adding to existing values
    if (homeManager) {
      homeManager.wins += homeScore > awayScore ? 1 : 0;
      homeManager.draws += homeScore === awayScore ? 1 : 0;
      homeManager.defeats += homeScore < awayScore ? 1 : 0;
      homeManager.goalsScored += homeScore;
      homeManager.goalsConceded += awayScore;
      em.persist(homeManager);
    }

    if (awayManager) {
      // Update away manager stats by adding to existing values
      awayManager.wins += awayScore > homeScore ? 1 : 0;
      awayManager.draws += awayScore === homeScore ? 1 : 0;
      awayManager.defeats += awayScore < homeScore ? 1 : 0;
      awayManager.goalsScored += awayScore;
      awayManager.goalsConceded += homeScore;
      em.persist(awayManager);
    }
  }
}
