import { Fixture } from '@/entities/Fixture.js';
import { Gameworld } from '@/entities/Gameworld.js';
import { FixtureGenerationDatabaseService } from '@/services/fixtures/fixture-generation-database-service.js';
import { FixtureFactory } from '@/testing/factories/fixtureFactory.js';
import {
  createMockRepositories,
  mockEntityManager,
  mockFixtureRepository,
  mockGameworldRepository,
} from '@/testing/mockRepositories.js';
import { beforeEach, describe, expect, it, vi } from 'vitest';

// Mock external dependencies
vi.mock('@/services/sqs/sqs.js', () => ({
  SQS: vi.fn().mockImplementation(() => ({
    send: vi.fn(),
    sendBatch: vi.fn(),
  })),
}));

describe('FixtureGenerationDatabaseService', () => {
  let service: FixtureGenerationDatabaseService;
  let repositories: ReturnType<typeof createMockRepositories>;

  beforeEach(() => {
    vi.clearAllMocks();
    repositories = createMockRepositories();
    service = new FixtureGenerationDatabaseService(repositories);

    // Set up environment variables
    process.env.FIXTURE_GENERATION_RETRY_QUEUE_URL =
      'https://sqs.us-east-1.amazonaws.com/123456789012/fixture-retry';
    process.env.FIXTURE_GENERATION_DLQ_URL =
      'https://sqs.us-east-1.amazonaws.com/123456789012/fixture-dlq';
  });

  describe('executeFixtureGeneration', () => {
    it('should execute all fixture generation operations', async () => {
      const mockFixtureGenerationService = {
        optimizeFixtureCleanup: vi.fn().mockResolvedValue(5),
        calculateSeasonEndDate: vi.fn().mockReturnValue(Date.now() + 86400000),
        getFixtureStatistics: vi.fn().mockReturnValue({
          teamCount: 2,
          fixtureCount: 2,
          firstFixtureDate: Date.now(),
          lastFixtureDate: Date.now() + 86400000,
          seasonDuration: 86400000,
        }),
        validateFixtureData: vi.fn(),
      };

      const fixtureData = {
        gameworldId: 'gw-1',
        leagueId: 'league-1',
        teams: [],
        fixtures: [
          FixtureFactory.build({ fixtureId: 'fixture-1' }),
          FixtureFactory.build({ fixtureId: 'fixture-2' }),
        ],
      };

      // Act
      await service.executeFixtureGeneration(
        mockEntityManager,
        fixtureData,
        mockFixtureGenerationService as any
      );

      // Assert
      expect(mockEntityManager.insertMany).toHaveBeenCalledWith(Fixture, expect.any(Array));
      expect(mockEntityManager.nativeUpdate).toHaveBeenCalledWith(
        Gameworld,
        { id: 'gw-1' },
        { endDate: expect.any(BigInt) }
      );
    });
  });

  describe('batchInsertFixtures', () => {
    it('should insert fixtures using native insert', async () => {
      const fixtures = [
        FixtureFactory.build({
          fixtureId: 'fixture-1',
          gameworldId: 'gw-1',
          date: BigInt(Date.now()),
        }),
      ];

      // Act
      await (service as any).batchInsertFixtures(mockEntityManager, fixtures);

      // Assert
      expect(mockEntityManager.insertMany).toHaveBeenCalledWith(Fixture, fixtures);
    });

    it('should handle empty fixtures array', async () => {
      // Act
      await (service as any).batchInsertFixtures(mockEntityManager, []);

      // Assert
      expect(mockEntityManager.insertMany).not.toHaveBeenCalled();
    });
  });

  describe('getGenerationStatus', () => {
    it('should return status with fixtures', async () => {
      // Arrange
      const fixtures = [
        FixtureFactory.build({ date: 1000n }),
        FixtureFactory.build({ date: 2000n }),
      ];
      const gameworld = { endDate: 3000 };

      mockFixtureRepository.getFixturesByLeague.mockResolvedValue(fixtures);
      mockGameworldRepository.getGameworld.mockResolvedValue(gameworld);

      // Act
      const result = await service.getGenerationStatus('gw-1', 'league-1');

      // Assert
      expect(result).toEqual({
        hasFixtures: true,
        fixtureCount: 2,
        lastGeneratedAt: 2000,
        seasonEndDate: 3000,
      });
    });

    it('should return status without fixtures', async () => {
      // Arrange
      mockFixtureRepository.getFixturesByLeague.mockResolvedValue([]);
      mockGameworldRepository.getGameworld.mockResolvedValue(null);

      // Act
      const result = await service.getGenerationStatus('gw-1', 'league-1');

      // Assert
      expect(result).toEqual({
        hasFixtures: false,
        fixtureCount: 0,
        lastGeneratedAt: null,
        seasonEndDate: null,
      });
    });
  });

  describe('recordSuccessMetrics', () => {
    it('should log success metrics', async () => {
      // Arrange
      const data = {
        gameworldId: 'gw-1',
        leagueId: 'league-1',
        fixtures: [FixtureFactory.build(), FixtureFactory.build()],
      } as any;

      // Act
      await service.recordSuccessMetrics(data, 1000);

      // Assert - This test mainly ensures the method doesn't throw
      // In a real implementation, you might verify metrics were sent to monitoring systems
    });
  });

  describe('recordFailureMetrics', () => {
    it('should log failure metrics', async () => {
      // Arrange
      const error = new Error('Test error');

      // Act
      await service.recordFailureMetrics('gw-1', 'league-1', error, 1000, 2);

      // Assert - This test mainly ensures the method doesn't throw
      // In a real implementation, you might verify metrics were sent to monitoring systems
    });
  });
});
