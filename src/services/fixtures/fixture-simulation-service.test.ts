import { Player } from '@/entities/Player.js';
import { FixtureSimulationService } from '@/services/fixtures/fixture-simulation-service.js';
import { PlayerFactory } from '@/testing/factories/playerFactory.js';
import { TeamsFactory } from '@/testing/factories/teamFactory.js';
import { createMockRepositories, mockTeamRepository } from '@/testing/mockRepositories.js';
import { SimulateFixturesEvent } from '@/types/generated/index.js';
import { setRandomSeed } from '@/utils/seeded-random.js';
import { Collection } from '@mikro-orm/core';
import { beforeEach, describe, expect, it, vi } from 'vitest';

// Mock the match engine
vi.mock('@/simulation/match-engine.js', () => ({
  MatchEngine: vi.fn().mockImplementation(() => ({
    simulate: vi.fn().mockReturnValue({
      stats: {
        score: [2, 1],
        possession: [60, 40],
        shots: [10, 8],
        shotsOnTarget: [5, 3],
        corners: [4, 2],
        fouls: [8, 6],
        yellowCards: [2, 1],
        redCards: [0, 0],
        passes: [400, 300],
        passAccuracy: [85, 80],
        tackles: [15, 12],
        interceptions: [8, 6],
        scorers: [
          {
            playerId: 'player1',
            playerName: 'Test Player',
            team: 0,
            goalTime: [{ minute: 25, half: 1 }],
          },
        ],
      },
      events: [
        {
          localisationId: 'goal',
          substitutions: { player: 'Test Player' },
          minute: 25,
          half: 1,
        },
      ],
    }),
  })),
}));

// Mock the notification manager
vi.mock('@/services/notifications/NotificationManager.js', () => ({
  NotificationManager: {
    getInstance: vi.fn().mockReturnValue({
      assignManagerPreferences: vi.fn(),
      sendMatchReport: vi.fn(),
    }),
  },
}));

// Mock utility functions
vi.mock('@/functions/fixtures/fixtureSimulationUtils.js', () => ({
  updatePlayerEnergy: vi.fn(),
}));

vi.mock('@/simulation/player-utils.js', () => ({
  generateTrialist: vi.fn().mockReturnValue([]),
  sortPlayersByPosition: vi.fn().mockReturnValue([]),
}));

vi.mock('@/utils/seeded-random.js', async () => {
  const actual = await vi.importActual('@/utils/seeded-random.js');
  return {
    ...actual,
    setAndReturnSeededRandom: vi.fn().mockReturnValue(12345),
  };
});

describe('FixtureSimulationService', () => {
  let service: FixtureSimulationService;

  beforeEach(() => {
    setRandomSeed(0.5);
    service = new FixtureSimulationService(createMockRepositories());
  });

  describe('simulateFixture', () => {
    it('should successfully simulate a fixture', async () => {
      // Arrange
      const fixture: SimulateFixturesEvent = {
        fixtureId: 'fixture-1',
        gameworldId: 'gameworld-1',
        leagueId: 'league-1',
        homeTeamId: 'home-team',
        awayTeamId: 'away-team',
        fixtureDate: Date.now(),
      };

      const homeTeam = TeamsFactory.build({
        teamId: 'home-team',
        gameworldId: 'gameworld-1',
        teamName: 'Home Team',
        manager: undefined,
        selectionOrder: [],
      });

      const awayTeam = TeamsFactory.build({
        teamId: 'away-team',
        gameworldId: 'gameworld-1',
        teamName: 'Away Team',
        manager: undefined,
        selectionOrder: [],
      });

      // Add some players to the teams
      const homePlayers = Array.from({ length: 16 }, () => PlayerFactory.build());
      const awayPlayers = Array.from({ length: 16 }, () => PlayerFactory.build());

      homeTeam.players = new Collection<Player>(homeTeam, homePlayers);
      awayTeam.players = new Collection<Player>(awayTeam, awayPlayers);

      mockTeamRepository.getTeamForSimulation
        .mockResolvedValueOnce(homeTeam)
        .mockResolvedValueOnce(awayTeam);

      // Act
      const result = await service.simulateFixture(fixture);

      // Assert
      expect(result).toBeDefined();
      expect(result.fixture).toEqual(fixture);
      expect(result.homeTeam).toEqual(homeTeam);
      expect(result.awayTeam).toEqual(awayTeam);
      expect(result.seed).toBe(12345);
      expect(result.result.stats.score).toEqual([2, 1]);
      expect(result.homeTeamConverted.teamId).toBe('home-team');
      expect(result.awayTeamConverted.teamId).toBe('away-team');
    });

    it('should throw error when home team is not found', async () => {
      // Arrange
      const fixture: SimulateFixturesEvent = {
        fixtureId: 'fixture-1',
        gameworldId: 'gameworld-1',
        leagueId: 'league-1',
        homeTeamId: 'home-team',
        awayTeamId: 'away-team',
        fixtureDate: Date.now(),
      };

      mockTeamRepository.getTeam
        .mockResolvedValueOnce(null)
        .mockResolvedValueOnce(TeamsFactory.build());

      // Act & Assert
      await expect(service.simulateFixture(fixture)).rejects.toThrow(
        'Teams not found for fixture fixture-1'
      );
    });

    it('should throw error when away team is not found', async () => {
      // Arrange
      const fixture: SimulateFixturesEvent = {
        fixtureId: 'fixture-1',
        gameworldId: 'gameworld-1',
        leagueId: 'league-1',
        homeTeamId: 'home-team',
        awayTeamId: 'away-team',
        fixtureDate: Date.now(),
      };

      mockTeamRepository.getTeam
        .mockResolvedValueOnce(TeamsFactory.build())
        .mockResolvedValueOnce(null);

      // Act & Assert
      await expect(service.simulateFixture(fixture)).rejects.toThrow(
        'Teams not found for fixture fixture-1'
      );
    });
  });

  describe('prepareTeamsForSimulation', () => {
    it('should convert teams to GameTeam format', () => {
      // Arrange
      const homeTeam = TeamsFactory.build({
        teamId: 'home-team',
        teamName: 'Home Team',
        points: 10,
        goalsFor: 5,
        goalsAgainst: 2,
      });

      const awayTeam = TeamsFactory.build({
        teamId: 'away-team',
        teamName: 'Away Team',
        points: 8,
        goalsFor: 3,
        goalsAgainst: 4,
      });

      // Add players to teams
      const homePlayers = Array.from({ length: 16 }, () => PlayerFactory.build());
      const awayPlayers = Array.from({ length: 16 }, () => PlayerFactory.build());

      homeTeam.players = new Collection<Player>(homeTeam, homePlayers);
      awayTeam.players = new Collection<Player>(awayTeam, awayPlayers);

      // Act
      const result = service.prepareTeamsForSimulation(homeTeam, awayTeam);

      // Assert
      expect(result.homeTeamConverted.teamId).toBe('home-team');
      expect(result.homeTeamConverted.teamName).toBe('Home Team');
      expect(result.homeTeamConverted.standings.points).toBe(10);
      expect(result.awayTeamConverted.teamId).toBe('away-team');
      expect(result.awayTeamConverted.teamName).toBe('Away Team');
      expect(result.awayTeamConverted.standings.points).toBe(8);
    });
  });

  describe('sendMatchNotifications', () => {
    it('should send notifications to managers when they exist', async () => {
      // Arrange
      const homeTeam = TeamsFactory.build({
        manager: { managerId: 'manager-1' } as any,
      });

      const awayTeam = TeamsFactory.build({
        manager: { managerId: 'manager-2' } as any,
      });

      const result = {
        stats: {
          score: [2, 1],
          scorers: [{ playerId: 'player1', playerName: 'Test Player', team: 0, goalTime: [] }],
        },
      } as any;

      // Act
      await service.sendMatchNotifications(homeTeam, awayTeam, result);

      // Assert
      // Verify that notification manager methods were called
      // (This would require more detailed mocking of the NotificationManager)
    });

    it('should not send notifications when no managers exist', async () => {
      // Arrange
      const homeTeam = TeamsFactory.build({ manager: undefined });
      const awayTeam = TeamsFactory.build({ manager: undefined });

      const result = {
        stats: {
          score: [2, 1],
          scorers: [],
        },
      } as any;

      // Act
      await service.sendMatchNotifications(homeTeam, awayTeam, result);

      // Assert
      // Should complete without error
    });
  });
});
