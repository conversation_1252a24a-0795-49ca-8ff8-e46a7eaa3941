import { Fixture } from '@/entities/Fixture.js';
import { Gameworld } from '@/entities/Gameworld.js';
import { Repositories } from '@/middleware/database/types.js';
import { FixtureDetails } from '@/model/fixture.js';
import DynamoDbService from '@/services/database/dynamo/dynamo-db-service.js';
import {
  FixtureGenerationData,
  FixtureGenerationService,
} from '@/services/fixtures/fixture-generation-service.js';
import { logger } from '@/utils/logger.js';
import { EntityManager } from '@mikro-orm/postgresql';

/**
 * Service responsible for handling all database operations related to fixture generation
 */
export class FixtureGenerationDatabaseService {
  private dynamoDb: DynamoDbService;

  constructor(private repositories: Repositories) {
    this.dynamoDb = new DynamoDbService();
  }

  /**
   * Execute all fixture generation database operations within a transaction
   */
  async executeFixtureGeneration(
    em: EntityManager,
    data: FixtureGenerationData,
    fixtureGenerationService: FixtureGenerationService
  ): Promise<void> {
    const { gameworldId, leagueId, fixtures } = data;
    const correlationId = `fixture-db-${gameworldId}-${leagueId}-${Date.now()}`;

    logger.debug('Executing fixture generation database operations', {
      gameworldId,
      leagueId,
      fixtureCount: fixtures.length,
      correlationId,
    });

    // 1. Insert new fixtures using batch operation
    await this.batchInsertFixtures(em, fixtures);

    // 2. Update gameworld end date
    const seasonEndDate = fixtureGenerationService.calculateSeasonEndDate(fixtures);
    await this.updateGameworldEndDate(em, gameworldId, seasonEndDate);

    logger.debug('All fixture generation database operations completed', {
      gameworldId,
      leagueId,
      fixturesInserted: fixtures.length,
      seasonEndDate,
      correlationId,
    });
  }

  /**
   * Batch insert fixtures using optimized database operations
   */
  private async batchInsertFixtures(em: EntityManager, fixtures: Fixture[]): Promise<void> {
    if (fixtures.length === 0) {
      logger.debug('No fixtures to insert');
      return;
    }

    logger.debug('Batch inserting fixtures', { count: fixtures.length });

    // Use native insert for better performance
    await em.insertMany(Fixture, fixtures);

    logger.debug('Batch fixture insertion completed', {
      insertedCount: fixtures.length,
    });
  }

  /**
   * Update gameworld end date
   */
  private async updateGameworldEndDate(
    em: EntityManager,
    gameworldId: string,
    endDate: number
  ): Promise<void> {
    logger.debug('Updating gameworld end date', { gameworldId, endDate });

    await em.nativeUpdate(Gameworld, { id: gameworldId }, { endDate: BigInt(endDate) });

    logger.debug('Gameworld end date updated', { gameworldId, endDate });
  }

  /**
   * Record fixture generation success metrics
   */
  async recordSuccessMetrics(data: FixtureGenerationData, processingTime: number): Promise<void> {
    const { gameworldId, leagueId, fixtures } = data;

    // Log success metrics for monitoring
    logger.info('Fixture generation completed successfully', {
      gameworldId,
      leagueId,
      processingTimeMs: processingTime,
    });

    // Here you could also send metrics to CloudWatch, DataDog, etc.
    // For now, we'll just log the metrics
  }

  /**
   * Record fixture generation failure metrics
   */
  async recordFailureMetrics(
    gameworldId: string,
    leagueId: string,
    error: Error,
    processingTime: number,
    attempt: number
  ): Promise<void> {
    // Log failure metrics for monitoring
    logger.error('Fixture generation failed', {
      gameworldId,
      leagueId,
      error: error.message,
      processingTimeMs: processingTime,
      attempt,
      errorType: error.constructor.name,
    });

    // Here you could also send error metrics to monitoring systems
  }

  /**
   * Get fixture generation status for monitoring
   */
  async getGenerationStatus(
    gameworldId: string,
    leagueId: string
  ): Promise<{
    hasFixtures: boolean;
    fixtureCount: number;
    lastGeneratedAt: number | null;
    seasonEndDate: number | null;
  }> {
    const fixtures = await this.repositories.fixtureRepository.getFixturesByLeague(
      gameworldId,
      leagueId
    );
    const gameworld = await this.repositories.gameworldRepository.getGameworld(gameworldId);

    return {
      hasFixtures: fixtures.length > 0,
      fixtureCount: fixtures.length,
      lastGeneratedAt:
        fixtures.length > 0 ? Math.max(...fixtures.map((f) => Number(f.date))) : null,
      seasonEndDate: Number(gameworld?.endDate) || null,
    };
  }

  async deleteCompletedFixtureDetails(gameworldId: string, leagueId: string): Promise<void> {
    // Query the GSI for matching items in DynamoDB
    const result = await this.dynamoDb.query<FixtureDetails>(
      process.env.FIXTURE_DETAIL_TABLE_NAME!,
      {
        hashKey: { name: 'gameworldId', value: gameworldId },
        rangeKey: { name: 'leagueId', value: leagueId },
      },
      'gameworldId_leagueId_index'
    );
    if (!result.items || result.items.length === 0) {
      logger.debug('No completed fixture details to delete', { gameworldId, leagueId });
      return;
    }

    // Prepare array of keys for batch delete
    const keys = result.items.map((item) => ({ fixtureId: item.fixtureId }));

    // Batch delete the items in chunks of 25
    for (let i = 0; i < keys.length; i += 25) {
      await this.dynamoDb.batchDelete(
        process.env.FIXTURE_DETAIL_TABLE_NAME!,
        keys.slice(i, i + 25)
      );
    }
  }
}
