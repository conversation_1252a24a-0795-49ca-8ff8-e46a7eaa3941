import { logger } from '@/utils/logger.js';
import { SESClient } from '@aws-sdk/client-ses';
import axios from 'axios';
import fs from 'fs';
import path from 'path';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { convertMjmlToHtml, createEmailTemplate, sendTemplatedEmail } from './index.js';

vi.mock('fs');
vi.mock('axios');
vi.mock('@aws-sdk/client-ses');
vi.mock('@/utils/logger.js');

const basicTemplatePath = path.join(__dirname, 'templates', 'basic.mjml');
const matchReportTemplatePath = path.join(__dirname, 'templates', 'matchReport.mjml');

const basicTemplate = `<mjml>[[Title]][[Content]][[Unsubscribe]]</mjml>`;
const matchReportTemplate = `<mjml>[[Title]][[HomeTeamName]][[HomeScore]][[HomeScorers]][[AwayTeamName]][[AwayScore]][[AwayScorers]][[Report]][[Unsubscribe]]</mjml>`;

describe('Email Service', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    vi.mocked(fs.readFileSync).mockImplementation((filePath: string) => {
      if (filePath.includes('basic.mjml')) return basicTemplate;
      if (filePath.includes('matchReport.mjml')) return matchReportTemplate;
      throw new Error('File not found');
    });
  });

  describe('createEmailTemplate', () => {
    it('should replace placeholders in basic template', () => {
      const content = 'Hello world!';
      const unsubscribeUrl = 'https://unsubscribe.com';
      const title = 'Test Title';
      const result = createEmailTemplate('basic', content, unsubscribeUrl, title);
      expect(result).toContain(title);
      expect(result).toContain(content);
      expect(result).toContain(`<a href="${unsubscribeUrl}">Unsubscribe</a>`);
    });

    it('should replace placeholders in matchReport template', () => {
      const contentObj = {
        HomeTeamName: 'Team A',
        HomeScore: '2',
        HomeScorers: 'Player 1, Player 2',
        AwayTeamName: 'Team B',
        AwayScore: '1',
        AwayScorers: 'Player 3',
        Report: 'A great match!',
      };
      const content = JSON.stringify(contentObj);
      const unsubscribeUrl = 'https://unsubscribe.com';
      const title = 'Match Report';
      const result = createEmailTemplate('matchReport', content, unsubscribeUrl, title);
      Object.values(contentObj).forEach((val) => expect(result).toContain(val));
      expect(result).toContain(title);
      expect(result).toContain(`<a href="${unsubscribeUrl}">Unsubscribe</a>`);
    });
  });

  describe('convertMjmlToHtml', () => {
    it('should call MJML API and return HTML', async () => {
      vi.mocked(axios.post).mockResolvedValue({
        data: { html: '<html>result</html>' },
      });
      process.env.MJML_APP_ID = 'id';
      process.env.MJML_SECRET = 'secret';
      const html = await convertMjmlToHtml('<mjml></mjml>');
      expect(html).toBe('<html>result</html>');
      expect(axios.post).toHaveBeenCalled();
    });
    it('should throw if MJML credentials are missing', async () => {
      delete process.env.MJML_APP_ID;
      delete process.env.MJML_SECRET;

      await expect(convertMjmlToHtml('<mjml></mjml>')).rejects.toThrow(
        'Failed to convert MJML template to HTML'
      );
      expect(logger.error).toHaveBeenCalledWith('Error converting MJML to HTML', {
        error: new Error('MJML credentials not configured'),
      });
    });
  });

  describe('sendTemplatedEmail', () => {
    it('should create, convert, and send email for basic template', async () => {
      vi.mocked(axios.post).mockResolvedValue({
        data: { html: '<html>result</html>' },
      });
      vi.mocked(SESClient).mockReturnValue({
        send: vi.fn().mockResolvedValue({}),
      });
      process.env.MJML_APP_ID = 'id';
      process.env.MJML_SECRET = 'secret';
      await expect(
        sendTemplatedEmail(
          'basic',
          ['<EMAIL>'],
          'Subject',
          'Content',
          'Title',
          'https://unsubscribe.com',
          '<EMAIL>'
        )
      ).resolves.toBeUndefined();
    });
    it('should create, convert, and send email for matchReport template', async () => {
      vi.mocked(axios.post).mockResolvedValue({
        data: { html: '<html>result</html>' },
      });
      vi.mocked(SESClient).mockReturnValue({
        send: vi.fn().mockResolvedValue({}),
      });
      process.env.MJML_APP_ID = 'id';
      process.env.MJML_SECRET = 'secret';
      const contentObj = {
        HomeTeamName: 'Team A',
        HomeScore: '2',
        HomeScorers: 'Player 1, Player 2',
        AwayTeamName: 'Team B',
        AwayScore: '1',
        AwayScorers: 'Player 3',
        Report: 'A great match!',
      };
      await expect(
        sendTemplatedEmail(
          'matchReport',
          ['<EMAIL>'],
          'Subject',
          JSON.stringify(contentObj),
          'Title',
          'https://unsubscribe.com',
          '<EMAIL>'
        )
      ).resolves.toBeUndefined();
    });
  });
});
