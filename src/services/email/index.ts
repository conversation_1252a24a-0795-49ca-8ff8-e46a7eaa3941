import { logger } from '@/utils/logger.js';
import { SendEmailCommand, SendEmailCommandInput, SESClient } from '@aws-sdk/client-ses';
import axios from 'axios';
import fs from 'fs';
import path from 'path';

const sesClient = new SESClient({
  region: process.env.REGION,
});

interface mjmlEmailResponse {
  errors: string[];
  html: string;
  mjml: string;
  mjml_version: string;
}

/**
 * Converts an MJML template to HTML
 * @param mjmlTemplate The MJML template string
 * @returns The HTML string
 */
export const convertMjmlToHtml = async (mjmlTemplate: string): Promise<string> => {
  try {
    const mjmlAppId = process.env.MJML_APP_ID;
    const mjmlSecret = process.env.MJML_SECRET;

    if (!mjmlAppId || !mjmlSecret) {
      throw new Error('MJML credentials not configured');
    }

    const response = await axios.post<mjmlEmailResponse>(
      'https://api.mjml.io/v1/render',
      { mjml: mjmlTemplate },
      {
        auth: {
          username: mjmlAppId,
          password: mjmlSecret,
        },
      }
    );

    if (response.data && response.data.html) {
      return response.data.html;
    } else {
      throw new Error('Invalid response from MJML API');
    }
  } catch (error) {
    logger.error('Error converting MJML to HTML', { error });
    throw new Error('Failed to convert MJML template to HTML');
  }
};

/**
 * Creates a basic email template with a logo and unsubscribe link
 * @param templateName The template.mjml file to use
 * @param content The main content of the email
 * @param unsubscribeUrl The URL for the unsubscribe link
 * @param title The title of the email
 * @returns The MJML template string
 */
export const createEmailTemplate = (
  templateName: string,
  content: string,
  unsubscribeUrl?: string,
  title: string = 'Jumpers for Goalposts'
): string => {
  try {
    // Try to load the template from file
    let template: string;
    try {
      // First try with __dirname which works in development
      const templatePath = path.join(__dirname, 'templates', `${templateName}.mjml`);
      template = fs.readFileSync(templatePath, 'utf8');
    } catch (fileError) {
      // If that fails, try with process.cwd() which might work in Lambda
      try {
        const templatePath = path.join(process.cwd(), 'templates', `${templateName}.mjml`);
        template = fs.readFileSync(templatePath, 'utf8');
      } catch (cwdError) {
        logger.error('Error loading email template', { fileError, cwdError });
        throw new Error('Failed to load email template');
      }
    }

    // Replace placeholders with actual values
    template = template.replace('[[Title]]', title);
    if (templateName === 'basic') {
      template = template.replace('[[Content]]', content);
    } else {
      const jsonContent = JSON.parse(content);
      // Loop through each key in the json and replace the placeholder in the template
      Object.keys(jsonContent).forEach((key) => {
        template = template.replace(`[[${key}]]`, jsonContent[key]);
      });
    }
    template = template.replace('[[Unsubscribe]]', `<a href="${unsubscribeUrl}">Unsubscribe</a>`);

    return template;
  } catch (error) {
    logger.error('Error processing email template', { error });
    throw new Error('Failed to process email template');
  }
};

/**
 * Sends an email to multiple recipients using AWS SES
 * @param recipients Array of email addresses to send to
 * @param subject Email subject
 * @param content Email content (HTML)
 * @param source Sender email address
 * @returns Promise that resolves when the email is sent
 */
export const sendEmail = async (
  recipients: string[],
  subject: string,
  content: string,
  source: string = '<EMAIL>'
): Promise<void> => {
  try {
    if (!recipients || recipients.length === 0) {
      throw new Error('No recipients provided');
    }

    const params: SendEmailCommandInput = {
      Source: `Jumpers for Goalposts <${source}>`,
      Destination: {
        ToAddresses: recipients,
      },
      Message: {
        Subject: {
          Data: subject,
          Charset: 'UTF-8',
        },
        Body: {
          Html: {
            Data: content,
            Charset: 'UTF-8',
          },
        },
      },
    };

    logger.debug('Sending email', { params });
    const command = new SendEmailCommand(params);
    await sesClient.send(command);

    logger.info('Email sent successfully', { recipients, subject });
  } catch (error) {
    logger.error('Error sending email', { error, recipients, subject });
    throw new Error('Failed to send email');
  }
};

/**
 * Main function to send an email with MJML template to multiple recipients
 * @param template The name of the template to use
 * @param recipients Array of email addresses to send to
 * @param subject Email subject
 * @param content Main content of the email
 * @param title Header to display in the email
 * @param unsubscribeUrl URL for the unsubscribe link
 * @param source Sender email address
 * @returns Promise that resolves when the email is sent
 */
export const sendTemplatedEmail = async (
  template: string,
  recipients: string[],
  subject: string,
  content: string,
  title?: string,
  unsubscribeUrl?: string,
  source?: string
): Promise<void> => {
  try {
    // Create MJML template
    const mjmlTemplate = createEmailTemplate(template, content, unsubscribeUrl, title);

    // Convert MJML to HTML
    const htmlContent = await convertMjmlToHtml(mjmlTemplate);

    // Send email
    await sendEmail(recipients, subject, htmlContent, source);
  } catch (error) {
    logger.error('Error sending templated email', { error, recipients, subject });
    throw new Error('Failed to send templated email');
  }
};
