import { ArchivedManager } from '@/entities/ArchivedManager.js';
import { ArchivedPlayer } from '@/entities/ArchivedPlayer.js';
import { ArchivedPlayerAttributes } from '@/entities/ArchivedPlayerAttributes.js';
import { ArchivedPlayerOverallStats } from '@/entities/ArchivedPlayerOverallStats.js';
import { AvailableTeam } from '@/entities/AvailableTeam.js';
import { Manager } from '@/entities/Manager.js';
import { Player } from '@/entities/Player.js';
import { PlayerAttributes } from '@/entities/PlayerAttributes.js';
import { PlayerOverallStats } from '@/entities/PlayerOverallStats.js';
import { Team } from '@/entities/Team.js';
import { logger } from '@/utils/logger.js';
import { EntityManager } from '@mikro-orm/core';

export interface ManagerLoginRestorationService {
  checkAndRestoreArchivedManager(managerId: string): Promise<Manager | null>;
  findAvailableTeamForRestoration(
    gameworldId: string,
    preferredTier: number
  ): Promise<AvailableTeam | null>;
}

export class ManagerLoginRestorationServiceImpl implements ManagerLoginRestorationService {
  constructor(private readonly em: EntityManager) {}

  /**
   * Check if a manager is archived and restore them if possible
   * @param managerId The manager ID to check and restore
   * @returns The restored manager or null if not archived or restoration failed
   */
  async checkAndRestoreArchivedManager(managerId: string): Promise<Manager | null> {
    logger.info('Checking for archived manager', { managerId });

    try {
      // Check if manager exists and is archived
      const existingManager = await this.em.findOne(Manager, {
        managerId,
        isArchived: true,
      });

      if (!existingManager) {
        logger.info('Manager not found or not archived', { managerId });
        return null;
      }

      // Find the archived data
      const archivedManager = await this.em.findOne(
        ArchivedManager,
        {
          managerId,
        },
        {
          populate: [
            'archivedTeam',
            'archivedTeam.archivedPlayers',
            'archivedTeam.archivedPlayers.attributes',
            'archivedTeam.archivedPlayers.overallStats',
          ],
        }
      );

      if (!archivedManager || !archivedManager.archivedTeam) {
        logger.error('Archived manager data not found', { managerId });
        throw new Error(`Archived data not found for manager ${managerId}`);
      }

      logger.info('Found archived manager, starting restoration', {
        managerId,
        archivedTeamTier: archivedManager.archivedTeam.tier,
        gameworldId: archivedManager.gameworldId,
      });

      // Find an available team for restoration
      const availableTeam = await this.findAvailableTeamForRestoration(
        archivedManager.gameworldId!,
        archivedManager.archivedTeam.tier
      );

      if (!availableTeam) {
        logger.error('No available teams found for restoration', {
          managerId,
          gameworldId: archivedManager.gameworldId,
          preferredTier: archivedManager.archivedTeam.tier,
        });
        throw new Error(
          `No available teams found for restoration in gameworld ${archivedManager.gameworldId}`
        );
      }

      // Perform the restoration in a transaction
      return await this.em.transactional(async (transactionalEm) => {
        const txService = new ManagerLoginRestorationServiceImpl(transactionalEm);
        return await txService.performRestoration(existingManager, archivedManager, availableTeam);
      });
    } catch (error) {
      logger.error('Error checking and restoring archived manager', {
        managerId,
        error: error instanceof Error ? error.message : String(error),
      });
      throw error;
    }
  }

  /**
   * Find an available team for restoration, starting with preferred tier and going down
   * @param gameworldId The gameworld to search in
   * @param preferredTier The preferred tier to start searching from
   * @returns Available team or null if none found
   */
  async findAvailableTeamForRestoration(
    gameworldId: string,
    preferredTier: number
  ): Promise<AvailableTeam | null> {
    logger.info('Finding available team for restoration', {
      gameworldId,
      preferredTier,
    });

    try {
      // Start from preferred tier and go down (higher tier numbers)
      for (let tier = preferredTier; tier <= 4; tier++) {
        // Find teams in this tier
        const availableTeams = await this.em.find(AvailableTeam, {
          gameworldId,
        });

        // Check each available team to see if it's in the desired tier
        for (const availableTeam of availableTeams) {
          const team = await this.em.findOne(Team, {
            teamId: availableTeam.teamId,
            tier,
          });

          if (team) {
            logger.info('Found available team for restoration', {
              gameworldId,
              teamId: availableTeam.teamId,
              tier,
              preferredTier,
            });
            return availableTeam;
          }
        }

        logger.info('No available teams found in tier, trying next tier', {
          gameworldId,
          tier,
          nextTier: tier + 1,
        });
      }

      logger.warn('No available teams found in any tier', {
        gameworldId,
        preferredTier,
        maxTierChecked: 4,
      });

      return null;
    } catch (error) {
      logger.error('Error finding available team for restoration', {
        gameworldId,
        preferredTier,
        error: error instanceof Error ? error.message : String(error),
      });
      throw error;
    }
  }

  private async performRestoration(
    existingManager: Manager,
    archivedManager: ArchivedManager,
    availableTeam: AvailableTeam
  ): Promise<Manager> {
    logger.info('Performing manager restoration', {
      managerId: existingManager.managerId,
      availableTeamId: availableTeam.teamId,
    });

    try {
      // Get the team that will be replaced
      const targetTeam = await this.em.findOneOrFail(
        Team,
        {
          teamId: availableTeam.teamId,
        },
        {
          populate: ['players', 'players.attributes', 'players.overallStats'],
        }
      );

      // Remove the available team entry
      this.em.remove(availableTeam);

      // Restore manager properties
      existingManager.isArchived = false;
      existingManager.lastActive = BigInt(Date.now());
      existingManager.team = targetTeam;
      existingManager.gameworldId = archivedManager.gameworldId;
      existingManager.scoutTokens = archivedManager.scoutTokens;
      existingManager.superScoutTokens = archivedManager.superScoutTokens;
      existingManager.magicSponges = archivedManager.magicSponges;
      existingManager.cardAppeals = archivedManager.cardAppeals;
      existingManager.trainingBoosts = archivedManager.trainingBoosts;
      existingManager.notificationPreferences = archivedManager.notificationPreferences;
      existingManager.pushToken = archivedManager.pushToken;
      existingManager.loginStreak = 0; // Reset login streak
      existingManager.role = archivedManager.role;
      existingManager.changedTeamName = archivedManager.changedTeamName;
      existingManager.wins = archivedManager.wins;
      existingManager.defeats = archivedManager.defeats;
      existingManager.draws = archivedManager.draws;
      existingManager.goalsScored = archivedManager.goalsScored;
      existingManager.goalsConceded = archivedManager.goalsConceded;
      existingManager.highestTransferPaid = archivedManager.highestTransferPaid;
      existingManager.highestTransferReceived = archivedManager.highestTransferReceived;
      existingManager.trophies = archivedManager.trophies;

      // Restore team data
      targetTeam.manager = existingManager;
      targetTeam.teamName = archivedManager.archivedTeam!.teamName;
      targetTeam.balance = archivedManager.archivedTeam!.balance;
      targetTeam.selectionOrder = [...archivedManager.archivedTeam!.selectionOrder];
      targetTeam.trainingLevel = archivedManager.archivedTeam!.trainingLevel;

      // Remove existing players from the target team
      const existingPlayers = targetTeam.players.getItems();
      for (const player of existingPlayers) {
        // Remove player attributes and stats
        if (player.attributes) {
          this.em.remove(player.attributes);
        }
        if (player.overallStats) {
          this.em.remove(player.overallStats);
        }
        this.em.remove(player);
      }

      // Restore archived players
      if (archivedManager.archivedTeam!.archivedPlayers) {
        for (const archivedPlayer of archivedManager.archivedTeam!.archivedPlayers.getItems()) {
          await this.restoreArchivedPlayer(archivedPlayer, targetTeam);
        }
      }

      // Clean up archived data
      await this.cleanupArchivedData(archivedManager);

      logger.info('Manager restoration completed successfully', {
        managerId: existingManager.managerId,
        teamId: targetTeam.teamId,
        teamName: targetTeam.teamName,
      });

      return existingManager;
    } catch (error) {
      logger.error('Error performing restoration', {
        managerId: existingManager.managerId,
        error: error instanceof Error ? error.message : String(error),
      });
      throw error;
    }
  }

  private async restoreArchivedPlayer(
    archivedPlayer: ArchivedPlayer,
    targetTeam: Team
  ): Promise<void> {
    const player = new Player();

    // Use original player ID to maintain references
    player.playerId = archivedPlayer.originalPlayerId;
    player.gameworldId = archivedPlayer.gameworldId;
    player.team = targetTeam;
    player.age = archivedPlayer.age;
    player.seed = archivedPlayer.seed;
    player.firstName = archivedPlayer.firstName;
    player.surname = archivedPlayer.surname;
    player.value = archivedPlayer.value;
    player.energy = 100; // Reset to full energy
    player.lastMatchPlayed = archivedPlayer.lastMatchPlayed;
    player.injuredUntil = undefined; // Clear any injuries
    player.suspendedForGames = 0; // Clear suspensions
    player.isTransferListed = false;
    player.retiringAtEndOfSeason = false;

    this.em.persist(player);

    // Restore attributes
    if (archivedPlayer.attributes) {
      await this.restorePlayerAttributes(player, archivedPlayer.attributes);
    }

    // Restore overall stats
    if (archivedPlayer.overallStats) {
      await this.restorePlayerOverallStats(player, archivedPlayer.overallStats);
    }
  }

  private async restorePlayerAttributes(
    player: Player,
    archivedAttributes: ArchivedPlayerAttributes
  ): Promise<void> {
    const attributes = new PlayerAttributes();
    attributes.player = player;
    attributes.isGoalkeeper = archivedAttributes.isGoalkeeper;
    attributes.reflexesCurrent = archivedAttributes.reflexesCurrent;
    attributes.reflexesPotential = archivedAttributes.reflexesPotential;
    attributes.positioningCurrent = archivedAttributes.positioningCurrent;
    attributes.positioningPotential = archivedAttributes.positioningPotential;
    attributes.shotStoppingCurrent = archivedAttributes.shotStoppingCurrent;
    attributes.shotStoppingPotential = archivedAttributes.shotStoppingPotential;
    attributes.tacklingCurrent = archivedAttributes.tacklingCurrent;
    attributes.tacklingPotential = archivedAttributes.tacklingPotential;
    attributes.markingCurrent = archivedAttributes.markingCurrent;
    attributes.markingPotential = archivedAttributes.markingPotential;
    attributes.headingCurrent = archivedAttributes.headingCurrent;
    attributes.headingPotential = archivedAttributes.headingPotential;
    attributes.finishingCurrent = archivedAttributes.finishingCurrent;
    attributes.finishingPotential = archivedAttributes.finishingPotential;
    attributes.paceCurrent = archivedAttributes.paceCurrent;
    attributes.pacePotential = archivedAttributes.pacePotential;
    attributes.crossingCurrent = archivedAttributes.crossingCurrent;
    attributes.crossingPotential = archivedAttributes.crossingPotential;
    attributes.passingCurrent = archivedAttributes.passingCurrent;
    attributes.passingPotential = archivedAttributes.passingPotential;
    attributes.visionCurrent = archivedAttributes.visionCurrent;
    attributes.visionPotential = archivedAttributes.visionPotential;
    attributes.ballControlCurrent = archivedAttributes.ballControlCurrent;
    attributes.ballControlPotential = archivedAttributes.ballControlPotential;
    attributes.stamina = archivedAttributes.stamina;

    player.attributes = attributes;
    this.em.persist(attributes);
  }

  private async restorePlayerOverallStats(
    player: Player,
    archivedStats: ArchivedPlayerOverallStats
  ): Promise<void> {
    const stats = new PlayerOverallStats();
    stats.player = player;
    stats.yellowCards = archivedStats.yellowCards;
    stats.redCards = archivedStats.redCards;
    stats.passesCompleted = archivedStats.passesCompleted;
    stats.passesAttempted = archivedStats.passesAttempted;
    stats.successfulBallCarries = archivedStats.successfulBallCarries;
    stats.ballCarriesAttempted = archivedStats.ballCarriesAttempted;
    stats.shots = archivedStats.shots;
    stats.shotsOnTarget = archivedStats.shotsOnTarget;
    stats.goals = archivedStats.goals;
    stats.saves = archivedStats.saves;
    stats.tackles = archivedStats.tackles;
    stats.fouls = archivedStats.fouls;

    player.overallStats = stats;
    this.em.persist(stats);
  }

  private async cleanupArchivedData(archivedManager: ArchivedManager): Promise<void> {
    // Remove all archived data for this manager
    if (archivedManager.archivedTeam) {
      // Remove archived players and their related data
      const archivedPlayers = await this.em.find(ArchivedPlayer, {
        archivedTeam: archivedManager.archivedTeam,
      });

      for (const archivedPlayer of archivedPlayers) {
        // Remove archived attributes and stats
        if (archivedPlayer.attributes) {
          this.em.remove(archivedPlayer.attributes);
        }
        if (archivedPlayer.overallStats) {
          this.em.remove(archivedPlayer.overallStats);
        }
        this.em.remove(archivedPlayer);
      }

      // Remove archived team
      this.em.remove(archivedManager.archivedTeam);
    }

    // Remove archived manager
    this.em.remove(archivedManager);

    logger.info('Archived data cleanup completed', {
      managerId: archivedManager.managerId,
    });
  }
}
