import { ArchivedManager } from '@/entities/ArchivedManager.js';
import { ArchivedPlayer } from '@/entities/ArchivedPlayer.js';
import { ArchivedPlayerAttributes } from '@/entities/ArchivedPlayerAttributes.js';
import { ArchivedPlayerOverallStats } from '@/entities/ArchivedPlayerOverallStats.js';
import { ArchivedTeam } from '@/entities/ArchivedTeam.js';
import { Manager } from '@/entities/Manager.js';
import { Player } from '@/entities/Player.js';
import { PlayerAttributes } from '@/entities/PlayerAttributes.js';
import { PlayerOverallStats } from '@/entities/PlayerOverallStats.js';
import { Team } from '@/entities/Team.js';
import { logger } from '@/utils/logger.js';
import { EntityManager } from '@mikro-orm/core';

export interface ManagerRestorationService {
  restoreManager(archivedManagerId: string): Promise<Manager>;
  canRestoreManager(archivedManagerId: string): Promise<boolean>;
}

export class ManagerRestorationServiceImpl implements ManagerRestorationService {
  constructor(private readonly em: EntityManager) {}

  /**
   * Check if a manager can be restored (team still exists and has no manager)
   * @param archivedManagerId The archived manager ID to check
   * @returns True if the manager can be restored, false otherwise
   */
  async canRestoreManager(archivedManagerId: string): Promise<boolean> {
    try {
      const archivedManager = await this.em.findOne(
        ArchivedManager,
        {
          managerId: archivedManagerId,
        },
        {
          populate: ['archivedTeam'],
        }
      );

      if (!archivedManager || !archivedManager.originalTeamId) {
        logger.warn('Archived manager not found or has no original team', {
          archivedManagerId,
        });
        return false;
      }

      // Check if the original team still exists and has no manager
      const originalTeam = await this.em.findOne(
        Team,
        {
          teamId: archivedManager.originalTeamId,
        },
        {
          populate: ['manager'],
        }
      );

      if (!originalTeam) {
        logger.warn('Original team no longer exists', {
          archivedManagerId,
          originalTeamId: archivedManager.originalTeamId,
        });
        return false;
      }

      if (originalTeam.manager) {
        logger.warn('Original team already has a manager', {
          archivedManagerId,
          originalTeamId: archivedManager.originalTeamId,
          currentManagerId: originalTeam.manager.managerId,
        });
        return false;
      }

      return true;
    } catch (error) {
      logger.error('Error checking if manager can be restored', {
        archivedManagerId,
        error: error instanceof Error ? error.message : String(error),
      });
      return false;
    }
  }

  /**
   * Restore an archived manager back to active status
   * @param archivedManagerId The archived manager ID to restore
   * @returns The restored manager entity
   */
  async restoreManager(archivedManagerId: string): Promise<Manager> {
    logger.info('Starting manager restoration', {
      archivedManagerId,
    });

    try {
      // Check if restoration is possible
      const canRestore = await this.canRestoreManager(archivedManagerId);
      if (!canRestore) {
        throw new Error(
          `Cannot restore manager ${archivedManagerId}: restoration conditions not met`
        );
      }

      // Load archived data
      const archivedManager = await this.em.findOneOrFail(
        ArchivedManager,
        {
          managerId: archivedManagerId,
        },
        {
          populate: [
            'archivedTeam',
            'archivedTeam.archivedPlayers',
            'archivedTeam.archivedPlayers.attributes',
            'archivedTeam.archivedPlayers.overallStats',
          ],
        }
      );

      const originalTeam = await this.em.findOneOrFail(Team, {
        teamId: archivedManager.originalTeamId!,
      });

      // Start transaction for restoration
      return await this.em.transactional(async (transactionalEm) => {
        const txRestorationService = new ManagerRestorationServiceImpl(transactionalEm);

        // 1. Restore the manager
        const restoredManager = await txRestorationService.restoreManagerEntity(
          archivedManager,
          originalTeam
        );

        // 2. Restore the team data
        await txRestorationService.restoreTeamData(archivedManager.archivedTeam!, originalTeam);

        // 3. Restore players
        if (archivedManager.archivedTeam?.archivedPlayers) {
          await txRestorationService.restorePlayers(
            archivedManager.archivedTeam.archivedPlayers.getItems(),
            originalTeam
          );
        }

        // 4. Clean up archived data
        await txRestorationService.cleanupArchivedData(archivedManager);

        logger.info('Manager restoration completed successfully', {
          archivedManagerId,
          restoredManagerId: restoredManager.managerId,
          teamId: originalTeam.teamId,
        });

        return restoredManager;
      });
    } catch (error) {
      logger.error('Error restoring manager', {
        archivedManagerId,
        error: error instanceof Error ? error.message : String(error),
      });
      throw error;
    }
  }

  private async restoreManagerEntity(
    archivedManager: ArchivedManager,
    originalTeam: Team
  ): Promise<Manager> {
    const manager = await this.em.findOneOrFail(Manager, {
      managerId: archivedManager.managerId,
    });

    // Restore all manager properties
    manager.loginStreak = 0; // Reset login streak
    manager.isArchived = false;

    // Associate with team
    manager.team = originalTeam;
    originalTeam.manager = manager;

    this.em.persist(manager);
    return manager;
  }

  private async restoreTeamData(archivedTeam: ArchivedTeam, originalTeam: Team): Promise<void> {
    // Restore team data that should be preserved
    originalTeam.teamName = archivedTeam.teamName;
    originalTeam.balance = archivedTeam.balance;
    originalTeam.selectionOrder = [...archivedTeam.selectionOrder];
    originalTeam.trainingLevel = archivedTeam.trainingLevel;

    // Note: We don't restore stats like points, wins, etc. as the season may have progressed
    logger.info('Team data restored', {
      teamId: originalTeam.teamId,
      restoredName: originalTeam.teamName,
      restoredBalance: originalTeam.balance,
    });
  }

  private async restorePlayers(
    archivedPlayers: ArchivedPlayer[],
    originalTeam: Team
  ): Promise<void> {
    for (const archivedPlayer of archivedPlayers) {
      // Check if the original player still exists
      const existingPlayer = await this.em.findOne(Player, {
        playerId: archivedPlayer.originalPlayerId,
      });

      if (existingPlayer && existingPlayer.team?.teamId === originalTeam.teamId) {
        // Player still exists and has not been transferred to another team, restore their data
        await this.restoreExistingPlayer(existingPlayer, archivedPlayer, originalTeam);
      } else {
        // Player no longer exists, create a new one
        await this.createRestoredPlayer(archivedPlayer, originalTeam);
      }
    }
  }

  private async restoreExistingPlayer(
    existingPlayer: Player,
    archivedPlayer: ArchivedPlayer,
    originalTeam: Team
  ): Promise<void> {
    // Restore player to original team
    existingPlayer.team = originalTeam;

    // Restore preserved attributes if they exist
    if (archivedPlayer.attributes) {
      await this.restorePlayerAttributes(existingPlayer, archivedPlayer.attributes);
    }

    if (archivedPlayer.overallStats) {
      await this.restorePlayerOverallStats(existingPlayer, archivedPlayer.overallStats);
    }

    logger.info('Existing player restored to team', {
      playerId: existingPlayer.playerId,
      teamId: originalTeam.teamId,
    });
  }

  private async createRestoredPlayer(
    archivedPlayer: ArchivedPlayer,
    originalTeam: Team
  ): Promise<void> {
    const player = new Player();

    // Use original player ID if possible, otherwise generate new one
    player.playerId = archivedPlayer.originalPlayerId;
    player.gameworldId = archivedPlayer.gameworldId;
    player.team = originalTeam;
    player.age = archivedPlayer.age;
    player.seed = archivedPlayer.seed;
    player.firstName = archivedPlayer.firstName;
    player.surname = archivedPlayer.surname;
    player.value = archivedPlayer.value;
    player.energy = 100; // Reset to full energy
    player.lastMatchPlayed = archivedPlayer.lastMatchPlayed;
    player.injuredUntil = undefined; // Clear any injuries
    player.suspendedForGames = 0; // Clear suspensions
    player.isTransferListed = false;
    player.retiringAtEndOfSeason = false;

    this.em.persist(player);

    // Restore attributes and stats
    if (archivedPlayer.attributes) {
      await this.createRestoredPlayerAttributes(player, archivedPlayer.attributes);
    }

    if (archivedPlayer.overallStats) {
      await this.createRestoredPlayerOverallStats(player, archivedPlayer.overallStats);
    }

    logger.info('New player created from archived data', {
      playerId: player.playerId,
      teamId: originalTeam.teamId,
    });
  }

  private async restorePlayerAttributes(
    player: Player,
    archivedAttributes: ArchivedPlayerAttributes
  ): Promise<void> {
    const attributes = await this.em.findOne(PlayerAttributes, { player: player.playerId });
    if (attributes) {
      // Update existing attributes
      this.copyArchivedAttributesToCurrent(archivedAttributes, attributes);
    } else {
      // Create new attributes
      await this.createRestoredPlayerAttributes(player, archivedAttributes);
    }
  }

  private async createRestoredPlayerAttributes(
    player: Player,
    archivedAttributes: ArchivedPlayerAttributes
  ): Promise<void> {
    const attributes = new PlayerAttributes();
    attributes.player = player;
    this.copyArchivedAttributesToCurrent(archivedAttributes, attributes);
    player.attributes = attributes;
    this.em.persist(attributes);
  }

  private copyArchivedAttributesToCurrent(
    archived: ArchivedPlayerAttributes,
    current: PlayerAttributes
  ): void {
    current.isGoalkeeper = archived.isGoalkeeper;
    current.reflexesCurrent = archived.reflexesCurrent;
    current.reflexesPotential = archived.reflexesPotential;
    current.positioningCurrent = archived.positioningCurrent;
    current.positioningPotential = archived.positioningPotential;
    current.shotStoppingCurrent = archived.shotStoppingCurrent;
    current.shotStoppingPotential = archived.shotStoppingPotential;
    current.tacklingCurrent = archived.tacklingCurrent;
    current.tacklingPotential = archived.tacklingPotential;
    current.markingCurrent = archived.markingCurrent;
    current.markingPotential = archived.markingPotential;
    current.headingCurrent = archived.headingCurrent;
    current.headingPotential = archived.headingPotential;
    current.finishingCurrent = archived.finishingCurrent;
    current.finishingPotential = archived.finishingPotential;
    current.paceCurrent = archived.paceCurrent;
    current.pacePotential = archived.pacePotential;
    current.crossingCurrent = archived.crossingCurrent;
    current.crossingPotential = archived.crossingPotential;
    current.passingCurrent = archived.passingCurrent;
    current.passingPotential = archived.passingPotential;
    current.visionCurrent = archived.visionCurrent;
    current.visionPotential = archived.visionPotential;
    current.ballControlCurrent = archived.ballControlCurrent;
    current.ballControlPotential = archived.ballControlPotential;
    current.stamina = archived.stamina;
  }

  private async restorePlayerOverallStats(
    player: Player,
    archivedStats: ArchivedPlayerOverallStats
  ): Promise<void> {
    const stats = await this.em.findOne(PlayerOverallStats, { player: player.playerId });
    if (stats) {
      // Update existing stats
      this.copyArchivedStatsToCurrentStats(archivedStats, stats);
    } else {
      // Create new stats
      await this.createRestoredPlayerOverallStats(player, archivedStats);
    }
  }

  private async createRestoredPlayerOverallStats(
    player: Player,
    archivedStats: ArchivedPlayerOverallStats
  ): Promise<void> {
    const stats = new PlayerOverallStats();
    stats.player = player;
    this.copyArchivedStatsToCurrentStats(archivedStats, stats);
    player.overallStats = stats;
    this.em.persist(stats);
  }

  private copyArchivedStatsToCurrentStats(
    archived: ArchivedPlayerOverallStats,
    current: PlayerOverallStats
  ): void {
    current.yellowCards = archived.yellowCards;
    current.redCards = archived.redCards;
    current.passesCompleted = archived.passesCompleted;
    current.passesAttempted = archived.passesAttempted;
    current.successfulBallCarries = archived.successfulBallCarries;
    current.ballCarriesAttempted = archived.ballCarriesAttempted;
    current.shots = archived.shots;
    current.shotsOnTarget = archived.shotsOnTarget;
    current.goals = archived.goals;
    current.saves = archived.saves;
    current.tackles = archived.tackles;
    current.fouls = archived.fouls;
  }

  private async cleanupArchivedData(archivedManager: ArchivedManager): Promise<void> {
    // Remove all archived data for this manager
    if (archivedManager.archivedTeam) {
      // Remove archived players and their related data
      const archivedPlayers = await this.em.find(ArchivedPlayer, {
        archivedTeam: archivedManager.archivedTeam.archivedTeamId,
      });

      for (const archivedPlayer of archivedPlayers) {
        // Remove archived attributes and stats
        await this.em.nativeDelete(ArchivedPlayerAttributes, {
          player: archivedPlayer.archivedPlayerId,
        });
        await this.em.nativeDelete(ArchivedPlayerOverallStats, {
          player: archivedPlayer.archivedPlayerId,
        });
      }

      // Remove archived players
      await this.em.nativeDelete(ArchivedPlayer, {
        archivedTeam: archivedManager.archivedTeam.archivedTeamId,
      });

      // Remove archived team
      this.em.remove(archivedManager.archivedTeam);
    }

    // Remove archived manager
    this.em.remove(archivedManager);

    logger.info('Archived data cleanup completed', {
      managerId: archivedManager.managerId,
    });
  }
}
