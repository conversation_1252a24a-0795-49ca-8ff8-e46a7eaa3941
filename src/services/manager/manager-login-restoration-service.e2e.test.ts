import { ArchivedManager } from '@/entities/ArchivedManager.js';
import { ArchivedPlayer } from '@/entities/ArchivedPlayer.js';
import { ArchivedPlayerAttributes } from '@/entities/ArchivedPlayerAttributes.js';
import { ArchivedPlayerOverallStats } from '@/entities/ArchivedPlayerOverallStats.js';
import { ArchivedTeam } from '@/entities/ArchivedTeam.js';
import { AvailableTeam } from '@/entities/AvailableTeam.js';
import { Gameworld } from '@/entities/Gameworld.js';
import { League } from '@/entities/League.js';
import { Manager } from '@/entities/Manager.js';
import { Player } from '@/entities/Player.js';
import { Team } from '@/entities/Team.js';
import { logger } from '@/utils/logger.js';
import { MikroORM } from '@mikro-orm/core';
import { PostgreSqlDriver } from '@mikro-orm/postgresql';
import { v4 as uuidv4 } from 'uuid';
import { beforeAll, beforeEach, describe, expect, it, vi } from 'vitest';
import mikroOrmConfig from '../../mikro-orm.config.js';
import { ManagerLoginRestorationServiceImpl } from './manager-login-restoration-service.js';

describe('ManagerLoginRestorationService', () => {
  let orm: MikroORM<PostgreSqlDriver>;
  let service: ManagerLoginRestorationServiceImpl;
  let gameworld: Gameworld;
  let league: League;
  let archivedManager: Manager;
  let archivedManagerData: ArchivedManager;
  let archivedTeamData: ArchivedTeam;
  let availableTeam: AvailableTeam;
  let targetTeam: Team;

  beforeAll(async () => {
    if (!process.env.DATABASE_URL?.includes('localhost')) {
      throw new Error('Tests aborted: DATABASE_URL must be localhost');
    }

    vi.restoreAllMocks();
    vi.resetModules();
    vi.unmock('../../storage-interface/database-initializer.js');

    await MikroORM.init<PostgreSqlDriver>(mikroOrmConfig)
      .then(async (_orm) => {
        orm = _orm;
        await _orm.em.getConnection().execute('CREATE EXTENSION IF NOT EXISTS "uuid-ossp";');
        await _orm.schema.refreshDatabase({ dropDb: false });
      })
      .catch((error) => {
        logger.error('Failed to initialize database', { error });
      });
  });

  beforeEach(async () => {
    const em = orm.em.fork();
    service = new ManagerLoginRestorationServiceImpl(em);
    await createTestData();
  });

  async function createTestData() {
    const em = orm.em.fork();

    // Create gameworld
    gameworld = new Gameworld();
    gameworld.id = uuidv4();
    gameworld.endDate = BigInt(Date.now() + 365 * 24 * 60 * 60 * 1000);
    gameworld.highestManageableTier = 3;

    // Create league
    league = new League();
    league.id = uuidv4();
    league.gameworld = gameworld;
    league.tier = 2;
    league.name = 'Test League';

    // Create archived manager (marked as archived)
    archivedManager = new Manager();
    archivedManager.managerId = uuidv4();
    archivedManager.createdAt = BigInt(Date.now() - 30 * 24 * 60 * 60 * 1000);
    archivedManager.lastActive = BigInt(Date.now() - 21 * 24 * 60 * 60 * 1000);
    archivedManager.firstName = 'Archived';
    archivedManager.lastName = 'Manager';
    archivedManager.email = '<EMAIL>';
    archivedManager.gameworldId = gameworld.id;
    archivedManager.isArchived = true;
    archivedManager.scoutTokens = 5;
    archivedManager.wins = 10;

    // Create target team (will be replaced during restoration)
    targetTeam = new Team();
    targetTeam.teamId = uuidv4();
    targetTeam.gameworldId = gameworld.id;
    targetTeam.league = league;
    targetTeam.tier = 2;
    targetTeam.teamName = 'Target Team';
    targetTeam.balance = 500000;

    // Create available team entry
    availableTeam = new AvailableTeam();
    availableTeam.id = uuidv4();
    availableTeam.gameworldId = gameworld.id;
    availableTeam.teamId = targetTeam.teamId;

    // Create archived manager data
    archivedManagerData = new ArchivedManager();
    archivedManagerData.managerId = archivedManager.managerId;
    archivedManagerData.createdAt = archivedManager.createdAt;
    archivedManagerData.lastActive = archivedManager.lastActive;
    archivedManagerData.archivedAt = BigInt(Date.now() - 1 * 24 * 60 * 60 * 1000);
    archivedManagerData.firstName = archivedManager.firstName;
    archivedManagerData.lastName = archivedManager.lastName;
    archivedManagerData.email = archivedManager.email;
    archivedManagerData.gameworldId = archivedManager.gameworldId;
    archivedManagerData.scoutTokens = archivedManager.scoutTokens;
    archivedManagerData.wins = archivedManager.wins;

    // Create archived team data
    archivedTeamData = new ArchivedTeam();
    archivedTeamData.archivedTeamId = uuidv4();
    archivedTeamData.gameworldId = gameworld.id;
    archivedTeamData.archivedAt = archivedManagerData.archivedAt;
    archivedTeamData.leagueId = league.id;
    archivedTeamData.tier = 2;
    archivedTeamData.teamName = 'Archived FC';
    archivedTeamData.balance = 1000000;
    archivedTeamData.points = 15;
    archivedTeamData.wins = 5;
    archivedTeamData.originalTeamId = uuidv4(); // Different from target team

    // Link archived manager and team
    archivedManagerData.archivedTeam = archivedTeamData;
    archivedTeamData.archivedManager = archivedManagerData;

    // Create archived player
    const archivedPlayer = new ArchivedPlayer();
    archivedPlayer.archivedPlayerId = uuidv4();
    archivedPlayer.gameworldId = gameworld.id;
    archivedPlayer.archivedAt = archivedManagerData.archivedAt;
    archivedPlayer.archivedTeam = archivedTeamData;
    archivedPlayer.age = 25;
    archivedPlayer.seed = BigInt(12345);
    archivedPlayer.firstName = 'Archived';
    archivedPlayer.surname = 'Player';
    archivedPlayer.value = 100000;
    archivedPlayer.energy = 80;
    archivedPlayer.lastMatchPlayed = BigInt(Date.now() - 7 * 24 * 60 * 60 * 1000);
    archivedPlayer.suspendedForGames = 0;
    archivedPlayer.originalPlayerId = uuidv4();

    // Create archived player attributes
    const archivedAttributes = new ArchivedPlayerAttributes();
    archivedAttributes.player = archivedPlayer;
    archivedAttributes.archivedAt = archivedManagerData.archivedAt;
    archivedAttributes.isGoalkeeper = false;
    archivedAttributes.reflexesCurrent = 15;
    archivedAttributes.reflexesPotential = 20;
    archivedAttributes.positioningCurrent = 16;
    archivedAttributes.positioningPotential = 21;
    archivedAttributes.shotStoppingCurrent = 14;
    archivedAttributes.shotStoppingPotential = 19;
    archivedAttributes.tacklingCurrent = 17;
    archivedAttributes.tacklingPotential = 22;
    archivedAttributes.markingCurrent = 16;
    archivedAttributes.markingPotential = 21;
    archivedAttributes.headingCurrent = 15;
    archivedAttributes.headingPotential = 20;
    archivedAttributes.finishingCurrent = 18;
    archivedAttributes.finishingPotential = 23;
    archivedAttributes.paceCurrent = 19;
    archivedAttributes.pacePotential = 24;
    archivedAttributes.crossingCurrent = 16;
    archivedAttributes.crossingPotential = 21;
    archivedAttributes.passingCurrent = 17;
    archivedAttributes.passingPotential = 22;
    archivedAttributes.visionCurrent = 16;
    archivedAttributes.visionPotential = 21;
    archivedAttributes.ballControlCurrent = 18;
    archivedAttributes.ballControlPotential = 23;
    archivedAttributes.stamina = 0.8;
    archivedPlayer.attributes = archivedAttributes;

    // Create archived player stats
    const archivedStats = new ArchivedPlayerOverallStats();
    archivedStats.player = archivedPlayer;
    archivedStats.archivedAt = archivedManagerData.archivedAt;
    archivedStats.yellowCards = 2;
    archivedStats.redCards = 0;
    archivedStats.goals = 5;
    archivedStats.shots = 20;
    archivedStats.passesCompleted = 100;
    archivedStats.passesAttempted = 120;
    archivedPlayer.overallStats = archivedStats;

    // Persist all entities
    em.persist([
      gameworld,
      league,
      archivedManager,
      targetTeam,
      availableTeam,
      archivedManagerData,
      archivedTeamData,
      archivedPlayer,
      archivedAttributes,
      archivedStats,
    ]);
    await em.flush();
  }

  it('should successfully restore an archived manager', async () => {
    // Act
    const restoredManager = await service.checkAndRestoreArchivedManager(archivedManager.managerId);

    // Assert
    expect(restoredManager).toBeDefined();
    expect(restoredManager!.managerId).toBe(archivedManager.managerId);
    expect(restoredManager!.isArchived).toBe(false);
    expect(restoredManager!.team).toBeDefined();
    expect(restoredManager!.team!.teamName).toBe('Archived FC'); // Should have restored team name
    expect(restoredManager!.team!.balance).toBe(1000000); // Should have restored balance
    expect(restoredManager!.scoutTokens).toBe(5); // Should have restored scout tokens

    // Check that available team was removed
    const em = orm.em.fork();
    const remainingAvailableTeams = await em.find(AvailableTeam, { teamId: targetTeam.teamId });
    expect(remainingAvailableTeams).toHaveLength(0);

    // Check that archived data was cleaned up
    const remainingArchivedManagers = await em.find(ArchivedManager, {
      managerId: archivedManager.managerId,
    });
    expect(remainingArchivedManagers).toHaveLength(0);

    // Check that players were restored
    const restoredPlayers = await em.find(
      Player,
      { team: restoredManager!.team!.teamId },
      {
        populate: ['attributes', 'overallStats'],
      }
    );
    expect(restoredPlayers).toHaveLength(1);

    const restoredPlayer = restoredPlayers[0]!;
    expect(restoredPlayer.firstName).toBe('Archived');
    expect(restoredPlayer.surname).toBe('Player');
    expect(restoredPlayer.energy).toBe(100); // Should be reset to full
    expect(restoredPlayer.attributes).toBeDefined();
    expect(restoredPlayer.overallStats).toBeDefined();
  });

  it('should return null for non-archived manager', async () => {
    // Arrange - create a non-archived manager
    const em = orm.em.fork();
    const nonArchivedManager = new Manager();
    nonArchivedManager.managerId = uuidv4();
    nonArchivedManager.isArchived = false;
    nonArchivedManager.lastActive = BigInt(Date.now());
    nonArchivedManager.createdAt = BigInt(Date.now());
    em.persist(nonArchivedManager);
    await em.flush();

    // Act
    const result = await service.checkAndRestoreArchivedManager(nonArchivedManager.managerId);

    // Assert
    expect(result).toBeNull();
  });

  it('should find available team in preferred tier', async () => {
    // Act
    const foundTeam = await service.findAvailableTeamForRestoration(gameworld.id, 2);

    // Assert
    expect(foundTeam).toBeDefined();
    expect(foundTeam!.teamId).toBe(targetTeam.teamId);
  });

  it('should find available team in lower tier when preferred tier unavailable', async () => {
    // Arrange - create a team in tier 3
    const em = orm.em.fork();
    const tier3Team = new Team();
    tier3Team.teamId = uuidv4();
    tier3Team.gameworldId = gameworld.id;
    tier3Team.league = league;
    tier3Team.tier = 3;
    tier3Team.teamName = 'Tier 3 Team';

    const tier3AvailableTeam = new AvailableTeam();
    tier3AvailableTeam.id = uuidv4();
    tier3AvailableTeam.gameworldId = gameworld.id;
    tier3AvailableTeam.teamId = tier3Team.teamId;

    em.persist([tier3Team, tier3AvailableTeam]);
    await em.flush();

    // Act - try to find team starting from tier 3 (should find tier 3 team)
    const foundTeam = await service.findAvailableTeamForRestoration(gameworld.id, 3);

    // Assert
    expect(foundTeam).toBeDefined();
    expect(foundTeam!.teamId).toBe(tier3Team.teamId);
  });

  it('should return null when no available teams exist', async () => {
    // Arrange - remove the available team
    const em = orm.em.fork();
    await em.nativeDelete(AvailableTeam, { teamId: targetTeam.teamId });

    // Act
    const foundTeam = await service.findAvailableTeamForRestoration(gameworld.id, 2);

    // Assert
    expect(foundTeam).toBeNull();
  });
});
