import { ArchivedManager } from '@/entities/ArchivedManager.js';
import { ArchivedPlayer } from '@/entities/ArchivedPlayer.js';
import { ArchivedPlayerAttributes } from '@/entities/ArchivedPlayerAttributes.js';
import { ArchivedPlayerOverallStats } from '@/entities/ArchivedPlayerOverallStats.js';
import { ArchivedTeam } from '@/entities/ArchivedTeam.js';

import { Manager } from '@/entities/Manager.js';
import { Player } from '@/entities/Player.js';
import { PlayerAttributes } from '@/entities/PlayerAttributes.js';
import { PlayerOverallStats } from '@/entities/PlayerOverallStats.js';
import { Team } from '@/entities/Team.js';
import { logger } from '@/utils/logger.js';
import { EntityManager } from '@mikro-orm/core';
import { v4 as uuidv4 } from 'uuid';

export interface ManagerArchivalService {
  archiveManager(manager: Manager): Promise<ArchivedManager>;
  archiveTeam(team: Team, archivedManager: ArchivedManager): Promise<ArchivedTeam>;
  archivePlayers(players: Player[], archivedTeam: ArchivedTeam): Promise<ArchivedPlayer[]>;
}

export class ManagerArchivalServiceImpl implements ManagerArchivalService {
  constructor(private readonly em: EntityManager) {}

  /**
   * Archive a manager and all associated data
   * @param manager The manager to archive
   * @returns The archived manager entity
   */
  async archiveManager(manager: Manager): Promise<ArchivedManager> {
    const now = Date.now();

    logger.info('Archiving manager', {
      managerId: manager.managerId,
      teamId: manager.team?.teamId,
    });

    try {
      const archivedManager = new ArchivedManager();

      // Copy all manager properties
      archivedManager.managerId = manager.managerId;
      archivedManager.createdAt = manager.createdAt;
      archivedManager.lastActive = manager.lastActive;
      archivedManager.archivedAt = BigInt(now);
      archivedManager.firstName = manager.firstName;
      archivedManager.lastName = manager.lastName;
      archivedManager.email = manager.email;
      archivedManager.gameworldId = manager.gameworldId;
      archivedManager.scoutTokens = manager.scoutTokens;
      archivedManager.superScoutTokens = manager.superScoutTokens;
      archivedManager.magicSponges = manager.magicSponges;
      archivedManager.cardAppeals = manager.cardAppeals;
      archivedManager.trainingBoosts = manager.trainingBoosts;
      archivedManager.notificationPreferences = manager.notificationPreferences;
      archivedManager.pushToken = manager.pushToken;
      archivedManager.loginStreak = manager.loginStreak;
      archivedManager.role = manager.role;
      archivedManager.changedTeamName = manager.changedTeamName;
      archivedManager.wins = manager.wins;
      archivedManager.defeats = manager.defeats;
      archivedManager.isGuest = manager.isGuest;
      archivedManager.migratedFromGuestId = manager.migratedFromGuestId;
      archivedManager.migrationCompleted = manager.migrationCompleted;
      archivedManager.draws = manager.draws;
      archivedManager.goalsScored = manager.goalsScored;
      archivedManager.goalsConceded = manager.goalsConceded;
      archivedManager.highestTransferPaid = manager.highestTransferPaid;
      archivedManager.highestTransferReceived = manager.highestTransferReceived;
      archivedManager.trophies = manager.trophies;
      archivedManager.originalTeamId = manager.team?.teamId;

      this.em.persist(archivedManager);

      await this.em.nativeUpdate(Manager, { managerId: manager.managerId }, { isArchived: true });

      logger.info('Manager archived successfully', {
        managerId: manager.managerId,
        archivedAt: now,
      });

      return archivedManager;
    } catch (error) {
      logger.error('Error archiving manager', {
        managerId: manager.managerId,
        error: error instanceof Error ? error.message : String(error),
      });
      throw error;
    }
  }

  /**
   * Archive a team and associate it with the archived manager
   * @param team The team to archive
   * @param archivedManager The archived manager to associate with
   * @returns The archived team entity
   */
  async archiveTeam(team: Team, archivedManager: ArchivedManager): Promise<ArchivedTeam> {
    const now = Date.now();

    logger.info('Archiving team', {
      teamId: team.teamId,
      teamName: team.teamName,
      managerId: archivedManager.managerId,
    });

    try {
      const archivedTeam = new ArchivedTeam();

      // Generate new UUID for archived team
      archivedTeam.archivedTeamId = uuidv4();
      archivedTeam.gameworldId = team.gameworldId;
      archivedTeam.archivedAt = BigInt(now);
      archivedTeam.leagueId = team.league.id;
      archivedTeam.tier = team.tier;
      archivedTeam.teamName = team.teamName;
      archivedTeam.balance = team.balance;
      archivedTeam.played = team.played;
      archivedTeam.points = team.points;
      archivedTeam.goalsFor = team.goalsFor;
      archivedTeam.goalsAgainst = team.goalsAgainst;
      archivedTeam.wins = team.wins;
      archivedTeam.draws = team.draws;
      archivedTeam.losses = team.losses;
      archivedTeam.selectionOrder = [...team.selectionOrder];
      archivedTeam.trainingLevel = team.trainingLevel;
      archivedTeam.originalTeamId = team.teamId;

      // Associate with archived manager
      archivedTeam.archivedManager = archivedManager;
      archivedManager.archivedTeam = archivedTeam;

      this.em.persist(archivedTeam);

      logger.info('Team archived successfully', {
        teamId: team.teamId,
        archivedTeamId: archivedTeam.archivedTeamId,
        archivedAt: now,
      });

      return archivedTeam;
    } catch (error) {
      logger.error('Error archiving team', {
        teamId: team.teamId,
        error: error instanceof Error ? error.message : String(error),
      });
      throw error;
    }
  }

  /**
   * Archive all players from a team
   * @param players The players to archive
   * @param archivedTeam The archived team to associate with
   * @returns Array of archived player entities
   */
  async archivePlayers(players: Player[], archivedTeam: ArchivedTeam): Promise<ArchivedPlayer[]> {
    const now = Date.now();
    const archivedPlayers: ArchivedPlayer[] = [];

    logger.info('Archiving players', {
      playerCount: players.length,
      teamId: archivedTeam.originalTeamId,
      archivedTeamId: archivedTeam.archivedTeamId,
    });

    try {
      for (const player of players) {
        // Archive the player
        const archivedPlayer = await this.archivePlayer(player, archivedTeam, now);

        // Create & link attributes
        if (player.attributes) {
          const archivedAttributes = this.archivePlayerAttributes(player.attributes, now);
          archivedAttributes.player = archivedPlayer; // owning side
          archivedPlayer.attributes = archivedAttributes; // inverse side
          this.em.persist(archivedAttributes);
        }

        // Create & link overall stats
        if (player.overallStats) {
          const archivedStats = this.archivePlayerOverallStats(player.overallStats, now);
          archivedStats.player = archivedPlayer;
          archivedPlayer.overallStats = archivedStats;
          this.em.persist(archivedStats);
        }

        this.em.persist(archivedPlayer);
        archivedPlayers.push(archivedPlayer);
      }

      logger.info('Players archived successfully', {
        playerCount: archivedPlayers.length,
        archivedTeamId: archivedTeam.archivedTeamId,
      });

      return archivedPlayers;
    } catch (error) {
      logger.error('Error archiving players', {
        playerCount: players.length,
        teamId: archivedTeam.originalTeamId,
        error: error instanceof Error ? error.message : String(error),
      });
      throw error;
    }
  }

  private async archivePlayer(
    player: Player,
    archivedTeam: ArchivedTeam,
    archivedAt: number
  ): Promise<ArchivedPlayer> {
    const archivedPlayer = new ArchivedPlayer();

    archivedPlayer.archivedPlayerId = uuidv4();
    archivedPlayer.gameworldId = player.gameworldId;
    archivedPlayer.archivedAt = BigInt(archivedAt);
    archivedPlayer.archivedTeam = archivedTeam;
    archivedPlayer.age = player.age;
    archivedPlayer.seed = player.seed;
    archivedPlayer.firstName = player.firstName;
    archivedPlayer.surname = player.surname;
    archivedPlayer.value = player.value;
    archivedPlayer.energy = player.energy;
    archivedPlayer.lastMatchPlayed = player.lastMatchPlayed;
    archivedPlayer.injuredUntil = player.injuredUntil;
    archivedPlayer.suspendedForGames = player.suspendedForGames;
    archivedPlayer.isTransferListed = player.isTransferListed;
    archivedPlayer.retiringAtEndOfSeason = player.retiringAtEndOfSeason;
    archivedPlayer.originalPlayerId = player.playerId;

    return archivedPlayer;
  }

  private archivePlayerAttributes(
    attributes: PlayerAttributes,
    archivedAt: number
  ): ArchivedPlayerAttributes {
    const archivedAttributes = new ArchivedPlayerAttributes();

    archivedAttributes.archivedAt = BigInt(archivedAt);
    archivedAttributes.isGoalkeeper = attributes.isGoalkeeper;
    archivedAttributes.reflexesCurrent = attributes.reflexesCurrent;
    archivedAttributes.reflexesPotential = attributes.reflexesPotential;
    archivedAttributes.positioningCurrent = attributes.positioningCurrent;
    archivedAttributes.positioningPotential = attributes.positioningPotential;
    archivedAttributes.shotStoppingCurrent = attributes.shotStoppingCurrent;
    archivedAttributes.shotStoppingPotential = attributes.shotStoppingPotential;
    archivedAttributes.tacklingCurrent = attributes.tacklingCurrent;
    archivedAttributes.tacklingPotential = attributes.tacklingPotential;
    archivedAttributes.markingCurrent = attributes.markingCurrent;
    archivedAttributes.markingPotential = attributes.markingPotential;
    archivedAttributes.headingCurrent = attributes.headingCurrent;
    archivedAttributes.headingPotential = attributes.headingPotential;
    archivedAttributes.finishingCurrent = attributes.finishingCurrent;
    archivedAttributes.finishingPotential = attributes.finishingPotential;
    archivedAttributes.paceCurrent = attributes.paceCurrent;
    archivedAttributes.pacePotential = attributes.pacePotential;
    archivedAttributes.crossingCurrent = attributes.crossingCurrent;
    archivedAttributes.crossingPotential = attributes.crossingPotential;
    archivedAttributes.passingCurrent = attributes.passingCurrent;
    archivedAttributes.passingPotential = attributes.passingPotential;
    archivedAttributes.visionCurrent = attributes.visionCurrent;
    archivedAttributes.visionPotential = attributes.visionPotential;
    archivedAttributes.ballControlCurrent = attributes.ballControlCurrent;
    archivedAttributes.ballControlPotential = attributes.ballControlPotential;
    archivedAttributes.stamina = attributes.stamina;
    return archivedAttributes;
  }

  private archivePlayerOverallStats(
    stats: PlayerOverallStats,
    archivedAt: number
  ): ArchivedPlayerOverallStats {
    const archivedStats = new ArchivedPlayerOverallStats();

    archivedStats.archivedAt = BigInt(archivedAt);
    archivedStats.yellowCards = stats.yellowCards;
    archivedStats.redCards = stats.redCards;
    archivedStats.passesCompleted = stats.passesCompleted;
    archivedStats.passesAttempted = stats.passesAttempted;
    archivedStats.successfulBallCarries = stats.successfulBallCarries;
    archivedStats.ballCarriesAttempted = stats.ballCarriesAttempted;
    archivedStats.shots = stats.shots;
    archivedStats.shotsOnTarget = stats.shotsOnTarget;
    archivedStats.goals = stats.goals;
    archivedStats.saves = stats.saves;
    archivedStats.tackles = stats.tackles;
    archivedStats.fouls = stats.fouls;
    return archivedStats;
  }
}
