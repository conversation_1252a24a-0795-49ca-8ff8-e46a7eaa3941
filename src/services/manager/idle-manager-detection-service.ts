import { Manager } from '@/entities/Manager.js';
import { logger } from '@/utils/logger.js';
import { EntityManager } from '@mikro-orm/core';

export interface IdleManagerDetectionService {
  findIdleManagers(gameworldId: string): Promise<Manager[]>;
}

export class IdleManagerDetectionServiceImpl implements IdleManagerDetectionService {
  constructor(private readonly em: EntityManager) {}

  /**
   * Find managers who have been inactive for over 2 weeks (14 days)
   * @param gameworldId The gameworld to check for idle managers
   * @returns Array of idle managers
   */
  async findIdleManagers(gameworldId: string): Promise<Manager[]> {
    const twoWeeksAgo = Date.now() - 14 * 24 * 60 * 60 * 1000; // 14 days in milliseconds

    logger.info('Searching for idle managers', {
      gameworldId,
      cutoffTime: twoWeeksAgo,
      cutoffDate: new Date(twoWeeksAgo).toISOString(),
    });

    try {
      const idleManagers = await this.em.find(
        Manager,
        {
          gameworldId,
          lastActive: { $lt: twoWeeksAgo },
          team: { $ne: null }, // Only managers who have teams
          isArchived: false, // Only non-archived managers
        },
        {
          populate: [
            'team',
            'team.players',
            'team.players.attributes',
            'team.players.overallStats',
          ],
        }
      );


      logger.info('Found idle managers', {
        gameworldId,
        count: idleManagers.length,
        managerIds: idleManagers.map((m) => m.managerId),
      });

      return idleManagers;
    } catch (error) {
      logger.error('Error finding idle managers', {
        gameworldId,
        error: error instanceof Error ? error.message : String(error),
      });
      throw error;
    }
  }

  /**
   * Check if a specific manager is idle (inactive for over 2 weeks)
   * @param managerId The manager ID to check
   * @returns True if the manager is idle, false otherwise
   */
  async isManagerIdle(managerId: string): Promise<boolean> {
    const twoWeeksAgo = Date.now() - 14 * 24 * 60 * 60 * 1000;

    try {
      const manager = await this.em.findOne(Manager, {
        managerId,
        lastActive: { $lt: twoWeeksAgo },
        isArchived: false,
      });

      return manager !== null;
    } catch (error) {
      logger.error('Error checking if manager is idle', {
        managerId,
        error: error instanceof Error ? error.message : String(error),
      });
      throw error;
    }
  }

  /**
   * Get the count of idle managers in a gameworld
   * @param gameworldId The gameworld to check
   * @returns Number of idle managers
   */
  async getIdleManagerCount(gameworldId: string): Promise<number> {
    const twoWeeksAgo = Date.now() - 14 * 24 * 60 * 60 * 1000;

    try {
      const count = await this.em.count(Manager, {
        gameworldId,
        lastActive: { $lt: twoWeeksAgo },
        team: { $ne: null },
        isArchived: false,
      });

      logger.info('Idle manager count', {
        gameworldId,
        count,
      });

      return count;
    } catch (error) {
      logger.error('Error getting idle manager count', {
        gameworldId,
        error: error instanceof Error ? error.message : String(error),
      });
      throw error;
    }
  }
}
