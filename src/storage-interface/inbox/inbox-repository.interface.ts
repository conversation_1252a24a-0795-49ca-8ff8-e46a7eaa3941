import { Inbox } from '@/entities/Inbox.js';

/**
 * Repository for inbox messages
 */
export interface InboxRepository {
  /**
   * Create a new inbox message
   * @param gameworldId The gameworld ID
   * @param teamId The team ID
   * @param date The date when the message was sent
   * @param message The message content
   * @param extra Additional information
   * @returns The created inbox message
   */
  createMessage(
    gameworldId: string,
    teamId: string,
    date: number,
    message: string,
    extra: string
  ): Promise<Inbox>;

  /**
   * Get an inbox message by ID
   * @param gameworldId The gameworld ID
   * @param teamId The team ID
   * @param messageId The ID of the message
   * @returns The inbox message, or null if not found
   */
  getMessage(gameworldId: string, teamId: string, messageId: string): Promise<Inbox | null>;

  /**
   * Get all inbox messages for a specific gameworld and team
   * @param gameworldId The gameworld ID
   * @param teamId The team ID
   * @param limit Optional limit for pagination
   * @returns Array of inbox messages
   */
  getMessagesByGameworldAndTeam(
    gameworldId: string,
    teamId: string,
    limit?: number
  ): Promise<Inbox[]>;

  /**
   * Update an inbox message
   * @param message The inbox message to update
   * @returns The updated inbox message
   */
  updateMessage(message: Inbox): Promise<Inbox>;

  /**
   * Delete an inbox message
   * @param gameworldId The gameworld ID
   * @param teamId The team ID
   * @param messageId The ID of the message to delete
   */
  deleteMessage(gameworldId: string, teamId: string, messageId: string): Promise<void>;
}
