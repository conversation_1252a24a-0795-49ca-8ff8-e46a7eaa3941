import { Gameworld } from '@/entities/Gameworld.js';

export interface GameworldRepository {
  /**
   * Get a gameworld by its ID
   * @param gameworldId The gameworld ID
   */
  getGameworld(gameworldId: string): Promise<Gameworld | null>;

  /**
   * Get all gameworlds
   */
  getAllGameworlds(): Promise<Gameworld[]>;

  getCompletedSeasons(): Promise<Gameworld[]>;

  /**
   * Create a new gameworld
   * @param gameworld The gameworld to create
   */
  createGameworld(gameworld: Gameworld): Promise<void>;

  /**
   * Update a gameworld
   * @param gameworld The gameworld to update
   */
  updateGameworld(gameworld: Gameworld): Promise<void>;

  updateGameworldEndDate(gameworldId: string, endDate: number): Promise<void>;
}
