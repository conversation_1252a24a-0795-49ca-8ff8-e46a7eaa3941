import { Gameworld } from '@/entities/Gameworld.js';
import { MikroOrmService } from '@/storage-interface/mikro-orm-service.js';
import { logger } from '@/utils/logger.js';
import { GameworldRepository } from './gameworld-repository.interface.js';

export class MikroOrmGameworldRepository implements GameworldRepository {
  constructor(private mikroOrmService: MikroOrmService) {}

  async getGameworld(gameworldId: string): Promise<Gameworld | null> {
    try {
      const em = this.mikroOrmService.getEntityManager();
      return em.findOne(Gameworld, { id: gameworldId });
    } catch (error) {
      logger.error('Failed to get gameworld:', { error, gameworldId });
      throw error;
    }
  }

  async getAllGameworlds(): Promise<Gameworld[]> {
    try {
      const em = this.mikroOrmService.getEntityManager();
      return em.find(Gameworld, {});
    } catch (error) {
      logger.error('Failed to get all gameworlds:', { error });
      throw error;
    }
  }

  async getCompletedSeasons(): Promise<Gameworld[]> {
    try {
      const em = this.mikroOrmService.getEntityManager();
      const now = Date.now();
      return em.find(Gameworld, { endDate: { $lt: now } });
    } catch (error) {
      logger.error('Failed to get all gameworlds:', { error });
      throw error;
    }
  }

  async createGameworld(gameworld: Gameworld): Promise<void> {
    try {
      const em = this.mikroOrmService.getEntityManager();
      em.persist(gameworld);
      await em.flush();
    } catch (error) {
      logger.error('Failed to create gameworld:', { error, gameworld });
      throw error;
    }
  }

  async updateGameworld(gameworld: Gameworld): Promise<void> {
    try {
      const em = this.mikroOrmService.getEntityManager();
      em.persist(gameworld);
      await em.flush();
    } catch (error) {
      logger.error('Failed to update gameworld:', { error, gameworld });
      throw error;
    }
  }

  async updateGameworldEndDate(gameworldId: string, endDate: number): Promise<void> {
    try {
      const em = this.mikroOrmService.getEntityManager();

      // Get the gameworld first
      const gameworld = await em.findOneOrFail(Gameworld, { id: gameworldId });

      // Update just the endDate field
      gameworld.endDate = BigInt(endDate);

      // Persist and flush changes
      em.persist(gameworld);
      await em.flush();

      logger.debug('Updated gameworld end date:', { gameworldId, endDate });
    } catch (error) {
      logger.error('Failed to update gameworld end date:', { error, gameworldId, endDate });
      throw error;
    }
  }
}
