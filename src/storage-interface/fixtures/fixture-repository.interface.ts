import { Fixture } from '@/entities/Fixture.js';
import { FixtureDetails, MatchEvent, MatchStats } from '@/model/fixture.js';
import { GamePlayer } from '@/simulation/types.js';
import { BaseRepository } from '@/storage-interface/base-repository.js';
import { SimulateFixturesEvent } from '@/types/generated/index.js';
import { Loaded } from '@mikro-orm/core';

export type GetFixturesByTeamResponse = Loaded<
  Fixture,
  'homeTeam' | 'awayTeam',
  | 'fixtureId'
  | 'gameworldId'
  | 'date'
  | 'played'
  | 'simulatedAt'
  | 'seed'
  | 'score'
  | 'scorers'
  | 'homeTeam.teamId'
  | 'homeTeam.teamName'
  | 'awayTeam.teamId'
>;

export interface IFixtureRepository extends BaseRepository<Fixture> {
  /**
   * Batch insert fixtures
   * @param fixtures The fixtures to insert
   */
  batchInsertFixtures(fixtures: Fixture[]): Promise<void>;

  /**
   * Get a fixture by its ID
   * @param fixtureId The fixture ID
   */
  getFixture(fixtureId: string): Promise<FixtureDetails | null>;

  /**
   * Get fixtures by league
   * @param gameworldId The gameworld ID
   * @param leagueId The league ID
   */
  getFixturesByLeague(gameworldId: string, leagueId: string): Promise<Fixture[]>;

  /**
   * Get fixtures by team
   * @param gameworldId The gameworld ID
   * @param teamId The team ID
   */
  getFixturesByTeam(gameworldId: string, teamId: string): Promise<GetFixturesByTeamResponse[]>;

  /**
   * Get unplayed fixtures with dates in the past
   * @param gameworldId Optional gameworld ID to filter by
   * @param leagueId Optional league ID to filter by
   * @param limit Number of records to return
   * @param offset Number of records to skip
   */
  getDueFixtures(
    gameworldId?: string,
    leagueId?: string,
    limit?: number,
    offset?: number
  ): Promise<SimulateFixturesEvent[]>;

  getAllUnplayedFixtures(gameworldId?: string, leagueId?: string): Promise<Fixture[]>;

  /**
   * Update fixture stats and mark as played
   * @param gameworldId The gameworld ID
   * @param leagueId The league ID
   * @param fixtureId The fixture ID
   * @param seed The seed used for the simulation
   * @param stats The match stats
   * @param events The match events
   * @param homePlayers The home team's players
   * @param awayPlayers The away team's players
   * @param hasHumanManager Whether either team has a human manager
   */
  updateFixtureResult(
    gameworldId: string,
    leagueId: string,
    fixtureId: string,
    seed: number,
    stats: MatchStats,
    events: MatchEvent[],
    homePlayers: GamePlayer[],
    awayPlayers: GamePlayer[],
    hasHumanManager: boolean
  ): Promise<void>;
}
