import { TeamTrainingSlot } from '@/entities/TeamTrainingSlot.js';
import { BaseRepository } from '@/storage-interface/base-repository.js';
import { Rel } from '@mikro-orm/core';

export interface TeamTrainingSlotRepository extends BaseRepository<TeamTrainingSlot> {
  getSlotById(id: string): Promise<TeamTrainingSlot | null>;
  getSlotsByTeam(teamId: string): Promise<TeamTrainingSlot[]>;
  getAllFilledSlots(): Promise<TeamTrainingSlot[]>;

  assignPlayerToSlot(
    id: string,
    playerId: string,
    attribute: string,
    startValue: number
  ): Promise<void>;
  clearSlot(id: string): Promise<void>;
  createSlot(slot: TeamTrainingSlot): Promise<void>;
  createFromPK(id: string): Rel<TeamTrainingSlot>;
  lockIAPSlot(teamId: string): Promise<void>;
}
