import { TeamTrainingSlot } from '@/entities/TeamTrainingSlot.js';
import { MikroOrmService } from '@/storage-interface/mikro-orm-service.js';
import { TeamTrainingSlotRepository } from '@/storage-interface/training/training-repository.interface.js';
import { logger } from '@/utils/logger.js';
import { Rel } from '@mikro-orm/core';
import { EntityManager } from '@mikro-orm/postgresql';

export class MikroOrmTeamTrainingSlotRepository implements TeamTrainingSlotRepository {
  constructor(private mikroOrmService: MikroOrmService) {}

  async getSlotById(id: string): Promise<TeamTrainingSlot | null> {
    const em = this.mikroOrmService.getEntityManager();
    return em.findOne(TeamTrainingSlot, { id }, { populate: ['player'] });
  }

  async getSlotsByTeam(teamId: string): Promise<TeamTrainingSlot[]> {
    const em = this.mikroOrmService.getEntityManager();
    return em.find(
      TeamTrainingSlot,
      { team: teamId },
      { populate: ['player', 'player.attributes'] }
    );
  }

  async getAllFilledSlots(): Promise<TeamTrainingSlot[]> {
    const em = this.mikroOrmService.getEntityManager();
    return em.find(
      TeamTrainingSlot,
      { player: { $ne: null } },
      { populate: ['player', 'player.attributes', 'team', 'team.manager'] }
    );
  }

  async assignPlayerToSlot(
    id: string,
    playerId: string,
    attribute: string,
    startValue: number
  ): Promise<void> {
    const em = this.mikroOrmService.getEntityManager();
    await em.nativeUpdate(
      TeamTrainingSlot,
      { id: id },
      { player: playerId, attribute, assignedAt: BigInt(Date.now()), startValue }
    );
  }

  async clearSlot(id: string): Promise<void> {
    const em = this.mikroOrmService.getEntityManager();
    await em.nativeUpdate(
      TeamTrainingSlot,
      { id },
      { player: null, attribute: null, assignedAt: null }
    );
  }

  async createSlot(slot: TeamTrainingSlot): Promise<void> {
    const em = this.mikroOrmService.getEntityManager();
    em.persist(slot);
    await em.flush();
  }

  /**
   * This should only be called if the user is given a refund for slot 5
   * @param teamId The team ID
   */
  async lockIAPSlot(teamId: string): Promise<void> {
    const em = this.mikroOrmService.getEntityManager();
    // find slot 4 for this team
    const slots = await em.find(TeamTrainingSlot, { team: teamId, slotIndex: 4 });
    logger.debug('Locking IAP slot', { teamId, slotCount: slots.length });
    // delete the found slot
    await em.removeAndFlush(slots);
  }

  createFromPK(id: string): Rel<TeamTrainingSlot> {
    const em = this.mikroOrmService.getEntityManager();
    return em.getReference(TeamTrainingSlot, id);
  }

  getEntityManager(): EntityManager {
    return this.mikroOrmService.getEntityManager();
  }
}
