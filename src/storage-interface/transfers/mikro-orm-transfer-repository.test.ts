import { BidHistory } from '@/entities/BidHistory.js';
import { TransferListedPlayer } from '@/entities/TransferListedPlayer.js';
import { MikroOrmService } from '@/storage-interface/mikro-orm-service.js';
import { MikroOrmTransferRepository } from '@/storage-interface/transfers/mikro-orm-transfer-repository.ts';
import { beforeEach, describe, expect, it, vi } from 'vitest';

// Mock the MikroOrmService
const mockEntityManager = {
  findOne: vi.fn(),
  find: vi.fn(),
  persist: vi.fn(),
  persistAndFlush: vi.fn(),
  getReference: vi.fn(),
};

const mockMikroOrmService = {
  getEntityManager: vi.fn(() => mockEntityManager),
} as unknown as MikroOrmService;

describe('MikroOrmTransferRepository - eBay Style Bidding', () => {
  let repository: MikroOrmTransferRepository;

  beforeEach(() => {
    vi.clearAllMocks();
    repository = new MikroOrmTransferRepository(mockMikroOrmService);
  });

  describe('calculateEbayStylePrice', () => {
    it('should return start price for no bids', () => {
      const result = (repository as any).calculateEbayStylePrice([], 100000, 100000, 'team1');

      expect(result).toEqual({
        newAuctionPrice: 100000,
        isHighestBidder: false,
      });
    });

    it('should return start price for first bid', () => {
      const bids = [{ team: { teamId: 'team1' }, maximumBid: 200000 } as BidHistory];

      const result = (repository as any).calculateEbayStylePrice(bids, 100000, 100000, 'team1');

      expect(result).toEqual({
        newAuctionPrice: 100000,
        isHighestBidder: true,
      });
    });

    // Test the exact eBay scenario from the user's description
    describe('User eBay Scenario Tests', () => {
      it('should handle scenario: Manager 1 bids 200k, Manager 2 bids 150k -> price becomes 160k', () => {
        // A player is available for 100,000
        // Manager 1 bids max 200,000 -> auction stays at 100,000
        // Manager 2 bids max 150,000 -> auction price becomes 150,000 + 10% = 160,000
        const bids = [
          { team: { teamId: 'manager1' }, maximumBid: 200000 } as BidHistory, // Highest bid
          { team: { teamId: 'manager2' }, maximumBid: 150000 } as BidHistory, // Second highest
        ];

        const result = (repository as any).calculateEbayStylePrice(
          bids,
          100000,
          100000,
          'manager2'
        );

        // 10% of 100,000 = 10,000, so 150,000 + 10,000 = 160,000
        expect(result).toEqual({
          newAuctionPrice: 160000,
          isHighestBidder: false, // Manager 2 is not the highest bidder
        });
      });

      it('should handle scenario: Manager 2 bids 250k -> price becomes 210k', () => {
        // Manager 2 now bids max 250,000
        // This is higher than Manager 1's 200,000
        // Price becomes 200,000 + 10% of roundDownToPowerOf10(160000) = 200,000 + 10,000 = 210,000
        const bids = [
          { team: { teamId: 'manager2' }, maximumBid: 250000 } as BidHistory, // Highest bid
          { team: { teamId: 'manager1' }, maximumBid: 200000 } as BidHistory, // Second highest
        ];

        const result = (repository as any).calculateEbayStylePrice(
          bids,
          100000,
          160000,
          'manager2'
        );

        // roundDownToPowerOf10(160000) = 100000, 10% of 100,000 = 10,000, so 200,000 + 10,000 = 210,000
        expect(result).toEqual({
          newAuctionPrice: 210000,
          isHighestBidder: true,
        });
      });

      it('should handle scenario: Manager 3 bids 260k -> price becomes 260k (capped)', () => {
        // Manager 3 bids 260,000
        // This would normally be 250,000 + 10% of roundDownToPowerOf10(210000) = 250,000 + 20,000 = 270,000
        // But it's capped at Manager 3's maximum of 260,000
        const bids = [
          { team: { teamId: 'manager3' }, maximumBid: 260000 } as BidHistory, // Highest bid
          { team: { teamId: 'manager2' }, maximumBid: 250000 } as BidHistory, // Second highest
          { team: { teamId: 'manager1' }, maximumBid: 200000 } as BidHistory, // Third highest
        ];

        const result = (repository as any).calculateEbayStylePrice(
          bids,
          100000,
          210000,
          'manager3'
        );

        // roundDownToPowerOf10(210000) = 200000, 10% of 200,000 = 20,000, so 250,000 + 20,000 = 270,000
        // But capped at 260,000 (Manager 3's maximum)
        expect(result).toEqual({
          newAuctionPrice: 260000,
          isHighestBidder: true,
        });
      });
    });

    it('should handle equal bids', () => {
      const bids = [
        { team: { teamId: 'team1' }, maximumBid: 200000 } as BidHistory,
        { team: { teamId: 'team2' }, maximumBid: 200000 } as BidHistory,
      ];

      const result = (repository as any).calculateEbayStylePrice(bids, 100000, 100000, 'team1');

      expect(result).toEqual({
        newAuctionPrice: 200000,
        isHighestBidder: true,
      });
    });

    it('should correctly identify non-highest bidder', () => {
      const bids = [
        { team: { teamId: 'team2' }, maximumBid: 250000 } as BidHistory, // Highest bid
        { team: { teamId: 'team1' }, maximumBid: 200000 } as BidHistory, // Second highest
      ];

      const result = (repository as any).calculateEbayStylePrice(bids, 100000, 200000, 'team1');

      expect(result).toEqual({
        newAuctionPrice: 220000,
        isHighestBidder: false,
      });
    });
  });

  describe('roundDownToPowerOf10', () => {
    it('should round down to power of 10 correctly', () => {
      expect((repository as any).roundDownToPowerOf10(150000)).toBe(100000);
      expect((repository as any).roundDownToPowerOf10(250000)).toBe(200000);
      expect((repository as any).roundDownToPowerOf10(99999)).toBe(90000);
      expect((repository as any).roundDownToPowerOf10(1000)).toBe(1000);
      expect((repository as any).roundDownToPowerOf10(999)).toBe(900);
    });
  });

  describe('sendOutbidNotifications', () => {
    it('should skip notifications when no repositories provided', async () => {
      const mockPreviousHighestBidder = { team: { teamId: 'team1' } } as BidHistory;
      const mockAllBids = [{ team: { teamId: 'team2' } } as BidHistory];
      const mockTransferListedPlayer = { id: 'player1' } as TransferListedPlayer;

      // Should not throw when repositories is undefined
      await expect(
        (repository as any).sendOutbidNotifications(
          mockPreviousHighestBidder,
          mockAllBids,
          mockTransferListedPlayer,
          200000,
          'team2',
          undefined
        )
      ).resolves.toBeUndefined();
    });
  });

  describe('submitBid', () => {
    it('should reject a bid lower than the current price', async () => {
      const mockPlayerId = 'player1';
      const mockTeamId = 'team1';
      const mockCurrentPrice = 200000;
      const mockMaxBid = 150000; // Lower than current price

      // Mock the transfer listed player
      const mockTransferListedPlayer = {
        id: 'transfer1',
        auctionStartPrice: 100000,
        auctionCurrentPrice: mockCurrentPrice,
        bidHistory: [],
        player: { id: mockPlayerId },
      } as unknown as TransferListedPlayer;

      mockEntityManager.findOne.mockResolvedValueOnce(mockTransferListedPlayer);
      mockEntityManager.find.mockResolvedValueOnce([
        {
          transferListing: mockTransferListedPlayer.id,
          team: { teamId: mockTeamId },
          maximumBid: mockCurrentPrice,
          bidTime: Date.now(),
        },
      ]);

      const result = await repository.submitBid(mockPlayerId, mockMaxBid, mockTeamId);
      expect(result).toEqual({
        maxBid: mockCurrentPrice,
        highestBidder: false,
      });
    });

    it('should accept a valid bid higher than the current price', async () => {
      const mockPlayerId = 'player1';
      const mockTeamId = 'team1';
      const mockCurrentPrice = 200000;
      const mockMaxBid = 250000; // Higher than current price

      // Mock the transfer listed player
      const mockTransferListedPlayer = {
        id: 'transfer1',
        auctionStartPrice: 100000,
        auctionCurrentPrice: mockCurrentPrice,
        bidHistory: [],
        player: { id: mockPlayerId },
      } as unknown as TransferListedPlayer;

      mockEntityManager.findOne.mockResolvedValueOnce(mockTransferListedPlayer);
      mockEntityManager.find.mockResolvedValueOnce([
        {
          transferListing: mockTransferListedPlayer.id,
          team: { teamId: mockTeamId },
          maximumBid: mockCurrentPrice,
          bidTime: Date.now(),
        },
      ]);
      mockEntityManager.find.mockResolvedValueOnce([
        {
          transferListing: mockTransferListedPlayer.id,
          team: { teamId: mockTeamId },
          maximumBid: mockMaxBid,
          bidTime: Date.now(),
        },
        {
          transferListing: mockTransferListedPlayer.id,
          team: { teamId: mockTeamId },
          maximumBid: mockCurrentPrice,
          bidTime: Date.now(),
        },
      ]);

      const result = await repository.submitBid(mockPlayerId, mockMaxBid, mockTeamId);

      expect(result).toEqual({
        maxBid: 220000,
        highestBidder: true,
      });
    });

    it('should adjust the current price if there is a higher bid', async () => {
      const mockPlayerId = 'player1';
      const mockTeamId = 'team1';
      const mockOtherTeamId = 'team2';
      const mockCurrentPrice = 200000;
      const mockMaxBid = 230000; // Higher than current price

      // Mock the transfer listed player
      const mockTransferListedPlayer = {
        id: 'transfer1',
        auctionStartPrice: 100000,
        auctionCurrentPrice: mockCurrentPrice,
        bidHistory: [],
        player: { id: mockPlayerId },
      } as unknown as TransferListedPlayer;

      mockEntityManager.findOne.mockResolvedValueOnce(mockTransferListedPlayer);
      mockEntityManager.find.mockResolvedValueOnce([
        {
          transferListing: mockTransferListedPlayer.id,
          team: { teamId: mockOtherTeamId },
          maximumBid: 250000,
          bidTime: Date.now(),
        },
      ]);
      mockEntityManager.find.mockResolvedValueOnce([
        {
          transferListing: mockTransferListedPlayer.id,
          team: { teamId: mockOtherTeamId },
          maximumBid: 300000,
          bidTime: Date.now(),
        },
        {
          transferListing: mockTransferListedPlayer.id,
          team: { teamId: mockTeamId },
          maximumBid: mockMaxBid,
          bidTime: Date.now(),
        },
      ]);

      const result = await repository.submitBid(mockPlayerId, mockMaxBid, mockTeamId);

      expect(result).toEqual({
        maxBid: 250000,
        highestBidder: false,
      });
    });
  });
});
