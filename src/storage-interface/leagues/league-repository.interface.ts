import { League } from '@/entities/League.js';
import { Loaded, type Rel } from '@mikro-orm/core';

export type GetLeagueResponse = Loaded<
  League,
  'leagueRules' | 'teams',
  | 'id'
  | 'name'
  | 'leagueRules'
  | 'teams.teamId'
  | 'teams.teamName'
  | 'teams.played'
  | 'teams.points'
  | 'teams.goalsFor'
  | 'teams.goalsAgainst'
  | 'teams.wins'
  | 'teams.draws'
  | 'teams.losses',
  never
> | null;

export interface LeagueRepository {
  batchCreateLeagues(leagues: League[]): Promise<void>;

  getLeaguesByGameworld(gameworldId: string, includeTeams?: boolean): Promise<League[]>;

  getLeague(gameworldId: string, leagueId: string): Promise<GetLeagueResponse>;

  getLeagueHierarchy(gameworldId: string, rootLeagueId: string): Promise<League[]>;

  updateLeague(league: League): Promise<void>;

  // Add other methods as needed
  createFromPK(id: string): Rel<League>;
}
