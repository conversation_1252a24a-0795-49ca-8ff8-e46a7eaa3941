import { League } from '@/entities/League.js';
import { MikroOrmService } from '@/storage-interface/mikro-orm-service.js';
import { logger } from '@/utils/logger.js';
import { FilterQuery, type Rel } from '@mikro-orm/core';
import { GetLeagueResponse, LeagueRepository } from './league-repository.interface.js';

export class MikroOrmLeagueRepository implements LeagueRepository {
  constructor(private mikroOrmService: MikroOrmService) {}

  createFromPK(id: string): Rel<League> {
    const em = this.mikroOrmService.getEntityManager();
    return em.getReference(League, id);
  }

  async batchCreateLeagues(leagues: League[]): Promise<void> {
    try {
      const em = this.mikroOrmService.getEntityManager();
      for (const league of leagues) {
        em.persist(league);
      }
      await em.flush();
    } catch (error) {
      logger.error('Failed to create leagues:', { error });
      throw error;
    }
  }

  async getLeaguesByGameworld(
    gameworldId: string,
    includeTeams: boolean = false
  ): Promise<League[]> {
    const em = this.mikroOrmService.getEntityManager();
    const where: FilterQuery<League> = { gameworld: gameworldId };

    if (includeTeams) {
      return em.find(League, where, { populate: ['teams', 'teams.manager', 'leagueRules'] });
    }

    return em.find(League, where);
  }

  async getLeague(gameworldId: string, leagueId: string): Promise<GetLeagueResponse> {
    const em = this.mikroOrmService.getEntityManager();

    return em.findOne(
      League,
      { gameworld: gameworldId, id: leagueId },
      {
        populate: ['teams', 'leagueRules'],
        fields: [
          'id',
          'name',
          'leagueRules',
          'teams.teamName',
          'teams.teamId',
          'teams.played',
          'teams.points',
          'teams.goalsFor',
          'teams.goalsAgainst',
          'teams.wins',
          'teams.draws',
          'teams.losses',
        ],
      }
    );
  }

  async getLeagueHierarchy(gameworldId: string, rootLeagueId: string): Promise<League[]> {
    const leagues: League[] = [];
    const em = this.mikroOrmService.getEntityManager();

    const rootLeague = await em.findOne(
      League,
      { gameworld: gameworldId, id: rootLeagueId },
      {
        populate: ['leagueChildren'],
      }
    );

    if (!rootLeague) return leagues;

    leagues.push(rootLeague);

    if (rootLeague.leagueChildren && rootLeague.leagueChildren.length > 0) {
      for (const child of rootLeague.leagueChildren) {
        const childLeagues = await this.getLeagueHierarchy(gameworldId, child.id);
        leagues.push(...childLeagues);
      }
    }

    return leagues;
  }

  async updateLeague(league: League): Promise<void> {
    const em = this.mikroOrmService.getEntityManager();
    await em.persistAndFlush(league);
  }
}
