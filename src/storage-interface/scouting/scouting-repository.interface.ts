import { Player } from '@/entities/Player.js';
import { ScoutedPlayer } from '@/entities/ScoutedPlayer.js';
import { Loaded } from '@mikro-orm/core';

/**
 * Repository for scouting operations
 */
export interface ScoutingRepository {
  /**
   * Get random players from a league and mark them as scouted by a team
   * @param gameworldId The gameworld ID
   * @param leagueId The league ID
   * @param teamId The team ID
   * @param count The number of players to scout
   * @returns Array of scouted players
   */
  scoutRandomPlayersFromLeague(
    gameworldId: string,
    leagueId: string,
    teamId: string,
    count: number
  ): Promise<Player[]>;

  /**
   * Get players from a team that haven't been scouted yet by the scouting team
   * @param gameworldId The gameworld ID
   * @param targetTeamId The ID of the team being scouted
   * @param scoutingTeamId The ID of the team doing the scouting
   * @param count The maximum number of players to scout
   * @returns Array of players to scout
   */
  scoutPlayersFromTeam(
    gameworldId: string,
    targetTeamId: string,
    scoutingTeamId: string,
    count: number
  ): Promise<Player[]>;

  /**
   * Check if a player has been scouted by a specific team
   * @param gameworldId The gameworld ID
   * @param playerId The player ID
   * @param teamId The team ID
   * @returns True if the player has been scouted by the team, false otherwise
   */
  isPlayerScoutedByTeam(gameworldId: string, playerId: string, teamId: string): Promise<boolean>;

  /**
   * Get all players scouted by a specific team
   * @param gameworldId The gameworld ID
   * @param teamId The team ID
   * @param limit The maximum number of players to return
   * @param lastEvaluatedKey The last evaluated key for pagination
   * @returns Array of scouted players
   */
  getPlayersScoutedByTeam(
    gameworldId: string,
    teamId: string,
    limit?: number,
    lastEvaluatedKey?: string
  ): Promise<Loaded<ScoutedPlayer, 'player' | 'player.attributes'>[]>;

  /**
   * Save players as scouted by a team
   * @param gameworldId The gameworld ID
   * @param teamId The team ID
   * @param playerIds Array of player IDs to mark as scouted
   * @returns Promise that resolves when the players are saved as scouted
   */
  saveScoutedPlayers(gameworldId: string, teamId: string, playerIds: string[]): Promise<void>;
}
