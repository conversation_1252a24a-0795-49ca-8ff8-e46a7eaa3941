import { AvailableTeam } from '@/entities/AvailableTeam.js';
import { Fixture } from '@/entities/Fixture.js';
import { Team } from '@/entities/Team.js';
import { Transactions } from '@/entities/Transactions.js';
import { TeamMovement } from '@/functions/league/logic/LeagueProcessorV2.js';
import { LeagueStandings } from '@/model/team.js';
import { BaseRepository } from '@/storage-interface/base-repository.js';
import { PlayerRepository } from '@/storage-interface/players/index.js';
import { TransferRepository } from '@/storage-interface/transfers/index.js';
import { Rel } from '@mikro-orm/core';
import { SqlEntityManager } from '@mikro-orm/postgresql';

export enum TransactionType {
  TRANSFER = 'transfer',
  SCOUTING = 'scouting',
  TRAINING = 'training',
  DAILY_REWARD = 'DAILY_REWARD',
  TICKET_INCOME = 'TICKET_INCOME',
  PROGRAMME_INCOME = 'PROGRAMME_INCOME',
  FOOD_INCOME = 'FOOD_INCOME',
  DRINKS_INCOME = 'DRINKS_INCOME',
  MERCHANDISE_INCOME = 'MERCHANDISE_INCOME',
  PLAYER_WAGES = 'PLAYER_WAGES',
  GROUND_MAINTENANCE = 'GROUND_MAINTENANCE',
  PRIZE_MONEY = 'PRIZE_MONEY',
}

export interface TeamRepository extends BaseRepository<Team> {
  batchInsertTeams(teams: Team[]): Promise<void>;
  batchInsertAvailableTeams(teams: AvailableTeam[]): Promise<void>;
  getTeamsByGameworld(gameworldId: string, includePlayers?: boolean): Promise<Team[]>;
  getTeamsByLeague(leagueId: string, includePlayers?: boolean): Promise<Team[]>;
  updateTeamLeague(teamId: string, gameworldId: string, newLeagueId: string): Promise<void>;
  resetTeamStandings(teamId: string, gameworldId: string): Promise<void>;
  updateTeamLeagues(teams: Team[], movements: TeamMovement[], gameworldId: string): Promise<void>;
  countAvailableTeams(): Promise<number>;
  getRandomAvailableTeam(): Promise<AvailableTeam | null>;
  deleteAvailableTeam(team: AvailableTeam): Promise<number>;
  updateTeamStandings(
    teamId: string,
    gameworldId: string,
    standings: LeagueStandings,
    flush?: boolean
  ): Promise<void>;

  getTeam(gameworldId: string, teamId: string, includePlayers: boolean): Promise<Team | null>;

  /**
   * Get team with minimal player data for simulation (no match history)
   */
  getTeamForSimulation(gameworldId: string, teamId: string): Promise<Team | null>;

  /**
   * Find multiple teams by their IDs for AI processing (minimal data)
   * @param teamIds Array of team IDs to find
   * @returns Array of teams with minimal player data for AI
   */
  findByIdsForAI(teamIds: string[]): Promise<Team[]>;

  /**
   * Find multiple teams by their IDs
   * @param teamIds Array of team IDs to find
   * @param includePlayers Whether to include players in the response
   * @returns Array of teams matching the provided IDs
   */
  findByIds(teamIds: string[], includePlayers?: boolean): Promise<Team[]>;

  /**
   * Get all teams without a manager
   * @param includePlayers Whether to include players in the response
   */
  getTeamsWithoutManager(includePlayers?: boolean): Promise<Team[]>;

  /**
   * Update a team's balance
   * @param teamId The team ID
   * @param gameworldId The gameworld ID
   * @param amount The amount to add to the balance
   * @param type The type of transaction
   * @param flush Should we flush the database
   */
  updateTeamBalance(
    teamId: string,
    gameworldId: string,
    amount: number,
    type: TransactionType,
    flush?: boolean
  ): Promise<void>;

  /**
   * Update a team's balance
   * @param teamId The team ID
   * @param gameworldId The gameworld ID
   * @param items Array of individual transactions
   * @param flush Should we flush the database
   */
  updateTeamBalances(
    teamId: string,
    gameworldId: string,
    items: { amount: number; type: TransactionType }[],
    flush?: boolean
  ): Promise<void>;

  /**
   * Update a team's selection order
   * @param teamId The team ID
   * @param gameworldId The gameworld ID
   * @param selectionOrder The new selection order array of player IDs
   */
  updateTeamSelectionOrder(
    teamId: string,
    gameworldId: string,
    selectionOrder: string[]
  ): Promise<void>;

  /**
   * Update a team's name
   * @param teamId The team ID
   * @param gameworldId The gameworld ID
   * @param teamName The new team name
   */
  updateTeamName(teamId: string, gameworldId: string, teamName: string): Promise<void>;

  getTeamAndNextMatch(
    gameworldId: string,
    teamId: string,
    includePlayers: boolean
  ): Promise<{ team: Team | null; nextFixture: Fixture | null }>;

  incrementTrainingLevel(team: Team, cost: number): Promise<void>;

  flush(): Promise<void>;

  createFromPK(teamId: string): Rel<Team>;

  getTransactions(number: number, teamId: string): Promise<Transactions[]>;

  /**
   * Remove a team from the AvailableTeam table by team ID
   * @param gameworldId The gameworld ID
   * @param teamId The team ID to remove
   */
  removeAvailableTeamByTeamId(gameworldId: string, teamId: string): Promise<void>;

  /**
   * Add a team to the AvailableTeam table
   * @param gameworldId The gameworld ID
   * @param teamId The team ID to add
   */
  addAvailableTeam(gameworldId: string, teamId: string): Promise<void>;

  /**
   * Get an AvailableTeam record by team ID
   * @param gameworldId The gameworld ID
   * @param teamId The team ID to find
   */
  getAvailableTeamByTeamId(gameworldId: string, teamId: string): Promise<AvailableTeam | null>;

  reinitialiseTeam(
    teamId: string,
    gameworldId: string,
    playerRepository: PlayerRepository,
    transferRepository: TransferRepository,
    txEm?: SqlEntityManager
  ): Promise<void>;

  /**
   * Get and delete an available team (atomic operation)
   */
  getAndDeleteAvailableTeam(txEm?: SqlEntityManager): Promise<AvailableTeam | null>;

  /**
   * Release a team back to the available teams pool
   * @param teamId The team ID to release
   * @param gameworldId The gameworld ID
   */
  releaseTeamToAvailable(teamId: string, gameworldId: string): Promise<void>;
}
