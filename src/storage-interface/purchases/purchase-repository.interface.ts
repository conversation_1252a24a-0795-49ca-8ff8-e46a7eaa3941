import { Purchases, PurchaseStatus } from '@/entities/Purchases.js';
import { BaseRepository } from '@/storage-interface/base-repository.js';

export interface PurchaseRepository extends BaseRepository<Purchases> {
  /**
   * Create a new purchase record
   * @param purchase The purchase to create
   */
  createPurchase(purchase: Purchases): Promise<void>;

  /**
   * Get a purchase by transaction ID
   * @param transactionId The transaction ID
   */
  getPurchaseByTransactionId(transactionId: string): Promise<Purchases | null>;

  /**
   * Get a purchase by RevenueCat event ID
   * @param eventId The RevenueCat event ID
   */
  getPurchaseByEventId(eventId: string): Promise<Purchases | null>;

  /**
   * Get all purchases for a manager
   * @param managerId The manager ID
   */
  getPurchasesByManagerId(managerId: string): Promise<Purchases[]>;

  /**
   * Get active subscriptions for a manager
   * @param managerId The manager ID
   */
  getActiveSubscriptionsByManagerId(managerId: string): Promise<Purchases[]>;

  /**
   * Update purchase status
   * @param transactionId The transaction ID
   * @param status The new status
   * @param expirationAt Optional new expiration timestamp
   * @param cancelReason Optional cancel reason
   * @param expirationReason Optional expiration reason
   */
  updatePurchaseStatus(
    transactionId: string,
    status: PurchaseStatus,
    expirationAt?: number,
    cancelReason?: string,
    expirationReason?: string
  ): Promise<void>;

  /**
   * Get purchases by product ID for a manager
   * @param managerId The manager ID
   * @param productId The product ID
   */
  getPurchasesByManagerAndProduct(managerId: string, productId: string): Promise<Purchases[]>;

  /**
   * Get expired subscriptions that need processing
   * @param beforeTimestamp Only get subscriptions that expired before this timestamp
   */
  getExpiredSubscriptions(beforeTimestamp: number): Promise<Purchases[]>;

  /**
   * Upsert a purchase (create or update based on transaction ID)
   * @param purchase The purchase data
   */
  upsertPurchase(purchase: Purchases): Promise<void>;
}
