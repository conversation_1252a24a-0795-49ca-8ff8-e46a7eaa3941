import { Purchases, PurchaseStatus } from '@/entities/Purchases.js';
import { MikroOrmService } from '@/storage-interface/mikro-orm-service.js';
import { logger } from '@/utils/logger.js';
import { LockMode, type Rel } from '@mikro-orm/core';
import { SqlEntityManager } from '@mikro-orm/postgresql';
import { PurchaseRepository } from './purchase-repository.interface.js';

export class MikroOrmPurchaseRepository implements PurchaseRepository {
  constructor(private mikroOrmService: MikroOrmService) {}

  getEntityManager(): SqlEntityManager {
    return this.mikroOrmService.getEntityManager();
  }

  createFromPK(id: string): Rel<Purchases> {
    return this.mikroOrmService.getEntityManager().getReference(Purchases, id);
  }

  async createPurchase(purchase: Purchases): Promise<void> {
    try {
      const em = this.mikroOrmService.getEntityManager();
      em.persist(purchase);
      await em.flush();
    } catch (error) {
      logger.error('Failed to create purchase:', { error });
      throw error;
    }
  }

  async getPurchaseByTransactionId(transactionId: string): Promise<Purchases | null> {
    try {
      const em = this.mikroOrmService.getEntityManager();
      return await em.findOne(Purchases, { transactionId }, { populate: ['manager'] });
    } catch (error) {
      logger.error('Failed to get purchase by transaction ID:', { error, transactionId });
      throw error;
    }
  }

  async getPurchaseByEventId(eventId: string): Promise<Purchases | null> {
    try {
      const em = this.mikroOrmService.getEntityManager();
      return await em.findOne(Purchases, { revenueCatEventId: eventId }, { populate: ['manager'] });
    } catch (error) {
      logger.error('Failed to get purchase by event ID:', { error, eventId });
      throw error;
    }
  }

  async getPurchasesByManagerId(managerId: string): Promise<Purchases[]> {
    try {
      const em = this.mikroOrmService.getEntityManager();
      return await em.find(Purchases, { manager: { managerId } }, { populate: ['manager'] });
    } catch (error) {
      logger.error('Failed to get purchases by manager ID:', { error, managerId });
      throw error;
    }
  }

  async getActiveSubscriptionsByManagerId(managerId: string): Promise<Purchases[]> {
    try {
      const em = this.mikroOrmService.getEntityManager();
      const now = Date.now();
      return await em.find(
        Purchases,
        {
          manager: { managerId },
          status: PurchaseStatus.ACTIVE,
          $or: [
            { expirationAt: { $gt: now } },
            { expirationAt: null }, // Non-renewing purchases
          ],
        },
        { populate: ['manager'] }
      );
    } catch (error) {
      logger.error('Failed to get active subscriptions by manager ID:', { error, managerId });
      throw error;
    }
  }

  async updatePurchaseStatus(
    transactionId: string,
    status: PurchaseStatus,
    expirationAt?: number,
    cancelReason?: string,
    expirationReason?: string
  ): Promise<void> {
    try {
      const em = this.mikroOrmService.getEntityManager();
      await em.transactional(async (transactionalEm) => {
        const purchase = await transactionalEm.findOne(
          Purchases,
          { transactionId },
          { lockMode: LockMode.PESSIMISTIC_WRITE }
        );

        if (!purchase) {
          throw new Error(`Purchase with transaction ID ${transactionId} not found`);
        }

        const updates: Partial<Purchases> = {
          status,
          updatedAt: BigInt(Date.now()),
        };

        if (expirationAt !== undefined) {
          updates.expirationAt = BigInt(expirationAt);
        }
        if (cancelReason !== undefined) {
          updates.cancelReason = cancelReason;
        }
        if (expirationReason !== undefined) {
          updates.expirationReason = expirationReason;
        }

        transactionalEm.assign(purchase, updates);
        await transactionalEm.persistAndFlush(purchase);
      });
    } catch (error) {
      logger.error('Failed to update purchase status:', { error, transactionId, status });
      throw error;
    }
  }

  async getPurchasesByManagerAndProduct(
    managerId: string,
    productId: string
  ): Promise<Purchases[]> {
    try {
      const em = this.mikroOrmService.getEntityManager();
      return await em.find(
        Purchases,
        { manager: { managerId }, productId },
        { populate: ['manager'], orderBy: { createdAt: 'DESC' } }
      );
    } catch (error) {
      logger.error('Failed to get purchases by manager and product:', {
        error,
        managerId,
        productId,
      });
      throw error;
    }
  }

  async getExpiredSubscriptions(beforeTimestamp: number): Promise<Purchases[]> {
    try {
      const em = this.mikroOrmService.getEntityManager();
      return await em.find(
        Purchases,
        {
          status: PurchaseStatus.ACTIVE,
          expirationAt: { $lte: beforeTimestamp, $ne: null },
        },
        { populate: ['manager'] }
      );
    } catch (error) {
      logger.error('Failed to get expired subscriptions:', { error, beforeTimestamp });
      throw error;
    }
  }

  async upsertPurchase(purchase: Purchases): Promise<void> {
    try {
      const em = this.mikroOrmService.getEntityManager();
      await em.transactional(async (transactionalEm) => {
        const existing = await transactionalEm.findOne(
          Purchases,
          { transactionId: purchase.transactionId },
          { lockMode: LockMode.PESSIMISTIC_WRITE }
        );

        if (existing) {
          // Update existing purchase
          transactionalEm.assign(existing, {
            ...purchase,
            id: existing.id, // Keep the original ID
            createdAt: existing.createdAt, // Keep the original creation time
            updatedAt: BigInt(Date.now()),
          });
          await transactionalEm.persistAndFlush(existing);
        } else {
          // Create new purchase
          purchase.createdAt = BigInt(Date.now());
          purchase.updatedAt = BigInt(Date.now());
          transactionalEm.persist(purchase);
          await transactionalEm.flush();
        }
      });
    } catch (error) {
      logger.error('Failed to upsert purchase:', { error, transactionId: purchase.transactionId });
      throw error;
    }
  }
}
