import { Manager } from '@/entities/Manager.js';
import { NotificationPreferences } from '@/model/manager.js';
import { MikroOrmService } from '@/storage-interface/mikro-orm-service.js';
import { logger } from '@/utils/logger.js';
import { LockMode, type Rel } from '@mikro-orm/core';
import { SqlEntityManager } from '@mikro-orm/postgresql';
import { ManagerRepository } from './manager-repository.interface.js';

export class MikroOrmManagerRepository implements ManagerRepository {
  constructor(private mikroOrmService: MikroOrmService) {}

  getEntityManager(): SqlEntityManager {
    return this.mikroOrmService.getEntityManager();
  }
  createFromPK(id: string): Rel<Manager> {
    throw new Error('Method not implemented.');
    // this doesnt work
    // return this.mikroOrmService.getEntityManager().getReference(Manager, { managerId: id });
  }

  async createManager(manager: Manager, txEm?: SqlEntityManager): Promise<void> {
    try {
      const em = txEm ?? this.mikroOrmService.getEntityManager();
      em.persist(manager);
      await em.flush();
    } catch (error) {
      logger.error('Failed to create manager:', { error });
      throw error;
    }
  }

  async getManagerById(managerId: string, populateTeam: boolean = true): Promise<Manager | null> {
    try {
      const em = this.mikroOrmService.getEntityManager();
      return await em.findOne(
        Manager,
        { managerId },
        { populate: populateTeam ? ['team'] : undefined }
      );
    } catch (error) {
      logger.error('Failed to get manager by ID:', { error, managerId });
      throw error;
    }
  }

  async deleteManager(managerId: string): Promise<void> {
    try {
      const em = this.mikroOrmService.getEntityManager();
      const manager = await em.findOne(Manager, { managerId });

      if (manager) {
        em.remove(manager);
        await em.flush();
      }
    } catch (error) {
      logger.error('Failed to delete manager:', { error, managerId });
      throw error;
    }
  }

  async updateNotificationPreferences(
    managerId: string,
    preferences: Partial<NotificationPreferences>
  ): Promise<void> {
    try {
      const em = this.mikroOrmService.getEntityManager();
      const manager = await em.findOne(Manager, { managerId });

      if (!manager) {
        throw new Error(`Manager with ID ${managerId} not found`);
      }

      manager.notificationPreferences = {
        ...manager.notificationPreferences,
        ...preferences,
      };

      await em.persistAndFlush(manager);
    } catch (error) {
      logger.error('Failed to update manager notification preferences:', { error, managerId });
      throw error;
    }
  }

  async getManagerByTeamId(teamId: string): Promise<Manager | null> {
    try {
      const em = this.mikroOrmService.getEntityManager();
      return await em.findOne(Manager, { team: { teamId } }, { populate: ['team'] });
    } catch (error) {
      logger.error('Failed to get manager by team ID:', { error, teamId });
      throw error;
    }
  }

  async updateMagicSpongeCount(managerId: string, count: number): Promise<void> {
    const em = this.mikroOrmService.getEntityManager();
    await em.transactional(async (transactionalEm) => {
      const manager = await transactionalEm.findOne(
        Manager,
        { managerId },
        { lockMode: LockMode.PESSIMISTIC_WRITE }
      );

      if (!manager) {
        throw new Error(`Manager with ID ${managerId} not found`);
      }
      if (count < 0 && manager.magicSponges === 0) {
        throw new Error(`Manager with ID ${managerId} has no magic sponges`);
      }

      manager.magicSponges = (manager.magicSponges ?? 0) + count;

      await transactionalEm.persistAndFlush(manager);
    });
  }

  async updateCardAppealCount(managerId: string, count: number): Promise<void> {
    const em = this.mikroOrmService.getEntityManager();
    await em.transactional(async (transactionalEm) => {
      const manager = await transactionalEm.findOne(
        Manager,
        { managerId },
        { lockMode: LockMode.PESSIMISTIC_WRITE }
      );

      if (!manager) {
        throw new Error(`Manager with ID ${managerId} not found`);
      }
      if (count < 0 && manager.cardAppeals === 0) {
        throw new Error(`Manager with ID ${managerId} has no card appeals`);
      }

      manager.cardAppeals = (manager.cardAppeals ?? 0) + count;

      await transactionalEm.persistAndFlush(manager);
    });
  }

  async updateManagerById(managerId: string, updates: Partial<Manager>): Promise<void> {
    try {
      const em = this.mikroOrmService.getEntityManager();
      const manager = await em.findOne(Manager, { managerId });

      if (!manager) {
        throw new Error(`Manager with ID ${managerId} not found`);
      }

      Object.assign(manager, updates);
      await em.persistAndFlush(manager);
    } catch (error) {
      logger.error('Failed to update manager by ID:', { error, managerId });
      throw error;
    }
  }

  async updateManager(manager: Manager): Promise<void> {
    try {
      const em = this.mikroOrmService.getEntityManager();
      await em.persistAndFlush(manager);
    } catch (error) {
      logger.error('Failed to update manager:', { error });
      throw error;
    }
  }

  async findIdleManagers(gameworldId: string): Promise<Manager[]> {
    const twoWeeksAgo = Date.now() - 14 * 24 * 60 * 60 * 1000; // 14 days in milliseconds
    const em = this.mikroOrmService.getEntityManager();
    return em.find(
      Manager,
      {
        gameworldId,
        lastActive: { $lt: twoWeeksAgo },
        team: { $ne: null }, // Only managers who have teams
        isArchived: false, // Only non-archived managers
      },
      {
        populate: ['team', 'team.players', 'team.players.attributes', 'team.players.overallStats'],
      }
    );
  }
}
