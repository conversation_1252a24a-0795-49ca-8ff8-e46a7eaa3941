import { Manager } from '@/entities/Manager.js';
import { BaseRepository } from '@/storage-interface/base-repository.js';
import { SqlEntityManager } from '@mikro-orm/postgresql';

export interface ManagerRepository extends BaseRepository<Manager> {
  /**
   * Create a new manager
   * @param manager The manager to create
   * @param txEm Optional transactional entity manager
   */
  createManager(manager: Manager, txEm?: SqlEntityManager): Promise<void>;

  /**
   * Get a manager by ID
   * @param managerId The ID of the manager to get
   * @param populateTeam Whether to populate the team data
   */
  getManagerById(managerId: string, populateTeam?: boolean): Promise<Manager | null>;

  /**
   * Update a manager by ID with partial updates
   * @param managerId The ID of the manager to update
   * @param updates The updates to apply
   */
  updateManagerById(managerId: string, updates: Partial<Manager>): Promise<void>;

  /**
   * Update a manager entity
   * @param manager The manager entity to update
   */
  update<PERSON>anager(manager: Manager): Promise<void>;

  /**
   * Delete a manager
   * @param managerId The ID of the manager to delete
   */
  deleteManager(managerId: string): Promise<void>;

  /**
   * Get a manager by team ID
   * @param teamId The ID of the team
   */
  getManagerByTeamId(teamId: string): Promise<Manager | null>;

  updateMagicSpongeCount(managerId: string, count: number): Promise<void>;
  updateCardAppealCount(managerId: string, count: number): Promise<void>;

  findIdleManagers(gameworldId: string): Promise<Manager[]>;
}
