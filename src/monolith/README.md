# Jumpers for Goalposts - Monolith API

This directory contains the Express.js monolith version of the Jumpers for Goalposts API, designed to replace the serverless Lambda functions for better performance and lower cold start times.

## Architecture

The monolith reuses the existing business logic from Lambda functions by:

1. **Adapter Pattern**: `adaptLambdaHandler.ts` converts Express requests/responses to Lambda event format
2. **Middleware Reuse**: Database, auth, and logging middleware adapted for Express
3. **Route Mapping**: Express routes mirror the original API Gateway routes
4. **Persistent Connections**: Database connections are established once at startup

## Key Benefits

- **Faster Response Times**: No cold starts, persistent database connections
- **Lower Costs**: Single t4g.micro EC2 instance vs multiple Lambda invocations
- **Minimal Code Changes**: Reuses existing Lambda handlers and business logic
- **Gradual Migration**: Background jobs (SQS, scheduled tasks) remain as Lambdas

## Files Structure

```
src/monolith/
├── app.ts                 # Main Express application setup
├── server.ts             # Server entry point with environment setup
├── test-server.ts        # Simple test server for verification
├── middleware/           # Express middleware
│   ├── auth.ts          # JWT authentication middleware
│   ├── database.ts      # Database connection and repository setup
│   ├── errorHandler.ts  # Global error handling
│   └── requestLogger.ts # Request/response logging
├── routes/              # Route handlers organized by domain
│   ├── index.ts         # Main router setup
│   ├── manager.ts       # Manager-related routes
│   ├── team.ts          # Team-related routes
│   ├── player.ts        # Player-related routes
│   ├── transfer.ts      # Transfer-related routes
│   ├── training.ts      # Training-related routes
│   ├── scouting.ts      # Scouting-related routes
│   ├── fixture.ts       # Fixture-related routes
│   ├── auth.ts          # Authentication routes
│   ├── admin.ts         # Admin routes
│   ├── iap.ts           # In-app purchase routes
│   └── inbox.ts         # Inbox routes
└── utils/
    └── adaptLambdaHandler.ts # Lambda-to-Express adapter
```

## Environment Variables

Required environment variables:

```bash
NODE_ENV=production
PORT=3000
STAGE=stage
DATABASE_URL=postgresql://user:pass@host:port/db
COGNITO_USER_POOL_ID=your-user-pool-id
COGNITO_USER_POOL_CLIENT_ID=your-client-id
```

## Development

### Local Development

1. **Test Server** (no database required):
   ```bash
   npm run monolith:test
   ```

2. **Full Development Server** (requires database):
   ```bash
   npm run monolith:dev
   ```

### Building for Production

```bash
npm run build
npm run monolith:start
```

## Deployment

### Infrastructure

The EC2 infrastructure is managed by Pulumi:

```bash
cd infrastructure
pulumi up
```

This creates:
- t4g.micro EC2 instance (ARM-based, cost-effective)
- Application Load Balancer with health checks
- Security groups for EC2 and ALB
- SSH key pair for access

### Application Deployment

Use the deployment scripts:

**Linux/Mac:**
```bash
./scripts/deploy-monolith.sh stage <instance-ip>
```

**Windows:**
```bash
scripts\deploy-monolith.bat stage <instance-ip>
```

### Manual Deployment Steps

1. Build the application: `npm run build`
2. Create deployment package with production dependencies
3. Upload to EC2 instance
4. Configure environment variables
5. Start systemd service

## Route Mapping

The monolith maintains the same API endpoints as the serverless version:

| Method | Path | Handler |
|--------|------|---------|
| GET | `/ping` | Health check |
| GET | `/manager` | Get current manager |
| GET | `/manager/:managerId` | Get specific manager |
| PUT | `/manager/name` | Update manager name |
| GET | `/:gameworldId/leagues` | Get leagues |
| GET | `/:gameworldId/team/:teamId` | Get team |
| POST | `/transfer/offer` | Submit transfer offer |
| ... | ... | ... |

## Authentication

- Uses the same Cognito JWT verification as the Lambda authorizer
- Public routes (ping, commentary, etc.) bypass authentication
- Bearer token required in Authorization header for protected routes

## Database

- Persistent MikroORM connection established at startup
- Same repository pattern as Lambda functions
- Connection pooling for better performance

## Monitoring

- Request/response logging via custom middleware
- Health check endpoint at `/ping` for load balancer
- Systemd service management on EC2

## Migration Strategy

1. **Phase 1**: Deploy monolith alongside existing serverless API
2. **Phase 2**: Route traffic to monolith via load balancer
3. **Phase 3**: Keep background jobs as Lambdas (SQS, scheduled tasks)
4. **Phase 4**: Gradually migrate remaining Lambda functions if needed

## Performance Considerations

- Database connection pooling
- Persistent connections eliminate cold starts
- ARM-based EC2 instance for cost efficiency
- Application Load Balancer for high availability
- Health checks ensure service reliability
