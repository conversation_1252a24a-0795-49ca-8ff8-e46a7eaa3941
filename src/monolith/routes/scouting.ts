import { adapt<PERSON><PERSON>bdaHand<PERSON> } from '@/monolith/utils/adaptLambdaHandler.js';
import { Router } from 'express';

// Import the existing lambda handlers
import { main as requestScoutingHandler } from '@/functions/scouting/requestScouting.js';
import { main as requestSuperScoutingHandler } from '@/functions/scouting/requestSuperScouting.js';

const router = Router();

// POST /scouting/request - Request scouting
router.post('/request', adapt<PERSON><PERSON>b<PERSON><PERSON><PERSON><PERSON>(requestScoutingHandler));

// POST /scouting/request/super - Request super scouting
router.post('/request/super', adapt<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(requestSuperScoutingHandler));

// Note: GET /:gameworldId/team/:teamId/scouted-players is handled in the main routes
// since it includes gameworld/team parameters

export { router as scoutingRoutes };
