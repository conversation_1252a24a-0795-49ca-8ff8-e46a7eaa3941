import { adaptLambdaHandler } from '@/monolith/utils/adaptLambdaHandler.js';
import { Router } from 'express';

// Import the existing lambda handlers
import { main as getRewardsHandler } from '@/functions/manager/dailyReward.js';
import { main as getManagerHandler } from '@/functions/manager/getManager.js';
import { main as unsubscribeHandler } from '@/functions/manager/unsubscribe.js';
import { main as updateManagerNameHandler } from '@/functions/manager/updateManagerName.js';
import { main as updateNotificationPreferencesHandler } from '@/functions/manager/updateNotificationPreferences.js';

const router = Router();

// GET /manager - Get current manager
// GET /manager/:managerId - Get specific manager
router.get('/', adaptLambdaHandler(getManagerHandler));

// GET /manager/rewards - Get manager rewards
router.get('/rewards', adaptLambda<PERSON>and<PERSON>(getRewardsHandler));

// PUT /manager/name - Update manager name
router.put('/name', adaptLambda<PERSON>andler(updateManagerNameHandler));

// PUT /manager/notification-preferences - Update notification preferences
router.put('/notification-preferences', adaptLambdaHandler(updateNotificationPreferencesHandler));

// GET /manager/unsubscribe - Unsubscribe from emails (public route)
router.get('/unsubscribe', adaptLambdaHandler(unsubscribeHandler));

router.get('/:managerId', adaptLambdaHandler(getManagerHandler));
export { router as managerRoutes };
