import { adaptLambdaHandler } from '@/monolith/utils/adaptLambdaHandler.js';
import { Router } from 'express';

// Import the existing lambda handlers
import { main as getIAPRewardsHandler } from '@/functions/iap/getIapRewards.js';
import { main as iapWebhookHandler } from '@/functions/iap/webhook.js';

const router = Router();

// POST /iap/webhook - IAP webhook (public route)
router.post('/webhook', adaptLambda<PERSON>andler(iapWebhookHandler));

// GET /iap/rewards - Get IAP rewards
router.get('/rewards', adapt<PERSON><PERSON>bdaHand<PERSON>(getIAPRewardsHandler));

export { router as iapRoutes };
