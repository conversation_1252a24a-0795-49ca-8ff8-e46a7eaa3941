import { adaptLambdaHandler } from '@/monolith/utils/adaptLambdaHandler.js';
import { Router } from 'express';

// Import the existing lambda handlers
import { main as getInboxMessagesHandler } from '@/functions/inbox/getInboxMessages.js';

const router = Router();

// GET /inbox/messages - Get inbox messages
router.get('/messages', adaptLambdaHandler(getInboxMessagesHandler));

export { router as inboxRoutes };
