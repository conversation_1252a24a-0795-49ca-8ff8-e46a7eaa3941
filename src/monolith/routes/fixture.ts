import { adaptLambdaHandler } from '@/monolith/utils/adaptLambdaHandler.js';
import { Router } from 'express';

// Import the existing lambda handlers
import { main as getFixtureHandler } from '@/functions/fixtures/getFixture.js';
import { main as getFixturesHandler } from '@/functions/fixtures/getLeagueFixtures.js';
import { main as getCommentaryHandler } from '@/functions/fixtures/getLocalisation.js';
import { main as getTeamFixturesHandler } from '@/functions/fixtures/getTeamFixtures.js';

const router = Router();

// GET /:gameworldId/league/:leagueId/fixtures - Get league fixtures
router.get('/:gameworldId/league/:leagueId/fixtures', adaptLambdaHandler(getFixturesHandler));

// GET /:gameworldId/league/:leagueId/fixtures/:teamId - Get team fixtures
router.get(
  '/:gameworldId/league/:leagueId/fixtures/:teamId',
  adapt<PERSON>ambda<PERSON><PERSON><PERSON>(getTeamFixturesHandler)
);

// GET /:gameworldId/league/:leagueId/fixture/:fixtureId - Get specific fixture
router.get(
  '/:gameworldId/league/:leagueId/fixture/:fixtureId',
  adaptLambdaHandler(getFixtureHandler)
);

// GET /commentary - Get commentary (public route)
router.get('/commentary', adaptLambdaHandler(getCommentaryHandler));

export { router as fixtureRoutes };
