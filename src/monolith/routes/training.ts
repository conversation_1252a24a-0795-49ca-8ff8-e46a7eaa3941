import { adaptLambdaHandler } from '@/monolith/utils/adaptLambdaHandler.js';
import { Router } from 'express';

// Import the existing lambda handlers
import { main as assignTrainingSlotHandler } from '@/functions/training/assignTrainingSlot.js';
import { main as getTrainingSlotsHandler } from '@/functions/training/getTrainingSlots.js';
import { main as unlockTrainingSlotHandler } from '@/functions/training/unlockTrainingSlot.js';

const router = Router();

// GET /training/slots - Get training slots
router.get('/slots', adaptLambdaHandler(getTrainingSlotsHandler));

// POST /training/slots/:slotId - Assign training slot
router.post('/slots/:slotId', adaptLambdaHandler(assignTrainingSlotHandler));

// POST /training/slots/unlock - Unlock training slot
router.post('/slots/unlock', adapt<PERSON><PERSON>bda<PERSON>and<PERSON>(unlockTrainingSlotHandler));

export { router as trainingRoutes };
