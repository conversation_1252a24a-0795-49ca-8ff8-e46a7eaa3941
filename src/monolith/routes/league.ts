import { adaptLambdaHandler } from '@/monolith/utils/adaptLambdaHandler.js';
import { Router } from 'express';

// Import the existing lambda handlers
import { main as getLeagueHandler } from '@/functions/league/getLeague.js';
import { main as getLeaguesHandler } from '@/functions/league/getLeagues.js';

const router = Router();

// GET /:gameworldId/leagues - Get leagues for gameworld
router.get('/:gameworldId/leagues', adaptLambdaHandler(getLeaguesHandler));

// GET /:gameworldId/leagues/:leagueId - Get specific league
router.get('/:gameworldId/leagues/:leagueId', adaptLambdaHandler(getLeagueHandler));

export { router as leagueRoutes };
