import { adaptLambdaHandler } from '@/monolith/utils/adaptLambdaHandler.js';
import { Router } from 'express';

// Import the existing lambda handlers
import { main as getMyBidTransferListPlayersHandler } from '@/functions/player/getMyBidTransferListPlayers.js';
import { main as getPlayerStatsHandler } from '@/functions/player/getPlayerStats.js';
import { main as getTransferListPlayersHandler } from '@/functions/player/getTransferListPlayers.js';
import { main as useMagicSpongeHandler } from '@/functions/player/useMagicSponge.js';
import { main as useRedCardAppealHandler } from '@/functions/player/useRedCardAppeal.js';

const router = Router();

// GET /:gameworldId/players/transfer-list - Get transfer list players
router.get(
  '/:gameworldId/players/transfer-list',
  adaptLambdaHandler(getTransferListPlayersHandler)
);

// GET /:gameworldId/players/my-bids - Get my bid transfer list players
router.get('/:gameworldId/players/my-bids', adaptLambdaHandler(getMyBidTransferListPlayersHandler));

// GET /:gameworldId/player/:playerId - Get player stats
router.get('/:gameworldId/player/:playerId', adaptLambdaHandler(getPlayerStatsHandler));

// POST /:gameworldId/players/:playerId/magic-sponge - Use magic sponge
router.post(
  '/:gameworldId/players/:playerId/magic-sponge',
  adaptLambdaHandler(useMagicSpongeHandler)
);

// POST /:gameworldId/players/:playerId/red-card-appeal - Use red card appeal
router.post(
  '/:gameworldId/players/:playerId/red-card-appeal',
  adaptLambdaHandler(useRedCardAppealHandler)
);

export { router as playerRoutes };
