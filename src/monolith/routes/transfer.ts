import { adaptLambdaHand<PERSON> } from '@/monolith/utils/adaptLambdaHandler.js';
import { Router } from 'express';

// Import the existing lambda handlers
import { main as acceptTransferRequestHandler } from '@/functions/transfers/acceptTransferRequest.js';
import { main as cancelTransferRequestHandler } from '@/functions/transfers/cancelTransferRequest.js';
import { main as myActiveTransfersHandler } from '@/functions/transfers/myActiveTransfers.js';
import { main as releasePlayerHandler } from '@/functions/transfers/releasePlayer.js';
import { main as submitBidHandler } from '@/functions/transfers/submitBid.js';
import { main as submitTransferOfferHandler } from '@/functions/transfers/submitOffer.js';
import { main as toggleTransferListStatusHandler } from '@/functions/transfers/toggleTransferListStatus.js';

const router = Router();

// POST /transfer/offer - Submit transfer offer
router.post('/offer', adaptLambdaHandler(submitTransferOfferHandler));

// POST /transfer/bid - Submit bid
router.post('/bid', adaptLambdaHandler(submitBidHandler));

// GET /transfer/my-active - Get my active transfers
router.get('/my-active', adaptLambdaHandler(myActiveTransfersHandler));

// POST /transfer/accept - Accept transfer request
router.post('/accept', adaptLambdaHandler(acceptTransferRequestHandler));

// POST /transfer/cancel - Cancel transfer request
router.post('/cancel', adaptLambdaHandler(cancelTransferRequestHandler));

// POST /transfer/release-player - Release player
router.post('/release-player', adaptLambdaHandler(releasePlayerHandler));

// POST /transfer/toggle - Toggle transfer list status
router.post('/toggle', adaptLambdaHandler(toggleTransferListStatusHandler));

export { router as transferRoutes };
