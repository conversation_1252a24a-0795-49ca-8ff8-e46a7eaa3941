/**
 * Test routes for Sentry integration
 * These routes help verify that <PERSON><PERSON> is working correctly
 * Remove or disable in production
 */

import { Router } from 'express';
import { captureException, captureMessage } from '../utils/sentry.js';

const router = Router();

// Test route to trigger an unhandled error (should be captured by Sentry)
router.post('/test-error', (req, res) => {
  if (process.env.NODE_ENV === 'production') {
    return res.status(404).json({ error: 'Not found' });
  }

  // This will be captured by <PERSON><PERSON>
  throw new Error('Test error for Sentry integration');
});

// Test route to trigger a handled error (should NOT be captured by Sentry)
router.post('/test-handled-error', (req, res) => {
  if (process.env.NODE_ENV === 'production') {
    return res.status(404).json({ error: 'Not found' });
  }

  // This should NOT be captured by Sentry (4xx error)
  return res.status(400).json({ 
    error: 'This is a handled validation error - should not appear in Sentry' 
  });
});

// Test route to manually capture an exception
router.post('/test-manual-capture', (req, res) => {
  if (process.env.NODE_ENV === 'production') {
    return res.status(404).json({ error: 'Not found' });
  }

  try {
    // Simulate an error
    const error = new Error('Manually captured test error');
    captureException(error, {
      testContext: 'manual-capture-test',
      userId: req.user?.userId,
      timestamp: new Date().toISOString(),
    });

    res.json({ 
      message: 'Error manually captured and sent to Sentry',
      sentryEventId: 'check-sentry-dashboard'
    });
  } catch (error) {
    res.status(500).json({ error: 'Failed to capture error' });
  }
});

// Test route to capture a message
router.post('/test-message', (req, res) => {
  if (process.env.NODE_ENV === 'production') {
    return res.status(404).json({ error: 'Not found' });
  }

  captureMessage('Test message from monolith', 'info', {
    testType: 'message-capture',
    userId: req.user?.userId,
    requestPath: req.path,
  });

  res.json({ 
    message: 'Test message sent to Sentry',
    note: 'Check your Sentry dashboard for the message'
  });
});

export { router as testRoutes };
