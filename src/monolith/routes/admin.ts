import { adapt<PERSON>ambdaHand<PERSON> } from '@/monolith/utils/adaptLambdaHandler.js';
import { Router } from 'express';

// Import the existing lambda handlers
import { main as feedbackHandler } from '@/functions/admin/feedback.js';
import { main as versionCheckHandler } from '@/functions/admin/versionCheck.js';
import { main as ssvHandler } from '@/functions/admob/ssv.js';
import { main as countAvailableTeamsHandler } from '@/functions/gameworld/countAvailableTeams.js';

const router = Router();

// POST /feedback - Submit feedback
router.post('/feedback', adaptLambdaHandler(feedbackHandler));

// POST /appversion - Version check
router.post('/appversion', adaptLambdaHandler(versionCheckHandler));

// GET /available-teams - Count available teams (public route)
router.get('/available-teams', adapt<PERSON>ambda<PERSON>and<PERSON>(countAvailableTeamsHandler));

// GET /admob/ssv - AdMob SSV (public route)
router.get('/admob/ssv', adapt<PERSON><PERSON>bda<PERSON>and<PERSON>(ssvHandler));

export { router as adminRoutes };
