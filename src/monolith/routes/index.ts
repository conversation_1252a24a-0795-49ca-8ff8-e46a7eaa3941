import { adaptLambdaHandler } from '@/monolith/utils/adaptLambdaHandler.js';
import { Router } from 'express';
import { adminRoutes } from './admin.js';
import { authRoutes } from './auth.js';
import { fixtureRoutes } from './fixture.js';
import { iapRoutes } from './iap.js';
import { inboxRoutes } from './inbox.js';
import { leagueRoutes } from './league.js';
import { managerRoutes } from './manager.js';
import { playerRoutes } from './player.js';
import { scoutingRoutes } from './scouting.js';
import { teamRoutes } from './team.js';
import { testRoutes } from './test.js';
import { trainingRoutes } from './training.js';
import { transferRoutes } from './transfer.js';

// Import team handlers for gameworld-specific routes
import { main as getScoutedPlayersHandler } from '@/functions/scouting/getScoutedPlayers.js';
import { main as getTeamHandler } from '@/functions/team/getTeam.js';
import { main as getTransactionsHandler } from '@/functions/team/getTransactions.js';
import { main as updateTeamOrderHandler } from '@/functions/team/updateTeamOrder.js';
import { main as upgradeTrainingHandler } from '@/functions/team/upgradeTraining.js';

export function createApiRoutes() {
  const router = Router();

  // Mount route modules
  router.use('/manager', managerRoutes);
  router.use('/team', teamRoutes);
  router.use('/', leagueRoutes); // League routes include gameworld prefix
  router.use('/', playerRoutes); // Player routes include gameworld prefix
  router.use('/transfer', transferRoutes);
  router.use('/training', trainingRoutes);
  router.use('/scouting', scoutingRoutes);
  router.use('/', fixtureRoutes); // Fixture routes include gameworld prefix
  router.use('/auth', authRoutes);
  router.use('/', adminRoutes); // Admin routes are at root level
  router.use('/iap', iapRoutes);
  router.use('/inbox', inboxRoutes);

  // Test routes for Sentry integration (development only)
  if (process.env.NODE_ENV !== 'production') {
    router.use('/test', testRoutes);
  }

  // Add the gameworld-specific team routes that were missing
  router.get('/:gameworldId/team/:teamId', adaptLambdaHandler(getTeamHandler));
  router.post('/:gameworldId/team/:teamId', adaptLambdaHandler(updateTeamOrderHandler));
  router.post(
    '/:gameworldId/team/:teamId/upgrade-training',
    adaptLambdaHandler(upgradeTrainingHandler)
  );
  router.get('/:gameworldId/team/:teamId/transactions', adaptLambdaHandler(getTransactionsHandler));

  // Add scouted players route
  router.get(
    '/:gameworldId/team/:teamId/scouted-players',
    adaptLambdaHandler(getScoutedPlayersHandler)
  );

  return router;
}
