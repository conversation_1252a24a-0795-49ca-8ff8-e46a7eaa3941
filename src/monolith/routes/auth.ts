import { adaptLambdaHandler } from '@/monolith/utils/adaptLambdaHandler.js';
import { Router } from 'express';

// Import the existing lambda handlers
import { main as createAndAssignNewManagerHandler } from '@/functions/auth/createAndAssignNewManager.js';
import { main as createGuestUserHandler } from '@/functions/auth/createGuestUser.js';
import { main as getMigrationConflictsHandler } from '@/functions/auth/getMigrationConflicts.js';
import { main as resolveMigrationHandler } from '@/functions/auth/resolveMigration.js';

const router = Router();

// POST /auth/guest - Create guest user
router.post('/guest', adaptLambdaHandler(createGuestUserHandler));

// GET /auth/migration/conflicts - Get migration conflicts
router.get('/migration/conflicts', adaptLambdaHandler(getMigrationConflictsHandler));

// POST /auth/migration/resolve - Resolve migration
router.post('/migration/resolve', adapt<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(resolveMigrationHandler));

// POST /auth/signup - Create and assign new manager
router.post('/signup', adaptLambdaHandler(createAndAssignNewManagerHandler));

// New: /auth/login - Authenticate user with Cognito and return tokens
router.post('/login', async (req, res) => {
  try {
    const { email, password } = req.body || {};
    if (!email || !password) {
      return res.status(400).json({ error: 'email and password are required' });
    }

    const clientId = process.env.COGNITO_USER_POOL_CLIENT_ID;
    if (!clientId) {
      return res.status(500).json({ error: 'Cognito client id not configured' });
    }

    const region = 'us-east-2';

    try {
      const { CognitoIdentityProviderClient, InitiateAuthCommand } = await import(
        '@aws-sdk/client-cognito-identity-provider'
      );
      const cognito = new CognitoIdentityProviderClient({ region });

      const command = new InitiateAuthCommand({
        AuthFlow: 'USER_PASSWORD_AUTH',
        ClientId: clientId,
        AuthParameters: {
          USERNAME: email,
          PASSWORD: password,
        },
      });

      const response = await cognito.send(command);

      if (response.ChallengeName) {
        return res
          .status(403)
          .json({ error: 'Additional challenge required', challenge: response.ChallengeName });
      }

      if (!response.AuthenticationResult) {
        return res.status(500).json({ error: 'Authentication failed' });
      }

      const { AccessToken, IdToken, RefreshToken, ExpiresIn, TokenType } =
        response.AuthenticationResult;

      return res.json({
        accessToken: AccessToken,
        idToken: IdToken,
        refreshToken: RefreshToken,
        expiresIn: ExpiresIn,
        tokenType: TokenType,
      });
    } catch (err: any) {
      const code = err?.name || err?.code;
      switch (code) {
        case 'NotAuthorizedException':
          return res.status(401).json({ error: 'Invalid credentials' });
        case 'UserNotFoundException':
          return res.status(401).json({ error: 'User not found' });
        case 'UserNotConfirmedException':
          return res.status(403).json({ error: 'User not confirmed' });
        default:
          return res.status(500).json({ error: 'Login failed', detail: code });
      }
    }
  } catch (e) {
    return res.status(500).json({ error: 'Internal server error' });
  }
});

export { router as authRoutes };
