import { adaptLambdaHandler } from '@/monolith/utils/adaptLambdaHandler.js';
import { Router } from 'express';

// Import the existing lambda handlers
import { main as updateTeamNameHandler } from '@/functions/team/updateTeamName.js';

const router = Router();

// PUT /team/name - Update team name
router.put('/name', adaptLambdaHandler(updateTeamNameHandler));

// These routes include gameworld/team parameters, so they're handled differently
// GET /:gameworldId/team/:teamId - Get team (handled in main routes)
// POST /:gameworldId/team/:teamId - Update team order (handled in main routes)
// POST /:gameworldId/team/:teamId/upgrade-training - Upgrade training (handled in main routes)
// GET /:gameworldId/team/:teamId/transactions - Get transactions (handled in main routes)

export { router as teamRoutes };
