/**
 * Structured logger for the monolith application
 * Provides request correlation and function context similar to Lambda CloudWatch logs
 */

interface LogLevel {
  DEBUG: number;
  INFO: number;
  WARN: number;
  ERROR: number;
}

const LOG_LEVELS: LogLevel = {
  DEBUG: 0,
  INFO: 1,
  WARN: 2,
  ERROR: 3,
};

interface LogContext {
  requestId?: string;
  userId?: string;
  functionName?: string;
  path?: string;
  method?: string;
  [key: string]: any;
}

class MonolithLogger {
  private logLevel: number;
  private context: LogContext = {};

  constructor() {
    const level = process.env.LOG_LEVEL?.toUpperCase() || 'INFO';
    this.logLevel = LOG_LEVELS[level as keyof LogLevel] ?? LOG_LEVELS.INFO;
  }

  // Set context for the current request/operation
  setContext(context: LogContext): void {
    this.context = { ...this.context, ...context };
  }

  // Clear context (useful for request cleanup)
  clearContext(): void {
    this.context = {};
  }

  // Create a child logger with additional context
  child(additionalContext: LogContext): MonolithLogger {
    const childLogger = new MonolithLogger();
    childLogger.context = { ...this.context, ...additionalContext };
    childLogger.logLevel = this.logLevel;
    return childLogger;
  }

  private formatMessage(level: string, message: string, meta?: any): string {
    const timestamp = new Date().toISOString();
    
    // Create structured log entry
    const logEntry = {
      timestamp,
      level,
      message,
      ...this.context,
      ...(meta && Object.keys(meta).length > 0 ? meta : {}),
    };

    // For development, use pretty format
    if (process.env.NODE_ENV === 'development') {
      const contextStr = this.context.requestId ? `[${this.context.requestId}]` : '';
      const functionStr = this.context.functionName ? `[${this.context.functionName}]` : '';
      const baseLog = `[${timestamp}] ${level}${contextStr}${functionStr}: ${message}`;
      
      if (meta && Object.keys(meta).length > 0) {
        return `${baseLog} ${JSON.stringify(meta, null, 2)}`;
      }
      return baseLog;
    }

    // For production, use JSON format for easier parsing
    return JSON.stringify(logEntry);
  }

  private shouldLog(level: number): boolean {
    return level >= this.logLevel;
  }

  debug(message: string, meta?: any): void {
    if (this.shouldLog(LOG_LEVELS.DEBUG)) {
      console.log(this.formatMessage('DEBUG', message, meta));
    }
  }

  info(message: string, meta?: any): void {
    if (this.shouldLog(LOG_LEVELS.INFO)) {
      console.log(this.formatMessage('INFO', message, meta));
    }
  }

  warn(message: string, meta?: any): void {
    if (this.shouldLog(LOG_LEVELS.WARN)) {
      console.warn(this.formatMessage('WARN', message, meta));
    }
  }

  error(message: string, meta?: any): void {
    if (this.shouldLog(LOG_LEVELS.ERROR)) {
      console.error(this.formatMessage('ERROR', message, meta));
    }
  }
}

export const logger = new MonolithLogger();
