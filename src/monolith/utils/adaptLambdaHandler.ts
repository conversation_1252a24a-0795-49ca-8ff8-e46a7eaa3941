import { APIGatewayProxyEvent, APIGatewayProxyResult, Context } from 'aws-lambda';
import { NextFunction, Request, Response } from 'express';

/**
 * Adapts a Lambda handler to work with Express.js
 * This allows us to reuse existing lambda functions without modification
 */
export function adaptLambdaHandler(lambdaHandler: any) {
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      // Add function context to logger
      const functionName = lambdaHandler.name || 'unknown-handler';
      req.logger.setContext({ functionName });

      req.logger.debug('Calling lambda handler', { functionName });

      // Convert Express request to Lambda event format
      const event = expressToLambdaEvent(req);

      // Create a mock Lambda context
      const context = createMockContext(req);

      // Call the lambda handler
      const result = await lambdaHandler(event, context);

      req.logger.debug('Lambda handler completed', {
        functionName,
        statusCode: result.statusCode,
      });

      // Convert Lambda result to Express response
      lambdaResultToExpressResponse(result, res);
    } catch (error: any) {
      req.logger.error('Error in lambda handler adapter', {
        error: error.message,
        stack: error.stack,
      });
      next(error);
    }
  };
}

function expressToLambdaEvent(req: Request): APIGatewayProxyEvent {
  // Extract path parameters from Express params
  const pathParameters = req.params && Object.keys(req.params).length > 0 ? req.params : null;

  // Extract query parameters
  const queryStringParameters =
    req.query && Object.keys(req.query).length > 0
      ? Object.fromEntries(
          Object.entries(req.query).map(([key, value]) => {
            // Handle different value types from ParsedQs
            if (Array.isArray(value)) {
              return [key, value[0]?.toString()]; // Take first element and convert to string
            } else if (value !== undefined) {
              return [key, value.toString()]; // Convert to string
            } else {
              return [key, undefined]; // Keep undefined as is
            }
          })
        )
      : null;

  return {
    resource: req.route?.path || req.path,
    path: req.path,
    httpMethod: req.method,
    headers: req.headers as { [name: string]: string },
    multiValueHeaders: {},
    queryStringParameters: queryStringParameters,
    multiValueQueryStringParameters: null,
    pathParameters,
    stageVariables: null,
    requestContext: {
      resourceId: '',
      resourcePath: req.path,
      httpMethod: req.method,
      requestId: Math.random().toString(36),
      path: req.path,
      accountId: '',
      apiId: '',
      stage: process.env.STAGE || 'dev',
      requestTime: new Date().toISOString(),
      requestTimeEpoch: Date.now(),
      identity: {
        cognitoIdentityPoolId: null,
        accountId: null,
        cognitoIdentityId: null,
        caller: null,
        sourceIp: req.ip || '',
        principalOrgId: null,
        accessKey: null,
        cognitoAuthenticationType: null,
        cognitoAuthenticationProvider: null,
        userArn: null,
        userAgent: req.get('User-Agent') || '',
        user: null,
        apiKey: null,
        apiKeyId: null,
        clientCert: null,
      },
      protocol: 'HTTP/1.1',
      authorizer: req.user
        ? {
            // Mirror API Gateway authorizer context set by jwtAuth lambda
            principalId: req.user.userId,
            userId: req.user.userId,
            userType: (req.user as any).userType,
            username: (req.user as any).username,
            email: req.user.email || '',
            // Keep original minimal claims object for any legacy use
            claims: {
              sub: req.user.userId,
              email: req.user.email || '',
              username: (req.user as any).username,
            },
          }
        : undefined,
    },
    body: req.body,
    isBase64Encoded: false,
    // Add repositories to context (this is how our lambda middleware works)
    // @ts-ignore
    context: {
      repositories: req.repositories,
    } as any,
  };
}

function createMockContext(req: Request): Context {
  return {
    callbackWaitsForEmptyEventLoop: false,
    functionName: 'monolith-handler',
    functionVersion: '1',
    invokedFunctionArn: '',
    memoryLimitInMB: '512',
    awsRequestId: Math.random().toString(36),
    logGroupName: '',
    logStreamName: '',
    getRemainingTimeInMillis: () => 30000,
    done: () => {},
    fail: () => {},
    succeed: () => {},
    // Add repositories to context
    repositories: req.repositories,
  } as any;
}

function lambdaResultToExpressResponse(result: APIGatewayProxyResult, res: Response) {
  // Set status code
  res.status(result.statusCode);

  // Set headers
  if (result.headers) {
    Object.entries(result.headers).forEach(([key, value]) => {
      if (value) {
        res.set(key, value.toString());
      }
    });
  }

  // Set multi-value headers
  if (result.multiValueHeaders) {
    Object.entries(result.multiValueHeaders).forEach(([key, values]) => {
      if (values) {
        values.forEach((value) => res.append(key, value.toString()));
      }
    });
  }

  // Send body
  if (result.body) {
    if (result.isBase64Encoded) {
      res.send(Buffer.from(result.body, 'base64'));
    } else {
      // Try to parse as JSON for proper content-type
      try {
        const parsed = JSON.parse(result.body);
        res.json(parsed);
      } catch {
        res.send(result.body);
      }
    }
  } else {
    res.end();
  }
}
