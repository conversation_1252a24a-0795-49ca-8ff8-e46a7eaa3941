/**
 * Sentry configuration for the monolith application
 * Captures unhandled errors while filtering out expected HTTP error responses
 */

import * as Sentry from '@sentry/node';
import { nodeProfilingIntegration } from '@sentry/profiling-node';

// Initialize Sentry
export function initSentry() {
  // Skip initialization if no DSN is provided
  const dsn =
    process.env.SENTRY_DSN ||
    'https://<EMAIL>/4509911419125840';

  if (!dsn) {
    console.log('Sentry DSN not provided, skipping initialization');
    return;
  }

  Sentry.init({
    dsn,

    // Environment configuration
    environment: process.env.NODE_ENV || 'development',

    // Setting this option to true will send default PII data to Sentry
    sendDefaultPii: true,

    // Performance monitoring
    tracesSampleRate: process.env.NODE_ENV === 'production' ? 0.1 : 1.0, // 10% in prod, 100% in dev

    // Profiling (optional)
    profilesSampleRate: process.env.NODE_ENV === 'production' ? 0.1 : 1.0,
    integrations: [nodeProfilingIntegration()],

    // Filter out expected errors
    beforeSend(event, hint) {
      const error = hint.originalException;

      // Don't send expected HTTP errors (4xx, 5xx responses that are handled)
      if (error && typeof error === 'object' && 'statusCode' in error) {
        const statusCode = (error as any).statusCode;

        // Filter out client errors (4xx) - these are usually validation errors, auth failures, etc.
        if (statusCode >= 400 && statusCode < 500) {
          return null; // Don't send to Sentry
        }

        // Filter out handled server errors (5xx with specific handling)
        if (statusCode >= 500 && (error as any).handled === true) {
          return null; // Don't send to Sentry
        }
      }

      // Filter out specific error types that are expected
      if (error && error instanceof Error) {
        const errorMessage = error.message.toLowerCase();

        // Common expected errors
        const expectedErrors = [
          'validation failed',
          'unauthorized',
          'forbidden',
          'not found',
          'bad request',
          'invalid token',
          'expired token',
          'user not found',
          'invalid credentials',
        ];

        if (expectedErrors.some((expected) => errorMessage.includes(expected))) {
          return null; // Don't send to Sentry
        }
      }

      // Send everything else to Sentry
      return event;
    },

    // Add additional context
    initialScope: {
      tags: {
        component: 'monolith',
        service: 'jumpers-for-goalposts-api',
      },
    },
  });
}

// Helper function to capture exceptions with context
export function captureException(error: Error, context?: Record<string, any>) {
  Sentry.withScope((scope) => {
    if (context) {
      Object.keys(context).forEach((key) => {
        scope.setContext(key, context[key]);
      });
    }
    Sentry.captureException(error);
  });
}

// Helper function to capture messages
export function captureMessage(
  message: string,
  level: Sentry.SeverityLevel = 'info',
  context?: Record<string, any>
) {
  Sentry.withScope((scope) => {
    if (context) {
      Object.keys(context).forEach((key) => {
        scope.setContext(key, context[key]);
      });
    }
    Sentry.captureMessage(message, level);
  });
}

// Helper function to add user context to Sentry
export function setUserContext(user: { id?: string; email?: string; [key: string]: any }) {
  Sentry.setUser(user);
}

// Helper function to add request context
export function setRequestContext(req: any) {
  Sentry.withScope((scope) => {
    scope.setContext('request', {
      method: req.method,
      url: req.url,
      path: req.path,
      query: req.query,
      headers: {
        'user-agent': req.get('User-Agent'),
        'content-type': req.get('Content-Type'),
        authorization: req.get('Authorization') ? '[REDACTED]' : undefined,
      },
      ip: req.ip,
      requestId: req.requestId,
    });

    // Add user context if available
    if (req.user) {
      scope.setUser({
        id: req.user.userId,
        email: req.user.email,
      });
    }
  });
}

// Express error handler middleware for Sentry
export function sentryErrorHandler() {
  return Sentry.expressErrorHandler({
    shouldHandleError(error) {
      // Only handle unhandled errors (not expected HTTP errors)
      return !(error.statusCode && Number(error.statusCode) < 500);
      // Handle server errors and unhandled exceptions
    },
  });
}

export { Sentry };
