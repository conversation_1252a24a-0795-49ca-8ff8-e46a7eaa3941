import {
  getFixtureRepository,
  getGameworldRepository,
  getInboxRepository,
  getLeagueRepository,
  getManagerRepository,
  getPlayerRepository,
  getPurchaseRepository,
  getScoutingRepository,
  getScoutingRequestRepository,
  getTeamRepository,
  getTrainingRepository,
  getTransferRepository,
} from '@/storage-interface/database-initializer.js';
import type { Repositories } from '@/middleware/database/types.js';

/**
 * Create all repositories for the monolith application
 * This reuses the same repository creation logic as the lambda middleware
 */
export async function createRepositories(): Promise<Repositories> {
  const repositories: Repositories = {
    leagueRepository: await getLeagueRepository(),
    teamRepository: await getTeamRepository(),
    fixtureRepository: await getFixtureRepository(),
    playerRepository: await getPlayerRepository(),
    managerRepository: await getManagerRepository(),
    purchaseRepository: await getPurchaseRepository(),
    scoutingRepository: await getScoutingRepository(),
    scoutingRequestRepository: await getScoutingRequestRepository(),
    gameworldRepository: await getGameworldRepository(),
    transferRepository: await getTransferRepository(),
    inboxRepository: await getInboxRepository(),
    trainingRepository: await getTrainingRepository(),
  };

  return repositories;
}
