import { randomUUID } from 'crypto';
import { NextFunction, Request, Response } from 'express';
import { logger } from '../utils/logger.js';
import { setUserContext } from '../utils/sentry.js';

// Extend Express Request to include logger
declare global {
  namespace Express {
    interface Request {
      logger: typeof logger;
      requestId: string;
    }
  }
}

export function requestLogger(req: Request, res: Response, next: NextFunction) {
  const start = Date.now();
  const requestId = randomUUID();

  // Add request ID to request object
  req.requestId = requestId;

  // Check if this is a health check request
  const isHealthCheck =
    req.path === '/ping' && req.get('User-Agent')?.includes('ELB-HealthChecker');

  // Create a child logger with request context
  req.logger = logger.child({
    requestId,
    method: req.method,
    path: req.originalUrl,
    userAgent: req.get('User-Agent'),
    ip: req.ip,
  });

  // Log incoming request (skip health checks to reduce noise)
  if (!isHealthCheck) {
    req.logger.info('Incoming request');
  }

  // Set user context in Sentry if user is authenticated
  if (req.user) {
    setUserContext({
      id: req.user.userId,
      email: req.user.email,
    });
  }

  // Override res.end to log response
  const originalEnd = res.end;
  res.end = function (chunk?: any, encoding?: any) {
    const duration = Date.now() - start;

    // Log completion (skip health checks, but log if there's an error)
    if (!isHealthCheck || res.statusCode >= 400) {
      req.logger.info('Request completed', {
        statusCode: res.statusCode,
        duration: `${duration}ms`,
        userId: req.user?.userId,
      });
    }

    originalEnd.call(this, chunk, encoding);
  };

  next();
}
