import { logger } from '@/utils/logger.js';
import { NextFunction, Request, Response } from 'express';

// Import the same JWT verification logic used in the lambda authorizer
import { CognitoJwtVerifier } from 'aws-jwt-verify';

let jwtVerifier: any = null;

// Initialize JWT verifier (same logic as lambda authorizer)
function getJwtVerifier() {
  if (!jwtVerifier) {
    const userPoolId = process.env.COGNITO_USER_POOL_ID;
    const clientId = process.env.COGNITO_USER_POOL_CLIENT_ID;

    if (!userPoolId || !clientId) {
      throw new Error('Missing Cognito configuration');
    }

    jwtVerifier = CognitoJwtVerifier.create({
      userPoolId,
      tokenUse: 'access',
      clientId,
    });
  }
  return jwtVerifier;
}

// Routes that don't require authentication
const publicRoutes = [
  '/ping',
  '/commentary',
  '/available-teams',
  '/appversion',
  '/iap/webhook',
  '/manager/unsubscribe',
  '/admob/ssv',
  // Auth routes that must remain public
  '/auth/login',
  '/auth/guest',
  '/auth/signup',
  '/auth/migration/conflicts',
  '/auth/migration/resolve',
];

// Routes that require gameworld parameter but no auth
const publicGameworldRoutes = [/^\/\w+\/leagues$/, /^\/\w+\/leagues\/\w+$/];

function isPublicRoute(path: string): boolean {
  // Check exact matches
  if (publicRoutes.includes(path)) {
    return true;
  }

  // Check regex patterns for gameworld routes
  if (publicGameworldRoutes.some((pattern) => pattern.test(path))) {
    return true;
  }

  // Defensive: treat specific auth endpoints as public even if trailing slash or case variance
  const normalized = path.replace(/\/$/, '').toLowerCase();
  const authPublic = [
    '/auth/login',
    '/auth/guest',
    '/auth/signup',
    '/auth/migration/conflicts',
    '/auth/migration/resolve',
  ];
  return authPublic.includes(normalized);
}

export async function authMiddleware(req: Request, res: Response, next: NextFunction) {
  const path = req.path; // capture early
  let publicMatch = false;
  try {
    publicMatch = isPublicRoute(path);
    logger.debug('Auth middleware check start', {
      path,
      publicMatch,
      method: req.method,
      hasAuthHeader: !!req.headers.authorization,
      contentType: req.headers['content-type'],
    });

    if (publicMatch) {
      logger.debug('Bypassing auth for public route', { path, reason: 'publicRouteList' });
      return next();
    }

    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      logger.debug('Auth header missing/invalid', {
        path,
        authHeaderPresent: !!authHeader,
        sampleHeaders: Object.keys(req.headers).slice(0, 5),
      });
      return res.status(401).json({ error: 'Missing or invalid authorization header' });
    }

    const token = authHeader.substring(7); // Remove 'Bearer ' prefix

    try {
      const verifier = getJwtVerifier();
      const payload = await verifier.verify(token);

      // Extract user information from JWT payload - keep parity with lambda authorizer context
      const username = payload.username || (payload as any)['cognito:username'];
      req.user = {
        userId: payload.sub,
        email: payload.email,
        userType: 'authenticated',
        username,
      } as any; // cast to any to allow extended shape

      logger.debug('User authenticated', {
        path,
        userId: payload.sub,
        username,
        claims: Object.keys(payload),
      });
      next();
    } catch (jwtError) {
      logger.warn('JWT verification failed', {
        path,
        errorName: (jwtError as any)?.name,
        message: (jwtError as any)?.message,
      });
      return res.status(401).json({ error: 'Invalid token' });
    }
  } catch (error) {
    logger.error('Auth middleware error', { path, publicMatch, error });
    return res.status(500).json({ error: 'Internal server error' });
  }
}
