import { Request, Response, NextFunction } from 'express';
import { logger } from '@/utils/logger.js';
import { captureException, setRequestContext } from '../utils/sentry.js';

export function errorHandler(error: any, req: Request, res: Response, next: NextFunction) {
  // Set request context for Sentry
  setRequestContext(req);

  // Log the error
  logger.error('Unhandled error in request', {
    error: error.message,
    stack: error.stack,
    path: req.path,
    method: req.method,
    userId: req.user?.userId,
    requestId: req.requestId,
  });

  // Capture unhandled errors in Sentry (but not expected HTTP errors)
  if (!error.statusCode || error.statusCode >= 500) {
    captureException(error, {
      request: {
        method: req.method,
        path: req.path,
        query: req.query,
        requestId: req.requestId,
      },
      user: req.user ? {
        id: req.user.userId,
        email: req.user.email,
      } : undefined,
    });
  }

  // Don't expose internal errors in production
  const isDevelopment = process.env.NODE_ENV === 'development';

  if (error.statusCode) {
    return res.status(error.statusCode).json({
      error: error.message,
      ...(isDevelopment && { stack: error.stack }),
    });
  }

  // Default to 500 for unhandled errors
  res.status(500).json({
    error: isDevelopment ? error.message : 'Internal server error',
    ...(isDevelopment && { stack: error.stack }),
  });
}
