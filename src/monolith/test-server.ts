#!/usr/bin/env node

/**
 * Simple test script to verify the monolith server can start
 * This bypasses database initialization for quick testing
 */

import express from 'express';
import cors from 'cors';

async function createTestApp() {
  const app = express();

  // Basic middleware
  app.use(cors());
  app.use(express.json());

  // Test routes
  app.get('/ping', (req, res) => {
    res.json({ message: 'pong', timestamp: new Date().toISOString() });
  });

  app.get('/health', (req, res) => {
    res.json({ 
      status: 'ok', 
      service: 'jumpers-for-goalposts-monolith',
      timestamp: new Date().toISOString() 
    });
  });

  // 404 handler
  app.use((req, res) => {
    res.status(404).json({ error: 'Route not found', path: req.path });
  });

  return app;
}

async function startTestServer(port: number = 3000) {
  try {
    const app = await createTestApp();
    
    const server = app.listen(port, '0.0.0.0', () => {
      console.log(`Test server running on port ${port}`);
      console.log(`Try: http://localhost:${port}/ping`);
      console.log(`Try: http://localhost:${port}/health`);
    });

    // Graceful shutdown
    process.on('SIGTERM', () => {
      console.log('SIGTERM received, shutting down gracefully');
      server.close(() => {
        console.log('Server closed');
        process.exit(0);
      });
    });

    process.on('SIGINT', () => {
      console.log('SIGINT received, shutting down gracefully');
      server.close(() => {
        console.log('Server closed');
        process.exit(0);
      });
    });

    return server;
  } catch (error) {
    console.error('Failed to start test server:', error);
    process.exit(1);
  }
}

// Start the test server
const port = parseInt(process.env.PORT || '3000', 10);
startTestServer(port);
