// Initialize Sentry first (must be before other imports)
import type { Repositories } from '@/middleware/database/types.js';
import { authMiddleware } from '@/monolith/middleware/auth.js';
import { createRepositories } from '@/monolith/middleware/database.js';
import { errorHandler } from '@/monolith/middleware/errorHandler.js';
import { requestLogger } from '@/monolith/middleware/requestLogger.js';
import { createApiRoutes } from '@/monolith/routes/index.js';
import { initializeDatabase } from '@/storage-interface/database-initializer.js';
import { logger } from '@/utils/logger.js';
import cors from 'cors';
import express from 'express';
import { initSentry, sentryErrorHandler } from './utils/sentry.js';

if (process.env.STAGE !== 'local') {
  initSentry();
}

// Extend Express Request to include repositories
declare global {
  namespace Express {
    interface Request {
      repositories: Repositories;
      user?: {
        userId: string;
        email?: string;
        userType?: string;
        username?: string;
      };
    }
  }
}

export async function createApp() {
  const app = express();

  // Initialize database connection once at startup
  logger.info('Initializing database connection...');
  await initializeDatabase();
  const repositories = await createRepositories();
  logger.info('Database connection established');

  // Global middleware
  app.use(
    cors({
      origin: true, // Allow all origins for now, configure as needed
      credentials: true,
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
      allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
    })
  );

  app.use(express.json({ limit: '10mb' }));
  app.use(express.urlencoded({ extended: true }));

  // Request logging
  app.use(requestLogger);

  // Inject repositories into all requests
  app.use((req, res, next) => {
    req.repositories = repositories;
    next();
  });

  // Health check endpoint (no auth required)
  app.get('/ping', (req, res) => {
    res.json({ message: 'pong', version: 'monolith-v2' });
  });

  // API routes with authentication
  app.use('/', authMiddleware, createApiRoutes());

  // Sentry error handler (must be before other error handlers)
  app.use(sentryErrorHandler());

  // Error handling
  app.use(errorHandler);

  return app;
}

export async function startServer(port: number = 3000) {
  try {
    const app = await createApp();

    const server = app.listen(port, '0.0.0.0', () => {
      logger.info(`Server running on port ${port}`);
    });

    // Graceful shutdown
    process.on('SIGTERM', () => {
      logger.info('SIGTERM received, shutting down gracefully');
      server.close(() => {
        logger.info('Server closed');
        process.exit(0);
      });
    });

    process.on('SIGINT', () => {
      logger.info('SIGINT received, shutting down gracefully');
      server.close(() => {
        logger.info('Server closed');
        process.exit(0);
      });
    });

    return server;
  } catch (error) {
    logger.error('Failed to start server:', { error });
    process.exit(1);
  }
}
