#!/usr/bin/env node

import 'reflect-metadata';

import { logger } from '@/utils/logger.js';
import dotenv from 'dotenv';
import { startServer } from './app.js';

// Load environment variables
dotenv.config();

// Set default environment variables for development
if (!process.env.NODE_ENV) {
  process.env.NODE_ENV = 'development';
}

if (!process.env.STAGE) {
  process.env.STAGE = 'dev';
}

// Validate required environment variables
const requiredEnvVars = ['DATABASE_URL', 'COGNITO_USER_POOL_ID', 'COGNITO_USER_POOL_CLIENT_ID'];

const missingEnvVars = requiredEnvVars.filter((envVar) => !process.env[envVar]);
if (missingEnvVars.length > 0) {
  logger.error('Missing required environment variables:', { missingEnvVars });
  process.exit(1);
}

// Get port from environment or default to 3000
const port = parseInt(process.env.PORT || '3000', 10);

// Start the server
startServer(port).catch((error) => {
  logger.error('Failed to start server:', error);
  process.exit(1);
});
