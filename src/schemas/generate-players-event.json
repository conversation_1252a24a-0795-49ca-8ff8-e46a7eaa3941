{"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "properties": {"gameworldId": {"type": "string"}, "requiredPlayers": {"type": "integer", "minimum": 1}, "teamId": {"type": "string"}, "managerId": {"type": "string"}, "minAge": {"type": "integer", "minimum": 16, "maximum": 38}, "maxAge": {"type": "integer", "minimum": 16, "maximum": 38}, "minSkill": {"type": "integer", "minimum": 1, "maximum": 40}, "maxSkill": {"type": "integer", "minimum": 1, "maximum": 40}, "minPotential": {"type": "integer", "minimum": 1, "maximum": 40}, "maxPotential": {"type": "integer", "minimum": 1, "maximum": 40}}, "required": ["gameworldId", "requiredPlayers", "teamId"], "additionalProperties": false}