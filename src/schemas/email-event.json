{"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "properties": {"recipients": {"type": "array", "items": {"type": "string"}}, "subject": {"type": "string"}, "content": {"type": "string"}, "title": {"type": "string"}, "template": {"type": "string"}, "managerId": {"type": "string"}, "category": {"type": "string"}}, "required": ["recipients", "subject", "content", "title", "managerId", "category"], "additionalProperties": false}