{"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "properties": {"playerId": {"type": "string"}, "playerName": {"type": "string"}, "managerId": {"type": "string", "nullable": true}, "attribute": {"type": "string"}, "current": {"type": "number"}, "potential": {"type": "number"}, "trainingMultiplier": {"type": "number"}}, "required": ["playerId", "<PERSON><PERSON><PERSON>", "attribute", "current", "potential", "trainingMultiplier"], "additionalProperties": false}