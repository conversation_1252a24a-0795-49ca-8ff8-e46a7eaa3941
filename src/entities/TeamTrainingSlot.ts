import { <PERSON><PERSON>ty, ManyToOne, PrimaryKey, Property, type Rel } from '@mikro-orm/core';
import { Player } from './Player.js';
import { Team } from './Team.js';

@Entity({ tableName: 'team_training_slots' })
export class TeamTrainingSlot {
  @PrimaryKey({ type: 'uuid', defaultRaw: 'uuid_generate_v4()' })
  id!: string;

  @ManyToOne(() => Team)
  team!: Rel<Team>;

  @Property({ type: 'number' })
  slotIndex!: number; // 0-4

  @ManyToOne(() => Player, { nullable: true })
  player?: Rel<Player>;

  @Property({ type: 'string', nullable: true })
  attribute?: string; // e.g. 'finishing', 'pace', etc.

  @Property({ type: 'float', nullable: true })
  startValue?: number; // attribute value when assigned

  @Property({ type: 'bigint', nullable: true })
  assignedAt?: bigint; // timestamp when training started
}
