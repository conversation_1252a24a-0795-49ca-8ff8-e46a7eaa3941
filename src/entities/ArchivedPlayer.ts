import {
  Entity,
  Index,
  ManyToOne,
  OneToOne,
  PrimaryKey,
  PrimaryKeyProp,
  Property,
  type Rel,
  Unique,
} from '@mikro-orm/core';
import { ArchivedPlayerAttributes } from './ArchivedPlayerAttributes.js';
import { ArchivedPlayerOverallStats } from './ArchivedPlayerOverallStats.js';
import { ArchivedTeam } from './ArchivedTeam.js';

/**
 * Archived snapshot of a Player entity for idle managers
 * Contains all the same fields as Player but with archived prefix
 */
@Entity({ tableName: 'archived_players' })
@Unique({
  name: 'archived_players_gameworld_id_archived_player_id_key',
  properties: ['gameworldId', 'archivedPlayerId'],
})
export class ArchivedPlayer {
  [PrimaryKeyProp]?: 'archivedPlayerId';

  @PrimaryKey({ type: 'uuid', fieldName: 'archived_player_id' })
  archivedPlayerId!: string; // New UUID for the archived player

  @Property({ type: 'uuid' })
  gameworldId!: string;

  @Property({ type: 'bigint' })
  archivedAt!: bigint; // When this player was archived

  @Index()
  @ManyToOne({ entity: () => ArchivedTeam, nullable: true })
  archivedTeam?: Rel<ArchivedTeam>;

  @Property({ type: 'number' })
  age!: number;

  @Property({ type: 'bigint' })
  seed!: bigint;

  @Property({ type: 'string' })
  firstName!: string;

  @Property({ type: 'string' })
  surname!: string;

  @Property({ type: 'numeric', precision: 15, scale: 2 })
  value!: number;

  @Property({ type: 'int' })
  energy!: number; // 0 - 100 value

  @Property({ type: 'bigint' })
  lastMatchPlayed!: bigint;

  @Property({ nullable: true, type: 'bigint' })
  injuredUntil?: bigint;

  @Property({ type: 'number' })
  suspendedForGames!: number;

  @Property({ type: 'boolean', default: false })
  isTransferListed: boolean = false;

  @Property({ type: 'boolean', default: false })
  retiringAtEndOfSeason: boolean = false;

  // Reference to the original player ID
  @Property({ type: 'uuid' })
  originalPlayerId!: string;

  @OneToOne(() => ArchivedPlayerAttributes, (attributes) => attributes.player)
  attributes!: Rel<ArchivedPlayerAttributes>;

  @OneToOne(() => ArchivedPlayerOverallStats, (overallStats) => overallStats.player, {
    nullable: true,
  })
  overallStats?: Rel<ArchivedPlayerOverallStats>;
}
