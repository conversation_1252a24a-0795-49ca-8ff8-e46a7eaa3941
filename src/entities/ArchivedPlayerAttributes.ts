import { Cascade, Entity, OneToOne, PrimaryKeyProp, Property, type Rel } from '@mikro-orm/core';
import { ArchivedPlayer } from './ArchivedPlayer.js';

/**
 * Archived snapshot of PlayerAttributes entity for idle managers
 * Contains all the same fields as PlayerAttributes but with archived prefix
 */
@Entity({ tableName: 'archived_player_attributes' })
export class ArchivedPlayerAttributes {
  [PrimaryKeyProp]?: 'player';

  @OneToOne({
    entity: () => ArchivedPlayer,
    fieldName: 'player_id',
    primary: true,
    cascade: [Cascade.REMOVE],
  })
  player!: Rel<ArchivedPlayer>;

  @Property({ type: 'bigint' })
  archivedAt!: bigint; // When this was archived

  @Property({ type: 'boolean' })
  isGoalkeeper: boolean = false;

  @Property({ type: 'float', columnType: 'float', length: 4 })
  reflexesCurrent!: number;

  @Property({ type: 'float', columnType: 'float', length: 4 })
  reflexesPotential!: number;

  @Property({ type: 'float', columnType: 'float', length: 4 })
  positioningCurrent!: number;

  @Property({ type: 'float', columnType: 'float', length: 4 })
  positioningPotential!: number;

  @Property({ type: 'float', columnType: 'float', length: 4 })
  shotStoppingCurrent!: number;

  @Property({ type: 'float', columnType: 'float', length: 4 })
  shotStoppingPotential!: number;

  @Property({ type: 'float', columnType: 'float', length: 4 })
  tacklingCurrent!: number;

  @Property({ type: 'float', columnType: 'float', length: 4 })
  tacklingPotential!: number;

  @Property({ type: 'float', columnType: 'float', length: 4 })
  markingCurrent!: number;

  @Property({ type: 'float', columnType: 'float', length: 4 })
  markingPotential!: number;

  @Property({ type: 'float', columnType: 'float', length: 4 })
  headingCurrent!: number;

  @Property({ type: 'float', columnType: 'float', length: 4 })
  headingPotential!: number;

  @Property({ type: 'float', columnType: 'float', length: 4 })
  finishingCurrent!: number;

  @Property({ type: 'float', columnType: 'float', length: 4 })
  finishingPotential!: number;

  @Property({ type: 'float', columnType: 'float', length: 4 })
  paceCurrent!: number;

  @Property({ type: 'float', columnType: 'float', length: 4 })
  pacePotential!: number;

  @Property({ type: 'float', columnType: 'float', length: 4 })
  crossingCurrent!: number;

  @Property({ type: 'float', columnType: 'float', length: 4 })
  crossingPotential!: number;

  @Property({ type: 'float', columnType: 'float', length: 4 })
  passingCurrent!: number;

  @Property({ type: 'float', columnType: 'float', length: 4 })
  passingPotential!: number;

  @Property({ type: 'float', columnType: 'float', length: 4 })
  visionCurrent!: number;

  @Property({ type: 'float', columnType: 'float', length: 4 })
  visionPotential!: number;

  @Property({ type: 'float', columnType: 'float', length: 4 })
  ballControlCurrent!: number;

  @Property({ type: 'float', columnType: 'float', length: 4 })
  ballControlPotential!: number;

  @Property({ type: 'float', precision: 2 })
  stamina!: number;
}
