import { <PERSON><PERSON>ty, <PERSON>um, ManyToOne, PrimaryKey, Property, type Rel, Unique } from '@mikro-orm/core';
import { Team } from './Team.js';

/**
 * Entity representing a scouting request
 */
@Entity({ tableName: 'scouting_requests' })
@Unique({ properties: ['requestId'] })
export class ScoutingRequest {
  @PrimaryKey({ type: 'uuid', defaultRaw: 'uuid_generate_v4()' })
  requestId!: string;

  @Property({ type: 'uuid' })
  gameworldId!: string;

  @Property({ type: 'string' })
  managerId!: string;

  @ManyToOne({ entity: () => Team })
  team!: Rel<Team>;

  @Enum(() => ScoutingRequestType)
  type!: ScoutingRequestType;

  @Property({ type: 'string' })
  targetId!: string; // player id, team id, or league id

  @Property({ type: 'bigint' })
  processAfter!: bigint; // minimum time in milliseconds that this report can be processed

  @Property({ type: 'bigint', nullable: true })
  processedAt?: bigint; // when the request was processed

  @Property({ type: 'boolean', default: false })
  processed: boolean = false;

  @Property({ type: 'timestamp', defaultRaw: 'now()' })
  createdAt: Date = new Date();
}

/**
 * Type of scouting request
 */
export enum ScoutingRequestType {
  PLAYER = 'player',
  TEAM = 'team',
  LEAGUE = 'league',
}
