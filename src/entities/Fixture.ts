import { Scorer } from '@/model/fixture.js';
import {
  Entity,
  ManyToOne,
  PrimaryKey,
  PrimaryKeyProp,
  Property,
  type Rel,
  Unique,
} from '@mikro-orm/core';
import { League } from './League.js';
import { Team } from './Team.js';

@Entity({ tableName: 'fixture' })
@Unique({ properties: ['gameworldId', 'league', 'fixtureId'] })
export class Fixture {
  [PrimaryKeyProp]?: 'fixtureId';

  @PrimaryKey({ type: 'uuid' })
  fixtureId!: string;

  @Property({ type: 'uuid' })
  gameworldId!: string;

  @ManyToOne(() => League)
  league!: Rel<League>;

  @ManyToOne(() => Team)
  homeTeam!: Rel<Team>;

  @ManyToOne(() => Team)
  awayTeam!: Rel<Team>;

  @Property({ type: 'bigint' })
  date!: bigint;

  @Property({ type: 'boolean' })
  played: boolean = false;

  @Property({ type: 'bigint', nullable: true })
  simulatedAt?: bigint;

  @Property({ type: 'bigint', nullable: true })
  seed?: bigint;

  @Property({ type: 'int[]', nullable: true })
  score?: [number, number];

  @Property({ type: 'json', nullable: true })
  scorers?: Scorer[];
}
