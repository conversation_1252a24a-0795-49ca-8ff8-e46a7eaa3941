import { Entity, OneToOne, Property, type Rel } from '@mikro-orm/core';
import { League } from './League.js';

@Entity({ tableName: 'league_rules' })
export class LeagueRules {
  constructor(rules?: { promotionSpots: number; relegationSpots: number; teamCount: number }) {
    if (!rules) return;
    this.promotionSpots = rules.promotionSpots;
    this.relegationSpots = rules.relegationSpots;
    this.teamCount = rules.teamCount;
  }

  @OneToOne({
    entity: () => League,
    inversedBy: 'leagueRules',
    owner: true,
    primary: true,
    fieldName: 'league_id',
  })
  league!: Rel<League>;

  @Property({ type: 'number' })
  promotionSpots!: number;

  @Property({ type: 'number' })
  relegationSpots!: number;

  @Property({ type: 'number' })
  teamCount!: number;

  @Property({ type: 'number' })
  maximumPrize: number = 0;
  @Property({ type: 'number' })
  minimumPrize: number = 0;
}
