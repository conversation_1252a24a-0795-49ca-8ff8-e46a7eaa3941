import {
  Collection,
  Entity,
  Index,
  ManyToOne,
  OneToMany,
  OneToOne,
  type Opt,
  PrimaryKey,
  PrimaryKeyProp,
  Property,
  type Rel,
  Unique,
} from '@mikro-orm/core';
import { STARTING_BALANCE } from '../functions/generate/constants.js';
import { League } from './League.js';
import { Manager } from './Manager.js';
import { Player } from './Player.js';

@Entity({ tableName: 'team' })
@Unique({ name: 'teams_gameworld_id_team_id_key', properties: ['gameworldId', 'teamId'] })
export class Team {
  [PrimaryKeyProp]?: 'teamId';

  @PrimaryKey({ type: 'uuid', unique: 'teams_pkey' })
  teamId!: string;

  @Index()
  @Property({ type: 'uuid' })
  gameworldId!: string;

  @ManyToOne({ entity: () => League, index: 'idx_teams_league' })
  league!: Rel<League>;

  @OneToOne({ entity: () => Manager, mappedBy: 'team', nullable: true })
  manager?: Rel<Manager>;

  @Property({ type: 'number' })
  tier!: number;

  @Property({ type: 'string', length: 100 })
  teamName!: string;

  @Property({ type: 'integer' })
  balance: number & Opt = STARTING_BALANCE;

  @Property({ type: 'integer' })
  played: number & Opt = 0;

  @Property({ type: 'integer' })
  points: number & Opt = 0;

  @Property({ type: 'integer' })
  goalsFor: number & Opt = 0;

  @Property({ type: 'integer' })
  goalsAgainst: number & Opt = 0;

  @Property({ type: 'integer' })
  wins: number & Opt = 0;

  @Property({ type: 'integer' })
  draws: number & Opt = 0;

  @Property({ type: 'integer' })
  losses: number & Opt = 0;

  @Property({ type: 'string[]' })
  selectionOrder: string[] & Opt = [];

  @OneToMany(() => Player, (player) => player.team)
  players = new Collection<Player>(this);

  @Property({ type: 'number' })
  trainingLevel: number = 1;
}
