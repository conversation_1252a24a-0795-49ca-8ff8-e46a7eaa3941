import {
  Collection,
  Entity,
  ManyToMany,
  ManyToOne,
  OneToMany,
  OneToOne,
  PrimaryKey,
  Property,
  type Rel,
  Unique,
} from '@mikro-orm/core';
import { Gameworld } from './Gameworld.js';
import { LeagueRules } from './LeagueRules.js';
import { Team } from './Team.js';

@Entity({ tableName: 'league' })
@Unique({ properties: ['id'] })
export class League {
  @PrimaryKey({ type: 'uuid', defaultRaw: 'uuid_generate_v4()' })
  id!: string;

  @ManyToOne({ entity: () => Gameworld, nullable: false })
  gameworld!: Rel<Gameworld>;

  @Property({ type: 'string', length: 100 })
  name!: string;

  @Property({ type: 'number', index: 'idx_leagues_tier' })
  tier!: number;

  @ManyToOne({ entity: () => League, nullable: true, index: 'idx_leagues_parent' })
  parentLeague?: Rel<League>;

  @ManyToMany({
    entity: () => League,
    pivotTable: 'league_children',
    joinColumn: 'parent_league_id',
    inverseJoinColumn: 'child_league_id',
  })
  leagueChildren = new Collection<League>(this);

  @OneToOne({
    entity: () => LeagueRules,
    mappedBy: 'league',
  })
  leagueRules!: Rel<LeagueRules>;

  @OneToMany(() => Team, (team) => team.league)
  teams = new Collection<Team>(this);
}
