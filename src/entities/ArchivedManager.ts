import { <PERSON><PERSON><PERSON>, OneTo<PERSON>ne, <PERSON><PERSON>ey, Property, type Rel, Unique } from '@mikro-orm/core';
import { NotificationCategory, NotificationChannel } from '../model/manager.js';
import { ArchivedTeam } from './ArchivedTeam.js';

// this is mirrored in model/manager.ts because for some reason the build fails if its imported
type NotificationPreferences = {
  [key in NotificationCategory]?: {
    [channel in NotificationChannel]?: boolean;
  };
};

/**
 * Archived snapshot of a Manager entity for idle managers
 * Contains all the same fields as Manager but with archived prefix
 */
@Entity({ tableName: 'archived_manager' })
@Unique({ name: 'archived_manager_id_key', properties: ['managerId'] })
export class ArchivedManager {
  @PrimaryKey({ type: 'string' })
  managerId!: string;

  @Property({ type: 'bigint' })
  createdAt!: bigint;

  @Property({ type: 'bigint' })
  lastActive!: bigint;

  @Property({ type: 'bigint' })
  archivedAt!: bigint; // When this manager was archived

  @Property({ type: 'string', length: 100, nullable: true })
  firstName?: string;

  @Property({ type: 'string', length: 100, nullable: true })
  lastName?: string;

  @Property({ type: 'string', nullable: true })
  email?: string;

  @OneToOne({ entity: () => ArchivedTeam, fieldName: 'archived_team_id', nullable: true })
  archivedTeam?: Rel<ArchivedTeam>;

  @Property({ type: 'uuid', nullable: true })
  gameworldId?: string;

  @Property({ type: 'integer' })
  scoutTokens: number = 2;

  @Property({ type: 'integer' })
  superScoutTokens: number = 0;

  @Property({ type: 'integer' })
  magicSponges: number = 1;

  @Property({ type: 'integer' })
  cardAppeals: number = 0;

  @Property({ type: 'integer' })
  trainingBoosts: number = 0;

  @Property({ type: 'json', nullable: true })
  notificationPreferences?: NotificationPreferences = {};

  @Property({ nullable: true, type: 'string' })
  pushToken?: string;

  @Property({ type: 'integer' })
  loginStreak: number = 0;

  @Property({ type: 'string', default: 'user' })
  role: string = 'user'; // Default role is 'user', can be changed to 'admin'

  @Property({ type: 'boolean', default: false })
  changedTeamName: boolean = false;

  // Manager stats
  @Property({ type: 'integer', default: 0 })
  wins: number = 0;

  @Property({ type: 'integer', default: 0 })
  defeats: number = 0;

  // Guest user and migration tracking
  @Property({ type: 'boolean', default: false })
  isGuest: boolean = false;

  @Property({ type: 'string', nullable: true })
  migratedFromGuestId?: string;

  @Property({ type: 'boolean', default: false })
  migrationCompleted: boolean = false;

  @Property({ type: 'integer', default: 0 })
  draws: number = 0;

  @Property({ type: 'integer', default: 0 })
  goalsScored: number = 0;

  @Property({ type: 'integer', default: 0 })
  goalsConceded: number = 0;

  @Property({ type: 'integer', default: 0 })
  highestTransferPaid: number = 0;

  @Property({ type: 'integer', default: 0 })
  highestTransferReceived: number = 0;

  @Property({ type: 'integer', default: 0 })
  trophies: number = 0;

  // Reference to the original team ID that's still active in the gameworld
  @Property({ type: 'uuid', nullable: true })
  originalTeamId?: string;
}
