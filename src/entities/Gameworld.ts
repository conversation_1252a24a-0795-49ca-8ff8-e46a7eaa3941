import { Collection, Entity, OneToMany, PrimaryKey, Property, Unique } from '@mikro-orm/core';
import { League } from './League.js';

/**
 * Entities describing the end date for a season
 */
@Entity({ tableName: 'gameworld' })
@Unique({ properties: ['id'] })
export class Gameworld {
  @PrimaryKey({ type: 'uuid', defaultRaw: 'uuid_generate_v4()' })
  id!: string;

  @Property({ type: 'bigint' })
  endDate!: bigint; // time in millis when gameworld season should end

  @OneToMany(() => League, (league) => league.gameworld)
  leagues = new Collection<League>(this);

  @Property({ type: 'integer' })
  highestManageableTier: number = 3;
}
