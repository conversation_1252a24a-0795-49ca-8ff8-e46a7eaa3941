import {
  Cascade,
  Entity,
  ManyToOne,
  type Opt,
  PrimaryKey,
  PrimaryKeyProp,
  Property,
  type Rel,
} from '@mikro-orm/core';
import { Fixture } from './Fixture.js';
import { Player } from './Player.js';

@Entity({ tableName: 'player_match_history' })
export class PlayerMatchHistory {
  [PrimaryKeyProp]?: ['player', 'fixtureId'];

  @ManyToOne({
    entity: () => Player,
    fieldName: 'player_id',
    primary: true,
    cascade: [Cascade.REMOVE],
  })
  player!: Rel<Player>;

  @PrimaryKey({ type: 'uuid' })
  fixtureId!: string;

  @ManyToOne({
    entity: () => Fixture,
    fieldName: 'fixture_fixture_id',
    cascade: [Cascade.REMOVE],
  })
  fixture!: Rel<Fixture>;

  @Property({ type: 'integer' })
  yellowCards: number & Opt = 0;

  @Property({ type: 'integer' })
  redCards: number & Opt = 0;

  @Property({ type: 'integer' })
  passesCompleted: number & Opt = 0;

  @Property({ type: 'integer' })
  passesAttempted: number & Opt = 0;

  @Property({ type: 'integer' })
  successfulBallCarries: number & Opt = 0;

  @Property({ type: 'integer' })
  ballCarriesAttempted: number & Opt = 0;

  @Property({ type: 'integer' })
  shots: number & Opt = 0;

  @Property({ type: 'integer' })
  shotsOnTarget: number & Opt = 0;

  @Property({ type: 'integer' })
  goals: number & Opt = 0;

  @Property({ type: 'integer' })
  saves: number & Opt = 0;

  @Property({ type: 'integer' })
  tackles: number & Opt = 0;

  @Property({ type: 'integer' })
  fouls: number & Opt = 0;

  @Property({ type: 'float', nullable: true })
  matchRating?: number;
}
