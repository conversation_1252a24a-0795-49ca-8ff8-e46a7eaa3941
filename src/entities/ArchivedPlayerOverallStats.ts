import { Entity, OneToOne, type Opt, PrimaryKeyProp, Property, type Rel } from '@mikro-orm/core';
import { ArchivedPlayer } from './ArchivedPlayer.js';

/**
 * Archived snapshot of PlayerOverallStats entity for idle managers
 * Contains all the same fields as PlayerOverallStats but with archived prefix
 */
@Entity({ tableName: 'archived_player_overall_stats' })
export class ArchivedPlayerOverallStats {
  [PrimaryKeyProp]?: 'player';

  @OneToOne({ entity: () => ArchivedPlayer, fieldName: 'player_id', primary: true })
  player!: Rel<ArchivedPlayer>;

  @Property({ type: 'bigint' })
  archivedAt!: bigint; // When this was archived

  @Property({ type: 'integer' })
  yellowCards: number & Opt = 0;

  @Property({ type: 'integer' })
  redCards: number & Opt = 0;

  @Property({ type: 'integer' })
  passesCompleted: number & Opt = 0;

  @Property({ type: 'integer' })
  passesAttempted: number & Opt = 0;

  @Property({ type: 'integer' })
  successfulBallCarries: number & Opt = 0;

  @Property({ type: 'integer' })
  ballCarriesAttempted: number & Opt = 0;

  @Property({ type: 'integer' })
  shots: number & Opt = 0;

  @Property({ type: 'integer' })
  shotsOnTarget: number & Opt = 0;

  @Property({ type: 'integer' })
  goals: number & Opt = 0;

  @Property({ type: 'integer' })
  saves: number & Opt = 0;

  @Property({ type: 'integer' })
  tackles: number & Opt = 0;

  @Property({ type: 'integer' })
  fouls: number & Opt = 0;
}
