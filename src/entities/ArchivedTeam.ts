import {
  Collection,
  Entity,
  Index,
  OneToMany,
  OneToOne,
  PrimaryKey,
  PrimaryKeyProp,
  Property,
  type Rel,
  Unique,
} from '@mikro-orm/core';
import { ArchivedManager } from './ArchivedManager.js';
import { ArchivedPlayer } from './ArchivedPlayer.js';

/**
 * Archived snapshot of a Team entity for idle managers
 * Contains all the same fields as Team but with archived prefix
 */
@Entity({ tableName: 'archived_team' })
@Unique({
  name: 'archived_teams_gameworld_id_team_id_key',
  properties: ['gameworldId', 'archivedTeamId'],
})
export class ArchivedTeam {
  [PrimaryKeyProp]?: 'archivedTeamId';

  @PrimaryKey({ type: 'uuid', unique: 'archived_teams_pkey' })
  archivedTeamId!: string; // New UUID for the archived team

  @Index()
  @Property({ type: 'uuid' })
  gameworldId!: string;

  @Property({ type: 'bigint' })
  archivedAt!: bigint; // When this team was archived

  @Property({ type: 'uuid' })
  leagueId!: string;

  @OneToOne({ entity: () => ArchivedManager, mappedBy: 'archivedTeam', nullable: true })
  archivedManager?: Rel<ArchivedManager>;

  @Property({ type: 'number' })
  tier!: number;

  @Property({ type: 'string', length: 100 })
  teamName!: string; // The team name at the time of archival

  @Property({ type: 'integer' })
  balance: number = 0;

  @Property({ type: 'integer' })
  played: number = 0;

  @Property({ type: 'integer' })
  points: number = 0;

  @Property({ type: 'integer' })
  goalsFor: number = 0;

  @Property({ type: 'integer' })
  goalsAgainst: number = 0;

  @Property({ type: 'integer' })
  wins: number = 0;

  @Property({ type: 'integer' })
  draws: number = 0;

  @Property({ type: 'integer' })
  losses: number = 0;

  @Property({ type: 'string[]' })
  selectionOrder: string[] = [];

  @Property()
  trainingLevel: number = 1;

  @OneToMany(() => ArchivedPlayer, (player) => player.archivedTeam)
  archivedPlayers = new Collection<ArchivedPlayer>(this);

  // Reference to the original team ID that's still active in the gameworld
  @Property({ type: 'uuid' })
  originalTeamId!: string;
}
