import {
  Cascade,
  Entity,
  ManyToOne,
  PrimaryKeyProp,
  Property,
  type Rel,
  Unique,
} from '@mikro-orm/core';
import { Player } from './Player.js';
import { Team } from './Team.js';

@Entity({ tableName: 'scouted_players' })
@Unique({ properties: ['gameworldId', 'team', 'player'] })
export class ScoutedPlayer {
  [PrimaryKeyProp]?: ['team', 'player'];

  @Property({ type: 'uuid' })
  gameworldId!: string;

  @ManyToOne({ entity: () => Team, primary: true })
  team!: Rel<Team>;

  @ManyToOne({ entity: () => Player, primary: true, cascade: [Cascade.REMOVE] })
  player!: Rel<Player>;

  @Property({ type: 'bigint' })
  scoutedAt!: bigint;
}
