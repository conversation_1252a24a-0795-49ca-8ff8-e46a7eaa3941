import { <PERSON>tity, OneToOne, type Opt, PrimaryKeyProp, Property, type Rel } from '@mikro-orm/core';
import { Player } from './Player.js';

@Entity({ tableName: 'player_overall_stats' })
export class PlayerOverallStats {
  [PrimaryKeyProp]?: 'player';

  @OneToOne({ entity: () => Player, fieldName: 'player_id', primary: true })
  player!: Rel<Player>;

  @Property({ type: 'integer' })
  yellowCards: number & Opt = 0;

  @Property({ type: 'integer' })
  redCards: number & Opt = 0;

  @Property({ type: 'integer' })
  passesCompleted: number & Opt = 0;

  @Property({ type: 'integer' })
  passesAttempted: number & Opt = 0;

  @Property({ type: 'integer' })
  successfulBallCarries: number & Opt = 0;

  @Property({ type: 'integer' })
  ballCarriesAttempted: number & Opt = 0;

  @Property({ type: 'integer' })
  shots: number & Opt = 0;

  @Property({ type: 'integer' })
  shotsOnTarget: number & Opt = 0;

  @Property({ type: 'integer' })
  goals: number & Opt = 0;

  @Property({ type: 'integer' })
  saves: number & Opt = 0;

  @Property({ type: 'integer' })
  tackles: number & Opt = 0;

  @Property({ type: 'integer' })
  fouls: number & Opt = 0;
}
