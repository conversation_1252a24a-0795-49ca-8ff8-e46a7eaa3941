import { Manager } from '@/entities/Manager.js';
import { httpMiddify } from '@/middleware/rest/index.js';
import { HttpEvent } from '@/middleware/rest/types.js';
import { buildResponse } from '@/utils/buildResponse.js';
import { getUser } from '@/utils/getUser.js';
import { logger } from '@/utils/logger.js';

interface CreateGuestUserResponse {
  guestUserId: string;
  identityId: string;
  team?: {
    teamId: string;
    gameworldId: string;
  };
}

export const main = async (event: HttpEvent<void, void, void>): Promise<any> => {
  const { managerRepository, teamRepository } = event.context.repositories;

  try {
    // Get identity ID from token or device ID from body/headers
    const userId = getUser(event);
    if (!userId) {
      return buildResponse(401, JSON.stringify({ error: 'Unauthorized' }));
    }

    // Check if a guest user already exists
    let existingManager = await managerRepository.getManagerById(userId);

    if (existingManager) {
      logger.info('Guest user already exists', {
        userId,
        managerId: existingManager.managerId,
      });

      const team = existingManager.team
        ? {
            teamId: existingManager.team.teamId,
            teamName: existingManager.team.teamName,
            gameworldId: existingManager.team.gameworldId,
          }
        : undefined;

      return buildResponse(
        200,
        JSON.stringify({
          guestUserId: existingManager.managerId,
          team,
        } as CreateGuestUserResponse)
      );
    }

    // --- TRANSACTION WRAP START ---
    const em = teamRepository.getEntityManager();
    return await em.transactional(async (txEm) => {
      // Get an available team
      const availableTeam = await teamRepository.getAndDeleteAvailableTeam(txEm);
      if (!availableTeam) {
        return buildResponse(503, JSON.stringify({ error: 'No available teams' }));
      }

      // Create a new guest manager using identity ID as manager ID
      const manager = new Manager();
      manager.managerId = userId;
      manager.team = teamRepository.createFromPK(availableTeam.teamId);
      manager.gameworldId = availableTeam.gameworldId;
      manager.isGuest = true;
      manager.scoutTokens = 3;
      manager.superScoutTokens = 0;
      manager.createdAt = BigInt(new Date().getTime());
      manager.lastActive = BigInt(new Date().getTime());
      manager.role = 'guest';

      // Save the manager
      await managerRepository.createManager(manager, txEm);

      // Initialize the team
      await teamRepository.reinitialiseTeam(
        availableTeam.teamId,
        availableTeam.gameworldId,
        event.context.repositories.playerRepository,
        event.context.repositories.transferRepository,
        txEm
      );

      logger.info('Guest user created successfully', {
        userId,
        teamId: availableTeam.teamId,
      });

      return buildResponse(
        201,
        JSON.stringify({
          guestUserId: userId,
          team: {
            teamId: availableTeam.teamId,
            gameworldId: availableTeam.gameworldId,
          },
        } as CreateGuestUserResponse)
      );
    });
    // --- TRANSACTION WRAP END ---
  } catch (error) {
    logger.error('Error creating guest user:', { error });
    return buildResponse(500, JSON.stringify({ error: 'Internal server error' }));
  }
};

export const handler = httpMiddify(main, {});
