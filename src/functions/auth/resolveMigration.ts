import { httpMiddify } from '@/middleware/rest/index.js';
import { HttpEvent } from '@/middleware/rest/types.js';
import { DataMigrationService } from '@/services/migration/DataMigrationService.js';
import { buildResponse } from '@/utils/buildResponse.js';
import { getUser, getUserEmail } from '@/utils/getUser.js';
import { logger } from '@/utils/logger.js';

interface ResolveMigrationRequest {
  guestUserId: string;
  keepGuestData: boolean; // true = keep guest data, false = keep authenticated data
}

interface ResolveMigrationResponse {
  success: boolean;
  keptTeamId: string;
  message: string;
}

export const main = async (event: HttpEvent<ResolveMigrationRequest, {}, {}>): Promise<any> => {
  const { guestUserId, keepGuestData } = event.body;
  const authenticatedUserId = getUser(event);
  const authenticatedUserEmail = getUserEmail(event);
  const { managerRepository, teamRepository } = event.context.repositories;

  if (!guestUserId || keepGuestData === undefined) {
    return buildResponse(
      400,
      JSON.stringify({
        error: 'Guest user ID and keepGuestData flag are required',
      })
    );
  }

  try {
    const migrationService = new DataMigrationService(managerRepository, teamRepository);

    const keptTeamId = await migrationService.resolveMigration({
      guestUserId,
      authenticatedUserId,
      keepGuestData,
      authenticatedUserEmail,
    });

    const message = keepGuestData
      ? 'Guest team data has been migrated to your account'
      : 'Your existing team data has been kept';

    return buildResponse(
      200,
      JSON.stringify({
        success: true,
        keptTeamId,
        message,
      } as ResolveMigrationResponse)
    );
  } catch (error) {
    logger.error('Error resolving migration:', {
      error,
      guestUserId,
      authenticatedUserId,
      keepGuestData,
    });

    if (error instanceof Error) {
      if (error.message.includes('already completed')) {
        return buildResponse(409, JSON.stringify({ error: error.message }));
      }
      if (error.message.includes('not found')) {
        return buildResponse(404, JSON.stringify({ error: error.message }));
      }
    }

    return buildResponse(500, JSON.stringify({ error: 'Internal server error' }));
  }
};

export const handler = httpMiddify(main, {});
