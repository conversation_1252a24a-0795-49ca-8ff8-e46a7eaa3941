import { httpMiddify } from '@/middleware/rest/index.js';
import { HttpEvent } from '@/middleware/rest/types.js';
import { DataMigrationService } from '@/services/migration/DataMigrationService.js';
import { buildResponse } from '@/utils/buildResponse.js';
import { getUser } from '@/utils/getUser.js';
import { logger } from '@/utils/logger.js';

interface MigrationConflictsRequest {
  guestUserId: string;
}

interface TeamData {
  teamId: string;
  teamName: string;
  gameworldId: string;
  balance: number;
  played: number;
  points: number;
  wins: number;
  draws: number;
  losses: number;
  managerStats: {
    scoutTokens: number;
    superScoutTokens: number;
    magicSponges: number;
    cardAppeals: number;
    trainingBoosts: number;
    loginStreak: number;
    wins: number;
    defeats: number;
    trophies: number;
  };
}

interface MigrationConflictsResponse {
  hasConflicts: boolean;
  guestData?: TeamData;
  authenticatedData?: TeamData;
}

export const main = async (
  event: HttpEvent<void, void, MigrationConflictsRequest>
): Promise<any> => {
  const { guestUserId } = event.queryStringParameters;
  const authenticatedUserId = getUser(event);
  const { managerRepository, teamRepository } = event.context.repositories;

  try {
    const migrationService = new DataMigrationService(managerRepository, teamRepository);
    const conflicts = await migrationService.checkMigrationConflicts(
      guestUserId,
      authenticatedUserId
    );

    if (!conflicts.hasConflicts) {
      return buildResponse(
        200,
        JSON.stringify({
          hasConflicts: false,
        } as MigrationConflictsResponse)
      );
    }

    // Both users have teams, so there are conflicts
    const guestManager = conflicts.guestManager!;
    const authenticatedManager = conflicts.authenticatedManager!;

    const guestTeamData: TeamData = {
      teamId: guestManager.team!.teamId,
      teamName: guestManager.team!.teamName,
      gameworldId: guestManager.team!.gameworldId,
      balance: guestManager.team!.balance,
      played: guestManager.team!.played,
      points: guestManager.team!.points,
      wins: guestManager.team!.wins,
      draws: guestManager.team!.draws,
      losses: guestManager.team!.losses,
      managerStats: {
        scoutTokens: guestManager.scoutTokens,
        superScoutTokens: guestManager.superScoutTokens,
        magicSponges: guestManager.magicSponges,
        cardAppeals: guestManager.cardAppeals,
        trainingBoosts: guestManager.trainingBoosts,
        loginStreak: guestManager.loginStreak,
        wins: guestManager.wins,
        defeats: guestManager.defeats,
        trophies: guestManager.trophies || 0,
      },
    };

    const authenticatedTeamData: TeamData = {
      teamId: authenticatedManager.team!.teamId,
      teamName: authenticatedManager.team!.teamName,
      gameworldId: authenticatedManager.team!.gameworldId,
      balance: authenticatedManager.team!.balance,
      played: authenticatedManager.team!.played,
      points: authenticatedManager.team!.points,
      wins: authenticatedManager.team!.wins,
      draws: authenticatedManager.team!.draws,
      losses: authenticatedManager.team!.losses,
      managerStats: {
        scoutTokens: authenticatedManager.scoutTokens,
        superScoutTokens: authenticatedManager.superScoutTokens,
        magicSponges: authenticatedManager.magicSponges,
        cardAppeals: authenticatedManager.cardAppeals,
        trainingBoosts: authenticatedManager.trainingBoosts,
        loginStreak: authenticatedManager.loginStreak,
        wins: authenticatedManager.wins,
        defeats: authenticatedManager.defeats,
        trophies: authenticatedManager.trophies || 0,
      },
    };

    return buildResponse(
      200,
      JSON.stringify({
        hasConflicts: true,
        guestData: guestTeamData,
        authenticatedData: authenticatedTeamData,
      } as MigrationConflictsResponse)
    );
  } catch (error) {
    logger.error('Error getting migration conflicts:', { error, guestUserId, authenticatedUserId });
    return buildResponse(500, JSON.stringify({ error: 'Internal server error' }));
  }
};

export const handler = httpMiddify(main, {
  schema: {
    $schema: 'http://json-schema.org/draft-07/schema#',
    type: 'object',
    properties: {
      queryStringParameters: {
        type: 'object',
        properties: {
          guestUserId: {
            type: 'string',
          },
        },
        required: ['guestUserId'],
      },
    },
    required: ['queryStringParameters'],
  },
});
