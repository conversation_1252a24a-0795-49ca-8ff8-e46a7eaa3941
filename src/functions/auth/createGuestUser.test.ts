import { AvailableTeam } from '@/entities/AvailableTeam.js';
import * as createGuestUser from '@/functions/auth/createGuestUser.js';
import createHttpEvent from '@/testing/createHttpEvent.js';
import { ManagerFactory } from '@/testing/factories/managerFactory.js';
import { TeamsFactory } from '@/testing/factories/teamFactory.js';
import {
  mockManagerRepository,
  mockTeamRepository,
  resetAllRepositoryMocks,
} from '@/testing/mockRepositories.js';
import { beforeEach, describe, expect, it, vi } from 'vitest';

describe('createGuestUser', () => {
  let mockEvent = createHttpEvent({
    body: { deviceId: 'test-device-123' },
    httpMethod: 'POST',
  });
  const mockContext = {} as any;

  beforeEach(() => {
    vi.clearAllMocks();
    resetAllRepositoryMocks();

    mockEvent = createHttpEvent({
      body: { deviceId: 'test-device-123' },
      httpMethod: 'POST',
    });
  });

  it('should create a new guest user when device ID does not exist', async () => {
    // Mock no existing manager
    mockManagerRepository.getManagerById = vi.fn().mockResolvedValue(null);

    // Mock available team
    const mockAvailableTeam = {
      teamId: 'team-123',
      id: 'test-id',
      gameworldId: 'gameworld-123',
    } as AvailableTeam;

    mockTeamRepository.getAndDeleteAvailableTeam = vi.fn().mockResolvedValue(mockAvailableTeam);
    mockTeamRepository.createFromPK = vi.fn().mockReturnValue({ teamId: 'team-123' });
    mockManagerRepository.createManager = vi.fn().mockResolvedValue(undefined);
    mockTeamRepository.reinitialiseTeam = vi.fn().mockResolvedValue(undefined);

    const response = await createGuestUser.handler(mockEvent, mockContext);

    expect(response.statusCode).toBe(201);

    const responseBody = JSON.parse(response.body);
    expect(responseBody).toMatchObject({
      guestUserId: expect.any(String),
      team: {
        teamId: 'team-123',
        gameworldId: 'gameworld-123',
      },
    });

    expect(mockManagerRepository.createManager).toHaveBeenCalledWith(
      expect.objectContaining({
        isGuest: true,
        scoutTokens: 3,
        superScoutTokens: 0,
        managerId: expect.any(String),
        team: expect.objectContaining({
          teamId: 'team-123',
        }),
        // add more properties as needed
      }),
      expect.anything()
    );
  });

  it('should return existing guest user when device ID already exists', async () => {
    const existingManager = ManagerFactory.build({
      managerId: 'existing-guest-123',
      isGuest: true,
      team: TeamsFactory.build({
        teamId: 'team-456',
        teamName: 'Existing Team',
        gameworldId: 'gameworld-456',
      }),
    });

    mockManagerRepository.getManagerById = vi.fn().mockResolvedValue(existingManager);

    const response = await createGuestUser.handler(mockEvent, mockContext);

    expect(response.statusCode).toBe(200);

    const responseBody = JSON.parse(response.body);
    expect(responseBody).toMatchObject({
      guestUserId: 'existing-guest-123',
      team: {
        teamId: 'team-456',
        gameworldId: 'gameworld-456',
      },
    });

    expect(mockManagerRepository.createManager).not.toHaveBeenCalled();
  });

  it('should return 503 when no available teams', async () => {
    mockManagerRepository.getManagerById = vi.fn().mockResolvedValue(null);
    mockTeamRepository.getAndDeleteAvailableTeam = vi.fn().mockResolvedValue(null);

    const response = await createGuestUser.handler(mockEvent, mockContext);

    expect(response.statusCode).toBe(503);

    const responseBody = JSON.parse(response.body);
    expect(responseBody.error).toBe('No available teams');
  });

  it('should handle errors gracefully', async () => {
    mockManagerRepository.getManagerById = vi.fn().mockRejectedValue(new Error('Database error'));

    const response = await createGuestUser.handler(mockEvent, mockContext);

    expect(response.statusCode).toBe(500);

    const responseBody = JSON.parse(response.body);
    expect(responseBody.error).toBe('Internal server error');
  });
});
