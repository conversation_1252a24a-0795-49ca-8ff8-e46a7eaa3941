export const getTransactionsSchema = {
  $schema: 'http://json-schema.org/draft-07/schema#',
  type: 'object',
  properties: {
    queryStringParameters: {
      type: 'object',
      properties: {
        days: {
          type: 'string',
        },
      },
    },
    pathParameters: {
      type: 'object',
      properties: {
        gameworldId: {
          type: 'string',
        },
        teamId: {
          type: 'string',
        },
      },
      required: ['gameworldId', 'teamId'],
    },
  },
  required: ['pathParameters'],
};
