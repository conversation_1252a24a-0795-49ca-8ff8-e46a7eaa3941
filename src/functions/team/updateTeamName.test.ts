import createHttpEvent from '@/testing/createHttpEvent.js';
import {
  mockManagerRepository,
  mockTeamRepository,
  resetAllRepositoryMocks,
} from '@/testing/mockRepositories.js';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { handler } from './updateTeamName.js';

describe('Update Team Name Handler', () => {
  const mockContext = {} as any;

  beforeEach(() => {
    resetAllRepositoryMocks();
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  it('returns 404 when manager is not found', async () => {
    const event = createHttpEvent({
      body: { teamName: 'New Team Name' },
      headers: { Authorization: 'Bearer valid-token' },
      httpMethod: 'POST',
    });

    vi.mocked(mockManagerRepository.getManagerById).mockResolvedValue(null);

    const response = await handler(event, mockContext);
    const body = JSON.parse(response.body);

    expect(response.statusCode).toBe(404);
    expect(body.error).toBe('Manager or team not found');
  });

  it('returns 404 when manager has no team', async () => {
    const event = createHttpEvent({
      body: { teamName: 'New Team Name' },
      headers: { Authorization: 'Bearer valid-token' },
      httpMethod: 'POST',
    });

    vi.mocked(mockManagerRepository.getManagerById).mockResolvedValue({
      managerId: 'manager-id',
      team: null,
      gameworldId: 'gameworld-id',
    });

    const response = await handler(event, mockContext);
    const body = JSON.parse(response.body);

    expect(response.statusCode).toBe(404);
    expect(body.error).toBe('Manager or team not found');
  });

  it('returns 400 when team name is not provided', async () => {
    const event = createHttpEvent({
      body: {},
      headers: { Authorization: 'Bearer valid-token' },
      httpMethod: 'POST',
    });

    vi.mocked(mockManagerRepository.getManagerById).mockResolvedValue({
      managerId: 'manager-id',
      team: { teamId: 'team-id' },
      gameworldId: 'gameworld-id',
    });

    const response = await handler(event, mockContext);
    const body = JSON.parse(response.body);

    expect(response.statusCode).toBe(400);
    expect(body.error).toBe('Team name must be provided');
  });

  it('returns 400 when team name is too long', async () => {
    const event = createHttpEvent({
      body: { teamName: 'A'.repeat(101) }, // 101 characters
      headers: { Authorization: 'Bearer valid-token' },
      httpMethod: 'POST',
    });

    vi.mocked(mockManagerRepository.getManagerById).mockResolvedValue({
      managerId: 'manager-id',
      team: { teamId: 'team-id' },
      gameworldId: 'gameworld-id',
    });

    const response = await handler(event, mockContext);
    const body = JSON.parse(response.body);

    expect(response.statusCode).toBe(400);
    expect(body.error).toBe('Team name must be 100 characters or less');
  });

  it('returns 406 when team name contains obscenity', async () => {
    const event = createHttpEvent({
      body: { teamName: 'fuck team' }, // Contains obscenity
      headers: { Authorization: 'Bearer valid-token' },
      httpMethod: 'POST',
    });

    vi.mocked(mockManagerRepository.getManagerById).mockResolvedValue({
      managerId: 'manager-id',
      team: { teamId: 'team-id' },
      gameworldId: 'gameworld-id',
    });

    const response = await handler(event, mockContext);
    const body = JSON.parse(response.body);

    expect(response.statusCode).toBe(406);
    expect(body.error).toBe('Oi! Children present!');
  });

  it('successfully updates team name and sets changedTeamName to true', async () => {
    const event = createHttpEvent({
      body: { teamName: 'New Team Name' },
      headers: { Authorization: 'Bearer valid-token' },
      httpMethod: 'POST',
    });

    const manager = {
      managerId: 'manager-id',
      team: { teamId: 'team-id' },
      gameworldId: 'gameworld-id',
    };

    const updatedTeam = {
      teamId: 'team-id',
      teamName: 'New Team Name',
      gameworldId: 'gameworld-id',
    };

    vi.mocked(mockManagerRepository.getManagerById).mockResolvedValue(manager);
    vi.mocked(mockTeamRepository.updateTeamName).mockResolvedValue({});
    vi.mocked(mockManagerRepository.updateManagerById).mockResolvedValue({});
    vi.mocked(mockTeamRepository.getTeam).mockResolvedValue(updatedTeam);

    const response = await handler(event, mockContext);
    const body = JSON.parse(response.body);

    expect(response.statusCode).toBe(200);
    expect(body.teamName).toBe('New Team Name');
    expect(mockTeamRepository.updateTeamName).toHaveBeenCalledWith(
      'team-id',
      'gameworld-id',
      'New Team Name'
    );
    expect(mockManagerRepository.updateManagerById).toHaveBeenCalledWith('debug-user-id', {
      changedTeamName: true,
    });
    expect(mockTeamRepository.getTeam).toHaveBeenCalledWith('gameworld-id', 'team-id', false);
  });

  it('returns 500 when team update fails', async () => {
    const event = createHttpEvent({
      body: { teamName: 'New Team Name' },
      headers: { Authorization: 'Bearer valid-token' },
      httpMethod: 'POST',
    });

    const manager = {
      managerId: 'manager-id',
      team: { teamId: 'team-id' },
      gameworldId: 'gameworld-id',
    };

    vi.mocked(mockManagerRepository.getManagerById).mockResolvedValue(manager);
    vi.mocked(mockTeamRepository.updateTeamName).mockRejectedValue(new Error('Database error'));

    const response = await handler(event, mockContext);
    const body = JSON.parse(response.body);

    expect(response.statusCode).toBe(500);
    expect(body.error).toBe('Failed to update team name');
  });

  it('accepts team name with exactly 100 characters', async () => {
    const event = createHttpEvent({
      body: { teamName: 'A'.repeat(100) }, // Exactly 100 characters
      headers: { Authorization: 'Bearer valid-token' },
      httpMethod: 'POST',
    });

    const manager = {
      managerId: 'manager-id',
      team: { teamId: 'team-id' },
      gameworldId: 'gameworld-id',
    };

    const updatedTeam = {
      teamId: 'team-id',
      teamName: 'A'.repeat(100),
      gameworldId: 'gameworld-id',
    };

    vi.mocked(mockManagerRepository.getManagerById).mockResolvedValue(manager);
    vi.mocked(mockTeamRepository.updateTeamName).mockResolvedValue({});
    vi.mocked(mockManagerRepository.updateManagerById).mockResolvedValue({});
    vi.mocked(mockTeamRepository.getTeam).mockResolvedValue(updatedTeam);

    const response = await handler(event, mockContext);

    expect(response.statusCode).toBe(200);
    expect(mockTeamRepository.updateTeamName).toHaveBeenCalledWith(
      'team-id',
      'gameworld-id',
      'A'.repeat(100)
    );
  });
});
