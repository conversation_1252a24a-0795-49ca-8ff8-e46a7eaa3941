import { httpMiddify } from '@/middleware/rest/index.js';
import { HttpEvent } from '@/middleware/rest/types.js';
import { buildResponse } from '@/utils/buildResponse.js';
import { getUser } from '@/utils/getUser.js';

interface PathParameters {
  teamId: string;
  gameworldId: string;
}

interface Body {
  playerIds: string[];
}

type UpdateTeamOrderEvent = HttpEvent<Body, PathParameters, void>;

export const main = async function (event: UpdateTeamOrderEvent) {
  if (!event.body) {
    return buildResponse(400, JSON.stringify({ error: 'Missing request body' }));
  }

  const playerIds = event.body.playerIds;
  if (!Array.isArray(playerIds)) {
    return buildResponse(
      400,
      JSON.stringify({ error: 'Body must be an array of player IDs', playerIds })
    );
  }

  const { managerRepository, teamRepository } = event.context.repositories;
  const userId = getUser(event);
  const teamId = event.pathParameters.teamId;
  const gameworldId = event.pathParameters.gameworldId;

  // Get the manager to verify team ownership
  const manager = await managerRepository.getManagerById(userId);

  if (!manager || manager.team?.teamId !== teamId || manager.gameworldId !== gameworldId) {
    return buildResponse(403, JSON.stringify({ error: 'Not authorized to update this team' }));
  }

  // Get the team first to verify it exists
  const team = await teamRepository.getTeam(gameworldId, teamId, false);

  if (!team) {
    return buildResponse(404, JSON.stringify({ error: 'Team not found' }));
  }

  // Update the team with the new selection order
  await teamRepository.updateTeamSelectionOrder(teamId, gameworldId, playerIds);

  return buildResponse(200, JSON.stringify({ message: 'Team order updated successfully' }));
};

export const handler = httpMiddify(main, {});
