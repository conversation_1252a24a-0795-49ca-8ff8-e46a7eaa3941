import createHttpEvent from '@/testing/createHttpEvent.js';
import { mockManagerRepository, mockTeamRepository } from '@/testing/mockRepositories.js';
import { buildResponse } from '@/utils/buildResponse.js';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { handler } from './getTransactions.js';

vi.mock('@/utils/getUser.js', () => ({
  getUser: vi.fn().mockReturnValue('mockTeamId'),
}));

describe('getTransactions handler', () => {
  const mockContext = {} as any;

  beforeEach(() => {
    vi.clearAllMocks();
    mockManagerRepository.getManagerById.mockResolvedValue({
      managerId: 'test-user-id',
      team: { teamId: 'mockTeamId' },
      gameworldId: 'test-gameworld',
    });
  });

  it('returns 200 with transactions when found', async () => {
    const mockTransactions = [{ id: 1, amount: 100 }];
    mockTeamRepository.getTransactions.mockResolvedValue(mockTransactions);

    const event = createHttpEvent({
      queryStringParameters: { days: '7' },
      pathParameters: { gameworldId: 'test-gameworld', teamId: 'mockTeamId' },
    });
    event.context = mockContext;

    const response = await handler(event, mockContext);

    expect(mockTeamRepository.getTransactions).toHaveBeenCalledWith(7, 'mockTeamId');
    expect(response).toEqual(
      buildResponse(200, JSON.stringify({ transactions: mockTransactions }))
    );
  });

  it('returns 404 when no transactions are found', async () => {
    mockTeamRepository.getTransactions.mockResolvedValue(null);

    const event = createHttpEvent({
      queryStringParameters: { days: '14' },
      pathParameters: { gameworldId: 'test-gameworld', teamId: 'mockTeamId' },
    });
    event.context = mockContext;

    const response = await handler(event, mockContext);

    expect(mockTeamRepository.getTransactions).toHaveBeenCalledWith(14, 'mockTeamId');
    expect(response).toEqual(buildResponse(404, JSON.stringify({ error: 'Team not found' })));
  });

  it('defaults to 14 days when days parameter is not provided', async () => {
    const mockTransactions = [{ id: 1, amount: 100 }];
    mockTeamRepository.getTransactions.mockResolvedValue(mockTransactions);

    const event = createHttpEvent({
      pathParameters: { gameworldId: 'test-gameworld', teamId: 'mockTeamId' },
    });
    event.context = mockContext;

    const response = await handler(event, mockContext);

    expect(mockTeamRepository.getTransactions).toHaveBeenCalledWith(14, 'mockTeamId');
    expect(response).toEqual(
      buildResponse(200, JSON.stringify({ transactions: mockTransactions }))
    );
  });

  it('handles invalid schema gracefully', async () => {
    const mockTransactions = [{ id: 1, amount: 100 }];
    mockTeamRepository.getTransactions.mockResolvedValue(mockTransactions);

    const event = createHttpEvent({
      queryStringParameters: { days: 'invalid' },
      pathParameters: { gameworldId: 'test-gameworld' },
    });
    event.context = mockContext;

    const response = await handler(event, mockContext);

    expect(mockTeamRepository.getTransactions).not.toHaveBeenCalled();
    expect(response.statusCode).toBe(422);
    expect(JSON.parse(response.body)).toEqual([
      {
        instancePath: '/pathParameters',
        message: "must have required property 'teamId'",
      },
    ]);
  });
});
