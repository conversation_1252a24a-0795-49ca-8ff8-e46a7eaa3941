import { buildResponse } from '@/utils/buildResponse.js';
import { beforeEach, describe, expect, it, vi } from 'vitest';

// Import the main function directly instead of the handler
import { handler } from '@/functions/team/updateTeamOrder.js';
import createHttpEvent from '@/testing/createHttpEvent.js';
import { ManagerFactory } from '@/testing/factories/managerFactory.js';
import { TeamsFactory } from '@/testing/factories/teamFactory.js';
import { mockManagerRepository, mockTeamRepository } from '@/testing/mockRepositories.js';

// Mock the getUser function
vi.mock('@/utils/getUser.js', () => ({
  getUser: vi.fn().mockImplementation(() => 'user1'),
}));

describe('updateTeamOrder', () => {
  const mockContext = {} as any;
  const mockManager = ManagerFactory.build({
    managerId: 'user1',
    team: TeamsFactory.build({ teamId: 'team1' }),
    gameworldId: 'gameworld1',
  });

  const mockTeam = TeamsFactory.build({
    teamId: 'team1',
    gameworldId: 'gameworld1',
    selectionOrder: ['player1', 'player2', 'player3'],
    teamName: 'Test Team',
    tier: 1,
    balance: 100000,
  });

  // Create a mock event with repositories in context
  const createMockEvent = (body: any, pathParameters: any) =>
    createHttpEvent({
      body,
      pathParameters,
      httpMethod: 'POST',
    });

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should return 400 if body is missing', async () => {
    const event = createMockEvent(
      {},
      {
        teamId: 'team1',
        gameworldId: 'gameworld1',
      }
    );

    const response = await handler(event, mockContext);
    expect(response).toEqual(
      buildResponse(400, JSON.stringify({ error: 'Body must be an array of player IDs' }))
    );
  });

  it('should return 400 if playerIds is not an array', async () => {
    const event = createMockEvent(
      { playerIds: 'not-an-array' },
      {
        teamId: 'team1',
        gameworldId: 'gameworld1',
      }
    );

    const response = await handler(event, mockContext);
    expect(response).toEqual(
      buildResponse(
        400,
        JSON.stringify({ error: 'Body must be an array of player IDs', playerIds: 'not-an-array' })
      )
    );
  });

  it('should return 403 if manager is not found', async () => {
    // Mock the getManager method to return null
    mockManagerRepository.getManagerById.mockResolvedValue(null);

    const event = createMockEvent(
      { playerIds: ['player1', 'player2', 'player3'] },
      {
        teamId: 'team1',
        gameworldId: 'gameworld1',
      }
    );

    const response = await handler(event, mockContext);

    expect(mockManagerRepository.getManagerById).toHaveBeenCalledWith('user1');
    expect(response).toEqual(
      buildResponse(403, JSON.stringify({ error: 'Not authorized to update this team' }))
    );
  });

  it('should return 403 if manager does not own the team', async () => {
    const wrongManager = { ...mockManager, team: { teamId: 'different-team' } };
    mockManagerRepository.getManagerById.mockResolvedValue(wrongManager);
    mockTeamRepository.getTeam.mockResolvedValue(mockTeam);

    const event = createMockEvent(
      { playerIds: ['player1', 'player2', 'player3'] },
      {
        teamId: 'team1',
        gameworldId: 'gameworld1',
      }
    );

    const response = await handler(event, mockContext);
    expect(response).toEqual(
      buildResponse(403, JSON.stringify({ error: 'Not authorized to update this team' }))
    );
  });

  it('should return 404 if team is not found', async () => {
    mockManagerRepository.getManagerById.mockResolvedValue(mockManager);
    mockTeamRepository.getTeam.mockResolvedValue(null);

    const event = createMockEvent(
      { playerIds: ['player1', 'player2', 'player3'] },
      {
        teamId: 'team1',
        gameworldId: 'gameworld1',
      }
    );

    const response = await handler(event, mockContext);

    expect(mockTeamRepository.getTeam).toHaveBeenCalledWith('gameworld1', 'team1', false);
    expect(response).toEqual(buildResponse(404, JSON.stringify({ error: 'Team not found' })));
  });

  it('should update team order successfully', async () => {
    mockManagerRepository.getManagerById.mockResolvedValue(mockManager);
    mockTeamRepository.getTeam.mockResolvedValue(mockTeam);
    mockTeamRepository.updateTeamSelectionOrder.mockResolvedValue({});

    const newOrder = ['player3', 'player1', 'player2'];
    const event = createMockEvent(
      { playerIds: newOrder },
      {
        teamId: 'team1',
        gameworldId: 'gameworld1',
      }
    );

    const response = await handler(event, mockContext);

    expect(mockManagerRepository.getManagerById).toHaveBeenCalledWith('user1');
    expect(mockTeamRepository.getTeam).toHaveBeenCalledWith('gameworld1', 'team1', false);
    expect(mockTeamRepository.updateTeamSelectionOrder).toHaveBeenCalledWith(
      'team1',
      'gameworld1',
      newOrder
    );
    expect(response).toEqual(
      buildResponse(200, JSON.stringify({ message: 'Team order updated successfully' }))
    );
  });
});
