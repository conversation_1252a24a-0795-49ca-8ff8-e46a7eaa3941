import { getTransactionsSchema } from '@/functions/team/schema.js';
import { httpMiddify } from '@/middleware/rest/index.js';
import { HttpEvent } from '@/middleware/rest/types.js';
import { buildResponse } from '@/utils/buildResponse.js';
import { getUser } from '@/utils/getUser.js';

interface PathParameters {
  teamId: string;
  gameworldId: string;
}

interface QueryParameters {
  days?: number; // Number of days to look back for transactions (max 14)
}

export type GetTransactionsEvent = HttpEvent<void, PathParameters, QueryParameters>;

export const main = async function (event: GetTransactionsEvent) {
  const { teamRepository, managerRepository } = event.context.repositories;
  const { teamId } = event.pathParameters;

  let managerId = getUser(event);

  // Check if the manager exists
  const manager = await managerRepository.getManagerById(managerId);
  if (!manager) {
    return buildResponse(404, JSON.stringify({ error: 'Manager not found' }));
  }

  const transactions = await teamRepository.getTransactions(
    Number(event.queryStringParameters?.days) || 14,
    teamId
  );
  if (!transactions) {
    return buildResponse(404, JSON.stringify({ error: 'Team not found' }));
  }

  return buildResponse(200, JSON.stringify({ transactions }));
};

export const handler = httpMiddify(main, { schema: getTransactionsSchema });
