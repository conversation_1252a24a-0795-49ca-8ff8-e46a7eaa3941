import { httpMiddify } from '@/middleware/rest/index.js';
import { HttpEvent } from '@/middleware/rest/types.js';
import { buildResponse } from '@/utils/buildResponse.js';
import { getUser } from '@/utils/getUser.js';

const TRAINING_COSTS = [
  100_000, 200_000, 350_000, 700_000, 1_300_000, 2_500_000, 4_700_000, 9_000_000, 17_000_000,
];

export const main = async function (event: HttpEvent<void, void, void>) {
  const { teamRepository, managerRepository } = event.context.repositories;

  const userId = getUser(event);
  const manager = await managerRepository.getManagerById(userId);
  if (!manager || !manager.team) {
    return buildResponse(404, JSON.stringify({ error: 'Manager or team not found' }));
  }

  const team = manager.team;

  const currentLevel = team.trainingLevel || 1;
  if (currentLevel >= 10) {
    return buildResponse(400, JSON.stringify({ error: 'Training is already at maximum level' }));
  }

  const nextLevel = currentLevel + 1;
  const cost = TRAINING_COSTS[currentLevel - 1]!;
  if (team.balance < cost) {
    return buildResponse(
      400,
      JSON.stringify({ error: 'Insufficient funds', required: cost, balance: team.balance })
    );
  }
  await teamRepository.incrementTrainingLevel(team, cost);

  return buildResponse(
    200,
    JSON.stringify({
      message: 'Training upgraded',
      trainingLevel: nextLevel,
      balance: team.balance - cost,
    })
  );
};

export const handler = httpMiddify(main, {});
