import createHttpEvent from '@/testing/createHttpEvent.js';
import { mockManagerRepository, mockTeamRepository } from '@/testing/mockRepositories.js';
import { buildResponse } from '@/utils/buildResponse.js';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { handler } from './upgradeTraining.js';

vi.mock('@/utils/getUser.js', () => ({
  getUser: vi.fn().mockReturnValue('user1'),
}));

describe('upgradeTraining', () => {
  const mockContext = {} as any;
  const baseTeam = {
    teamId: 'team1',
    gameworldId: 'gw1',
    trainingLevel: 1,
    balance: 200000,
  };
  const mockManager = {
    managerId: 'user1',
    team: { ...baseTeam },
    gameworldId: 'gw1',
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('returns 404 if manager or team not found', async () => {
    mockManagerRepository.getManagerById.mockResolvedValue(null);
    const event = createHttpEvent({});
    const response = await handler(event, mockContext);
    expect(response).toEqual(
      buildResponse(404, JSON.stringify({ error: 'Manager or team not found' }))
    );
  });

  it('returns 400 if already at max training level', async () => {
    mockManagerRepository.getManagerById.mockResolvedValue({
      ...mockManager,
      team: { ...baseTeam, trainingLevel: 10 },
    });
    const event = createHttpEvent({});
    const response = await handler(event, mockContext);
    expect(response).toEqual(
      buildResponse(400, JSON.stringify({ error: 'Training is already at maximum level' }))
    );
  });

  it('returns 400 if insufficient funds', async () => {
    mockManagerRepository.getManagerById.mockResolvedValue({
      ...mockManager,
      team: { ...baseTeam, balance: 50000 },
    });
    const event = createHttpEvent({});
    const response = await handler(event, mockContext);
    expect(response).toEqual(buildResponse(400, expect.stringContaining('Insufficient funds')));
  });

  it('calls incrementTrainingLevel and returns 200 on success', async () => {
    mockManagerRepository.getManagerById.mockResolvedValue({ ...mockManager });
    mockTeamRepository.incrementTrainingLevel.mockResolvedValue({});
    const event = createHttpEvent({});
    event.context = {
      repositories: {
        teamRepository: mockTeamRepository,
        managerRepository: mockManagerRepository,
      },
    };
    const response = await handler(event, mockContext);
    expect(mockTeamRepository.incrementTrainingLevel).toHaveBeenCalledWith(
      expect.objectContaining({ teamId: 'team1' }),
      100000
    );
    expect(response).toEqual(
      buildResponse(
        200,
        JSON.stringify({ message: 'Training upgraded', trainingLevel: 2, balance: 100000 })
      )
    );
  });
});
