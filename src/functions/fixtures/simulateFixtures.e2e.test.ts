import { Fixture } from '@/entities/Fixture.js';
import { Gameworld } from '@/entities/Gameworld.js';
import { League } from '@/entities/League.js';
import { LeagueRules } from '@/entities/LeagueRules.js';
import { Manager } from '@/entities/Manager.js';
import { Player } from '@/entities/Player.js';
import { PlayerAttributes } from '@/entities/PlayerAttributes.js';
import { PlayerMatchHistory } from '@/entities/PlayerMatchHistory.js';
import { PlayerOverallStats } from '@/entities/PlayerOverallStats.js';
import { Team } from '@/entities/Team.js';
import { Transactions } from '@/entities/Transactions.js';

import createSqsEvent from '@/testing/createSqsEvent.js';
import { SimulateFixturesEvent } from '@/types/generated/index.js';
import { logger } from '@/utils/logger.js';
import { Collection, MikroORM } from '@mikro-orm/core';
import { PostgreSqlDriver } from '@mikro-orm/postgresql';
import { v4 as uuidv4 } from 'uuid';
import { beforeAll, beforeEach, describe, expect, it, vi } from 'vitest';
import mikroOrmConfig from '../../mikro-orm.config.js';
import { handler } from './simulateFixtures.js';

describe('simulateFixtures E2E Test', () => {
  let orm: MikroORM<PostgreSqlDriver>;
  let gameworld: Gameworld;
  let league: League;
  let homeTeam: Team;
  let awayTeam: Team;
  let homeManager: Manager;
  let awayManager: Manager;
  let fixture: Fixture;
  let homePlayers: Player[];
  let awayPlayers: Player[];

  beforeAll(async () => {
    if (!process.env.DATABASE_URL?.includes('localhost')) {
      throw new Error('Tests aborted: DATABASE_URL must be localhost');
    }

    vi.restoreAllMocks();
    vi.resetModules();
    vi.unmock('../../storage-interface/database-initializer.js');
    await MikroORM.init<PostgreSqlDriver>(mikroOrmConfig)
      .then(async (_orm) => {
        orm = _orm;
        // Create schema
        await _orm.em.getConnection().execute('CREATE EXTENSION IF NOT EXISTS "uuid-ossp";');
        await _orm.schema.refreshDatabase({ dropDb: false });
      })
      .catch((error) => {
        logger.error('Failed to initialize database', { error });
      });
  });

  beforeEach(async () => {
    // Create test data
    await createTestData();
  });

  async function createTestData() {
    const em = orm.em.fork();

    // Create gameworld
    gameworld = new Gameworld();
    gameworld.id = uuidv4();
    gameworld.endDate = BigInt(Date.now() + 86400000); // 1 day from now
    gameworld.highestManageableTier = 3;
    em.persist(gameworld);

    // Create league
    league = new League();
    league.id = uuidv4();
    league.gameworld = gameworld;
    league.name = 'Test League';
    league.tier = 1;
    em.persist(league);

    // Create league rules
    const leagueRules = new LeagueRules();
    leagueRules.league = league;
    leagueRules.promotionSpots = 1;
    leagueRules.relegationSpots = 1;
    leagueRules.teamCount = 20;
    leagueRules.maximumPrize = 100000;
    leagueRules.minimumPrize = 10000;
    em.persist(leagueRules);

    // Create home team
    homeTeam = new Team();
    homeTeam.teamId = uuidv4();
    homeTeam.gameworldId = gameworld.id;
    homeTeam.league = league;
    homeTeam.tier = 1;
    homeTeam.teamName = 'Home Team FC';
    homeTeam.balance = 1000000;
    homeTeam.played = 5;
    homeTeam.points = 10;
    homeTeam.goalsFor = 8;
    homeTeam.goalsAgainst = 3;
    homeTeam.wins = 3;
    homeTeam.draws = 1;
    homeTeam.losses = 1;
    // Create players for both teams
    homePlayers = await createPlayersForTeam(em, homeTeam, 11);
    homeTeam.players = new Collection<Player>(homeTeam, homePlayers);
    em.persist(homeTeam);

    // Create away team
    awayTeam = new Team();
    awayTeam.teamId = uuidv4();
    awayTeam.gameworldId = gameworld.id;
    awayTeam.league = league;
    awayTeam.tier = 1;
    awayTeam.teamName = 'Away Team FC';
    awayTeam.balance = 800000;
    awayTeam.played = 0;
    awayTeam.points = 0;
    awayTeam.goalsFor = 0;
    awayTeam.goalsAgainst = 0;
    awayTeam.wins = 0;
    awayTeam.draws = 0;
    awayTeam.losses = 0;
    awayPlayers = await createPlayersForTeam(em, awayTeam, 11);
    awayTeam.players = new Collection<Player>(awayTeam, awayPlayers);
    em.persist(awayTeam);

    // Create managers
    homeManager = new Manager();
    homeManager.managerId = uuidv4();
    homeManager.createdAt = BigInt(Date.now() - 86400000);
    homeManager.lastActive = BigInt(Date.now());
    homeManager.firstName = 'Home';
    homeManager.lastName = 'Manager';
    homeManager.email = '<EMAIL>';
    homeManager.team = homeTeam;
    homeManager.gameworldId = gameworld.id;
    homeManager.wins = 10;
    homeManager.draws = 5;
    homeManager.defeats = 3;
    homeManager.goalsScored = 25;
    homeManager.goalsConceded = 15;
    em.persist(homeManager);

    awayManager = new Manager();
    awayManager.managerId = uuidv4();
    awayManager.createdAt = BigInt(Date.now() - 86400000);
    awayManager.lastActive = BigInt(Date.now());
    awayManager.firstName = 'Away';
    awayManager.lastName = 'Manager';
    awayManager.email = '<EMAIL>';
    awayManager.team = awayTeam;
    awayManager.gameworldId = gameworld.id;
    awayManager.wins = 0;
    awayManager.draws = 0;
    awayManager.defeats = 0;
    awayManager.goalsScored = 0;
    awayManager.goalsConceded = 0;
    em.persist(awayManager);

    // Create fixture
    fixture = new Fixture();
    fixture.fixtureId = uuidv4();
    fixture.gameworldId = gameworld.id;
    fixture.league = league;
    fixture.homeTeam = homeTeam;
    fixture.awayTeam = awayTeam;
    fixture.date = BigInt(Date.now());
    fixture.played = false;
    em.persist(fixture);

    await em.flush();
  }

  async function createPlayersForTeam(em: any, team: Team, count: number): Promise<Player[]> {
    const players: Player[] = [];

    for (let i = 1; i <= count; i++) {
      const player = new Player();
      player.playerId = uuidv4();
      player.gameworldId = team.gameworldId;
      player.team = team;
      player.age = 20 + (i % 15);
      player.seed = BigInt(Date.now() + i);
      player.firstName = `Player${i}`;
      player.surname = `Surname${i}`;
      player.value = 100000 + i * 10000;
      player.energy = 100;
      player.lastMatchPlayed = BigInt(Date.now() - 604800000); // 1 week ago
      player.suspendedForGames = 0;
      em.persist(player);

      // Create player attributes
      const attributes = new PlayerAttributes();
      attributes.player = player;
      attributes.isGoalkeeper = i === 1; // First player is goalkeeper

      // Set all required attributes
      const baseValue = 15 + (i % 10);
      attributes.paceCurrent = baseValue;
      attributes.pacePotential = baseValue + 5;
      attributes.finishingCurrent = baseValue;
      attributes.finishingPotential = baseValue + 5;
      attributes.passingCurrent = baseValue;
      attributes.passingPotential = baseValue + 5;
      attributes.tacklingCurrent = baseValue;
      attributes.tacklingPotential = baseValue + 5;
      attributes.reflexesCurrent = i === 1 ? 20 : 5; // Goalkeeper has high reflexes
      attributes.reflexesPotential = attributes.reflexesCurrent + 5;
      attributes.positioningCurrent = baseValue;
      attributes.positioningPotential = baseValue + 5;
      attributes.shotStoppingCurrent = i === 1 ? 20 : 5; // Goalkeeper has high shot stopping
      attributes.shotStoppingPotential = attributes.shotStoppingCurrent + 5;
      attributes.markingCurrent = baseValue;
      attributes.markingPotential = baseValue + 5;
      attributes.headingCurrent = baseValue;
      attributes.headingPotential = baseValue + 5;
      attributes.crossingCurrent = baseValue;
      attributes.crossingPotential = baseValue + 5;
      attributes.visionCurrent = baseValue;
      attributes.visionPotential = baseValue + 5;
      attributes.ballControlCurrent = baseValue;
      attributes.ballControlPotential = baseValue + 5;
      attributes.stamina = 0.8;
      em.persist(attributes);

      players.push(player);
    }

    return players;
  }

  it('should successfully simulate a fixture and update all database entities', async () => {
    // Arrange
    const fixtureEvent: SimulateFixturesEvent = {
      fixtureId: fixture.fixtureId,
      gameworldId: gameworld.id,
      leagueId: league.id,
      homeTeamId: homeTeam.teamId,
      awayTeamId: awayTeam.teamId,
      fixtureDate: Number(fixture.date),
    };

    const sqsEvent = createSqsEvent([
      {
        body: fixtureEvent,
      },
    ]) as any;

    // Get initial state for comparison
    let em = orm.em.fork();
    const initialHomeTeam = await em.findOneOrFail(Team, { teamId: homeTeam.teamId });
    const initialAwayTeam = await em.findOneOrFail(Team, { teamId: awayTeam.teamId });
    const initialHomeManager = await em.findOneOrFail(Manager, {
      managerId: homeManager.managerId,
    });
    const initialAwayManager = await em.findOneOrFail(Manager, {
      managerId: awayManager.managerId,
    });

    // Act
    const result = await handler(sqsEvent, {} as any);

    // Assert - No batch failures
    expect(result).toBeDefined();
    expect(result!.batchItemFailures).toHaveLength(0);

    em = orm.em.fork();
    // Verify fixture was updated
    const updatedFixture = await em.findOneOrFail(Fixture, { fixtureId: fixture.fixtureId });
    expect(updatedFixture.played).toBe(true);
    expect(updatedFixture.score).toBeDefined();
    expect(updatedFixture.score).toHaveLength(2);
    expect(updatedFixture.simulatedAt).toBeDefined();
    expect(updatedFixture.seed).toBeDefined();

    // Verify team standings were updated
    const updatedHomeTeam = await em.findOneOrFail(Team, { teamId: homeTeam.teamId });
    const updatedAwayTeam = await em.findOneOrFail(Team, { teamId: awayTeam.teamId });

    expect(updatedHomeTeam.played).toBe(initialHomeTeam.played + 1);
    expect(updatedAwayTeam.played).toBe(initialAwayTeam.played + 1);

    // Goals should be updated based on the score
    const homeScore = Number(updatedFixture.score![0]);
    const awayScore = Number(updatedFixture.score![1]);

    expect(updatedHomeTeam.goalsFor).toBe(initialHomeTeam.goalsFor + homeScore);
    expect(updatedHomeTeam.goalsAgainst).toBe(initialHomeTeam.goalsAgainst + awayScore);
    expect(updatedAwayTeam.goalsFor).toBe(initialAwayTeam.goalsFor + awayScore);
    expect(updatedAwayTeam.goalsAgainst).toBe(initialAwayTeam.goalsAgainst + homeScore);

    // Verify manager stats were updated
    const updatedHomeManager = await em.findOneOrFail(Manager, {
      managerId: homeManager.managerId,
    });
    const updatedAwayManager = await em.findOneOrFail(Manager, {
      managerId: awayManager.managerId,
    });

    expect(updatedHomeManager.goalsScored).toBe(initialHomeManager.goalsScored + homeScore);
    expect(updatedHomeManager.goalsConceded).toBe(initialHomeManager.goalsConceded + awayScore);
    expect(updatedAwayManager.goalsScored).toBe(initialAwayManager.goalsScored + awayScore);
    expect(updatedAwayManager.goalsConceded).toBe(initialAwayManager.goalsConceded + homeScore);

    // Verify win/draw/loss stats
    if (homeScore > awayScore) {
      expect(updatedHomeManager.wins).toBe(initialHomeManager.wins + 1);
      expect(updatedAwayManager.defeats).toBe(initialAwayManager.defeats + 1);
    } else if (awayScore > homeScore) {
      expect(updatedAwayManager.wins).toBe(initialAwayManager.wins + 1);
      expect(updatedHomeManager.defeats).toBe(initialHomeManager.defeats + 1);
    } else {
      expect(updatedHomeManager.draws).toBe(initialHomeManager.draws + 1);
      expect(updatedAwayManager.draws).toBe(initialAwayManager.draws + 1);
    }

    // Verify player match history was created
    const matchHistoryCount = await em.count(PlayerMatchHistory, {
      fixtureId: fixture.fixtureId,
    });
    expect(matchHistoryCount).toBeGreaterThan(0);

    // Verify player overall stats were updated
    const overallStatsCount = await em.count(PlayerOverallStats, {});
    expect(overallStatsCount).toBeGreaterThan(0);

    // Verify transactions were created (for match income/expenses)
    const transactionsCount = await em.count(Transactions, {});
    expect(transactionsCount).toBeGreaterThan(0);

    // Verify players' lastMatchPlayed was updated
    const updatedHomePlayers = await em.find(Player, { team: homeTeam });
    for (const player of updatedHomePlayers) {
      expect(player.lastMatchPlayed).toBeGreaterThanOrEqual(fixture.date);
    }
  });

  it('should update only the home manager stats if only the home team has a manager', async () => {
    // Remove away manager
    let em = orm.em.fork();
    await em.removeAndFlush(awayManager);

    const fixtureEvent: SimulateFixturesEvent = {
      fixtureId: fixture.fixtureId,
      gameworldId: gameworld.id,
      leagueId: league.id,
      homeTeamId: homeTeam.teamId,
      awayTeamId: awayTeam.teamId,
      fixtureDate: Number(fixture.date),
    };
    const sqsEvent = createSqsEvent([{ body: fixtureEvent }]) as any;

    // Get initial state
    const initialHomeManager = await em.findOneOrFail(Manager, {
      managerId: homeManager.managerId,
    });
    const initialHomeTeam = await em.findOneOrFail(Team, { teamId: homeTeam.teamId });
    const initialAwayTeam = await em.findOneOrFail(Team, { teamId: awayTeam.teamId });

    // Act
    await handler(sqsEvent, {} as any);

    em = orm.em.fork();
    const updatedFixture = await em.findOneOrFail(Fixture, { fixtureId: fixture.fixtureId });
    const updatedHomeManager = await em.findOneOrFail(Manager, {
      managerId: homeManager.managerId,
    });
    const homeScore = Number(updatedFixture.score![0]);
    const awayScore = Number(updatedFixture.score![1]);

    // Only home manager stats should be updated
    expect(updatedHomeManager.goalsScored).toBe(initialHomeManager.goalsScored + homeScore);
    expect(updatedHomeManager.goalsConceded).toBe(initialHomeManager.goalsConceded + awayScore);
    // Away manager should not exist
    expect(await em.findOne(Manager, { managerId: awayManager.managerId })).toBeNull();
  });

  it('should update only the away manager stats if only the away team has a manager', async () => {
    // Remove home manager
    let em = orm.em.fork();
    await em.removeAndFlush(homeManager);

    const fixtureEvent: SimulateFixturesEvent = {
      fixtureId: fixture.fixtureId,
      gameworldId: gameworld.id,
      leagueId: league.id,
      homeTeamId: homeTeam.teamId,
      awayTeamId: awayTeam.teamId,
      fixtureDate: Number(fixture.date),
    };
    const sqsEvent = createSqsEvent([{ body: fixtureEvent }]) as any;

    // Get initial state
    const initialAwayManager = await em.findOneOrFail(Manager, {
      managerId: awayManager.managerId,
    });

    // Act
    await handler(sqsEvent, {} as any);

    em = orm.em.fork();
    const updatedFixture = await em.findOneOrFail(Fixture, { fixtureId: fixture.fixtureId });
    const updatedAwayManager = await em.findOneOrFail(Manager, {
      managerId: awayManager.managerId,
    });
    const homeScore = Number(updatedFixture.score![0]);
    const awayScore = Number(updatedFixture.score![1]);

    // Only away manager stats should be updated
    expect(updatedAwayManager.goalsScored).toBe(initialAwayManager.goalsScored + awayScore);
    expect(updatedAwayManager.goalsConceded).toBe(initialAwayManager.goalsConceded + homeScore);
    // Home manager should not exist
    expect(await em.findOne(Manager, { managerId: homeManager.managerId })).toBeNull();
  });
});
