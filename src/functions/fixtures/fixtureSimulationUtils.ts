import { Player } from '@/entities/Player.js';
import { SQS } from '@/services/sqs/sqs.js';
import { IFixtureRepository } from '@/storage-interface/fixtures/index.js';
import { SimulateFixturesEvent } from '@/types/generated/index.js';
import { logger } from '@/utils/logger.js';
import { jsonStringifySafe } from '@/utils/misc.js';
import { tracer } from '@/utils/tracer.js';

const sqs = new SQS({ tracer: tracer });

// Multiply the players stamina by this and add this many energy points * hours since last match
export const STAMINA_SCALAR = 4;

export function updatePlayerEnergy(players: Player[]) {
  for (const player of players) {
    const hoursSinceLastMatch = (Date.now() - Number(player.lastMatchPlayed)) / (1000 * 60 * 60);
    const staminaScalar = player.attributes.stamina * STAMINA_SCALAR;
    player.energy = Math.min(100, player.energy + staminaScalar * hoursSinceLastMatch);

    // Clear expired injuries before the match starts
    if (player.injuredUntil && player.injuredUntil < Date.now()) {
      player.injuredUntil = undefined;
    }
  }
}

/**
 * Processes next batch of fixtures for a specific gameworld and league by date
 * This is a debug method called to save us waiting for the scheduled lambda
 */
export async function debugProcessNextFixtures(
  fixtureRepository: IFixtureRepository,
  gameworldId: string,
  leagueId: string
): Promise<SimulateFixturesEvent[]> {
  const unplayedFixtures = await fixtureRepository.getAllUnplayedFixtures(gameworldId, leagueId);

  // If no unplayed fixtures, return empty array
  if (unplayedFixtures.length === 0) {
    logger.debug('No unplayed fixtures found', { gameworldId, leagueId });
    return [];
  }

  // get first date and filter to get all fixtures with the same date
  const firstDate = unplayedFixtures[0]!.date;
  const fixturesToSimulate = unplayedFixtures.filter((fixture) => fixture.date === firstDate);

  logger.debug(`Sending ${fixturesToSimulate.length} fixtures to SQS`, {
    fixtures: fixturesToSimulate.map((f) => f.fixtureId),
    gameworldId,
    leagueId,
  });

  return fixturesToSimulate.map((f) => ({
    gameworldId: f.gameworldId,
    leagueId: f.league.id,
    fixtureId: f.fixtureId,
    homeTeamId: f.homeTeam.teamId,
    awayTeamId: f.awayTeam.teamId,
    fixtureDate: Number(f.date),
  }));
}

/**
 * Sends fixtures to the SQS queue for simulation
 */
export async function sendFixturesToQueue(fixtures: SimulateFixturesEvent[]): Promise<void> {
  if (!process.env.QUEUE_URL) {
    throw new Error('QUEUE_URL environment variable not set');
  }

  logger.debug(`Sending ${fixtures.length} fixtures to SQS`);

  await sqs.sendBatch(
    process.env.QUEUE_URL,
    fixtures.map((f) => ({
      Id: f.fixtureId,
      MessageBody: jsonStringifySafe(f),
    }))
  );
}
