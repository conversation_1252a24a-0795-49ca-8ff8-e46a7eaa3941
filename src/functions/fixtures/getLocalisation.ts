import { httpMiddify } from '@/middleware/rest/index.js';
import { HttpEvent } from '@/middleware/rest/types.js';
import { buildResponse } from '@/utils/buildResponse.js';
import { APIGatewayProxyResult } from 'aws-lambda';
import { readFileSync } from 'fs';
import { dirname, join } from 'path';
import { fileURLToPath } from 'url';

// Polyfill __dirname for ESM and CJS in a TypeScript-safe way
const __dirname: string =
  typeof fileURLToPath === 'function' && typeof import.meta !== 'undefined'
    ? dirname(fileURLToPath(import.meta.url))
    : (typeof global !== 'undefined' && (global as any).__dirname) || '';

export const main = function (_: HttpEvent<void, void, void>): Promise<APIGatewayProxyResult> {
  const csv = readFileSync(join(__dirname, 'commentary.csv'), 'utf8');
  // convert csv to json, skip header row and remove carriage returns
  const result = csv
    .split('\n')
    .slice(1) // Skip header row
    .map((line) => line.split(','))
    .reduce(
      (acc, [id, text]) => {
        if (id && text) {
          // Only process if both id and text exist
          acc[id.trim()] = text.trim(); // Trim removes both \r and whitespace
        }
        return acc;
      },
      {} as Record<string, string>
    );

  return Promise.resolve(buildResponse(200, JSON.stringify(result)));
};

export const handler = httpMiddify(main, {});
