import { Fixture } from '@/entities/Fixture.js';
import { SQS } from '@/services/sqs/sqs.js';
import { LeagueFactory } from '@/testing/factories/leagueFactory.ts';
import { PlayerFactory } from '@/testing/factories/playerFactory.js';
import { TeamsFactory } from '@/testing/factories/teamFactory.js';
import { SimulateFixturesEvent } from '@/types/generated/index.js';
import { SendMessageBatchRequestEntry, SendMessageCommandInput } from '@aws-sdk/client-sqs';
import { afterEach, beforeEach, describe, expect, it, MockInstance, vi } from 'vitest';
import {
  debugProcessNextFixtures,
  sendFixturesToQueue,
  updatePlayerEnergy,
} from './fixtureSimulationUtils.js';

describe('updatePlayerEnergy', () => {
  const lastMatchPlayed = Date.now() - 1000 * 60 * 60 * 14; // 14 hours ago
  const mockPlayers = PlayerFactory.batch(5, {
    energy: 50,
    lastMatchPlayed: BigInt(lastMatchPlayed),
  });
  it('should update player energy correctly', () => {
    const energiesBefore = mockPlayers.map((player) => player.energy);
    updatePlayerEnergy(mockPlayers);
    const energiesAfter = mockPlayers.map((player) => player.energy);
    for (let i = 0; i < energiesBefore.length; i++) {
      expect(energiesAfter[i]).toBeGreaterThanOrEqual(energiesBefore[i] ?? 0);
    }
  });
});

describe('sendFixturesToQueue', () => {
  let fixtures: SimulateFixturesEvent[];
  let sendSpy: MockInstance<
    (
      queueUrl: string,
      entries: SendMessageBatchRequestEntry[],
      additionalOpts?: Partial<SendMessageCommandInput>
    ) => Promise<void>
  >;

  beforeEach(() => {
    vi.clearAllMocks();
    vi.useFakeTimers();
    vi.setSystemTime(new Date(1741165200000));
    process.env.QUEUE_URL = 'https://sqs.queue.url';
    sendSpy = vi.spyOn(SQS.prototype, 'sendBatch').mockResolvedValue();

    fixtures = [
      {
        fixtureId: 'fixture1',
        gameworldId: 'gameworld1',
        leagueId: 'league1',
        homeTeamId: 'homeTeam1',
        awayTeamId: 'awayTeam1',
        fixtureDate: Date.now(),
      },
    ];
  });
  afterEach(() => {
    vi.useRealTimers();
  });

  it('sends valid fixtures to SQS', async () => {
    await sendFixturesToQueue(fixtures);
    expect(sendSpy).toHaveBeenCalledTimes(1);
    expect(sendSpy).toHaveBeenCalledWith(process.env.QUEUE_URL, [
      {
        Id: 'fixture1',
        MessageBody: JSON.stringify({
          fixtureId: 'fixture1',
          gameworldId: 'gameworld1',
          leagueId: 'league1',
          homeTeamId: 'homeTeam1',
          awayTeamId: 'awayTeam1',
          fixtureDate: 1741165200000,
        }),
      },
    ]);
  });

  it('throws an error if QUEUE_URL is not set', async () => {
    delete process.env.QUEUE_URL;
    await expect(sendFixturesToQueue(fixtures)).rejects.toThrow(
      'QUEUE_URL environment variable not set'
    );
  });
});

describe('debugProcessNextFixtures', () => {
  const mockFixtureRepository = {
    getDueFixtures: vi.fn(),
    getAllUnplayedFixtures: vi.fn(),
  } as any;

  const fixtures: Fixture[] = [
    {
      fixtureId: 'fixture1',
      gameworldId: 'gameworld1',
      league: LeagueFactory.build({ id: 'league1' }),
      homeTeam: TeamsFactory.build({ teamId: 'homeTeam1' }),
      awayTeam: TeamsFactory.build({ teamId: 'awayTeam1' }),
      date: 1741165200000n,
      played: false,
    },
    {
      fixtureId: 'fixture2',
      gameworldId: 'gameworld1',
      league: LeagueFactory.build({ id: 'league2' }),
      homeTeam: TeamsFactory.build({ teamId: 'homeTeam2' }),
      awayTeam: TeamsFactory.build({ teamId: 'awayTeam2' }),
      date: 1741165200000n,
      played: false,
    },
  ];

  beforeEach(() => {
    vi.useFakeTimers();
    vi.setSystemTime(new Date(1741165200000));
  });
  afterEach(() => {
    vi.useRealTimers();
  });

  it('returns unplayed fixtures sorted by date', async () => {
    mockFixtureRepository.getAllUnplayedFixtures.mockResolvedValue(fixtures);

    const result = await debugProcessNextFixtures(mockFixtureRepository, 'gameworld1', 'league1');
    expect(result).toMatchSnapshot();
  });

  it('returns an empty array if no unplayed fixtures are found', async () => {
    mockFixtureRepository.getAllUnplayedFixtures.mockResolvedValue([]);
    const result = await debugProcessNextFixtures(mockFixtureRepository, 'gameworld1', 'league1');
    expect(result).toEqual([]);
  });
});
