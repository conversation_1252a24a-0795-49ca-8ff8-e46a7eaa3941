import { handler } from '@/functions/fixtures/simulateFixtures.js';
import { SQSEvent } from '@/middleware/sqs/types.js';
import { TransactionService } from '@/services/database/transaction-service.js';
import { FixtureDatabaseService } from '@/services/fixtures/fixture-database-service.js';
import { FixtureSimulationService } from '@/services/fixtures/fixture-simulation-service.js';
import { getMikroOrmService } from '@/storage-interface/database-initializer.js';
import { ManagerFactory } from '@/testing/factories/managerFactory.js';
import { TeamsFactory } from '@/testing/factories/teamFactory.js';
import { createMockRepositories } from '@/testing/mockRepositories.js';
import { SimulateFixturesEvent } from '@/types/generated/index.js';
import { setRandomSeed } from '@/utils/seeded-random.js';
import { beforeEach, describe, expect, it, vi } from 'vitest';

// Mock the services
vi.mock('@/services/database/transaction-service.js');
vi.mock('@/services/fixtures/fixture-simulation-service.js');
vi.mock('@/services/fixtures/fixture-database-service.js');
vi.mock('@/storage-interface/database-initializer.js');

// Mock the SQS middleware
vi.mock('@/middleware/sqs/index.js', () => ({
  sqsMiddify: vi.fn().mockImplementation((handler) => handler),
}));

describe('simulateFixtures handler', () => {
  let mockTransactionService: any;
  let mockSimulationService: any;
  let mockDatabaseService: any;
  let mockMikroOrmService: any;

  beforeEach(() => {
    setRandomSeed(0.5);

    // Setup mocks
    mockTransactionService = {
      executeInTransaction: vi.fn(),
    };

    mockSimulationService = {
      simulateFixture: vi.fn(),
      sendMatchNotifications: vi.fn(),
    };

    mockDatabaseService = {
      updateFixtureResults: vi.fn(),
      storeFixtureDetails: vi.fn(),
    };

    mockMikroOrmService = {
      getEntityManager: vi.fn(),
    };

    // Mock constructors
    vi.mocked(TransactionService).mockImplementation(() => mockTransactionService);
    vi.mocked(FixtureSimulationService).mockImplementation(() => mockSimulationService);
    vi.mocked(FixtureDatabaseService).mockImplementation(() => mockDatabaseService);
    vi.mocked(getMikroOrmService).mockResolvedValue(mockMikroOrmService);
  });

  it('should successfully process a batch of fixtures', async () => {
    // Arrange
    const fixture1: SimulateFixturesEvent = {
      fixtureId: 'fixture-1',
      gameworldId: 'gameworld-1',
      leagueId: 'league-1',
      homeTeamId: 'home-team-1',
      awayTeamId: 'away-team-1',
      fixtureDate: Date.now(),
    };

    const fixture2: SimulateFixturesEvent = {
      fixtureId: 'fixture-2',
      gameworldId: 'gameworld-1',
      leagueId: 'league-1',
      homeTeamId: 'home-team-2',
      awayTeamId: 'away-team-2',
      fixtureDate: Date.now(),
    };

    const event: SQSEvent<SimulateFixturesEvent> = {
      Records: [
        { messageId: 'msg-1', body: fixture1 },
        { messageId: 'msg-2', body: fixture2 },
      ],
      context: {
        repositories: createMockRepositories(),
      },
    } as any;

    const simulationData = {
      fixture: fixture1,
      homeTeam: TeamsFactory.build({ manager: undefined }),
      awayTeam: TeamsFactory.build({ manager: undefined }),
      seed: 12345,
      result: {
        stats: { score: [2, 1] },
        events: [],
      },
      homeTeamConverted: { players: [] },
      awayTeamConverted: { players: [] },
    };

    mockSimulationService.simulateFixture.mockResolvedValue(simulationData);
    mockTransactionService.executeInTransaction.mockImplementation(async (callback: any) => {
      return await callback({});
    });

    // Act
    const result = await handler(event, {} as any);

    // Assert
    expect(result?.batchItemFailures).toHaveLength(0);
    expect(mockSimulationService.simulateFixture).toHaveBeenCalledTimes(2);
    expect(mockTransactionService.executeInTransaction).toHaveBeenCalledTimes(2);
    expect(mockDatabaseService.updateFixtureResults).toHaveBeenCalledTimes(2);
  });

  it('should handle fixture simulation failure and add to batch failures', async () => {
    // Arrange
    const fixture: SimulateFixturesEvent = {
      fixtureId: 'fixture-1',
      gameworldId: 'gameworld-1',
      leagueId: 'league-1',
      homeTeamId: 'home-team-1',
      awayTeamId: 'away-team-1',
      fixtureDate: Date.now(),
    };

    const event: SQSEvent<SimulateFixturesEvent> = {
      Records: [{ messageId: 'msg-1', body: fixture }],
      context: {
        repositories: createMockRepositories(),
      },
    } as any;

    mockSimulationService.simulateFixture.mockRejectedValue(new Error('Simulation failed'));

    // Act
    const result = await handler(event, {} as any);

    // Assert
    expect(result?.batchItemFailures).toHaveLength(1);
    expect(result?.batchItemFailures[0]!.itemIdentifier).toBe('msg-1');
  });

  it('should handle transaction failure and add to batch failures', async () => {
    // Arrange
    const fixture: SimulateFixturesEvent = {
      fixtureId: 'fixture-1',
      gameworldId: 'gameworld-1',
      leagueId: 'league-1',
      homeTeamId: 'home-team-1',
      awayTeamId: 'away-team-1',
      fixtureDate: Date.now(),
    };

    const event: SQSEvent<SimulateFixturesEvent> = {
      Records: [{ messageId: 'msg-1', body: fixture }],
      context: {
        repositories: createMockRepositories(),
      },
    } as any;

    const simulationData = {
      fixture: fixture,
      homeTeam: TeamsFactory.build({ manager: undefined }),
      awayTeam: TeamsFactory.build({ manager: undefined }),
      seed: 12345,
      result: {
        stats: { score: [2, 1] },
        events: [],
      },
      homeTeamConverted: { players: [] },
      awayTeamConverted: { players: [] },
    };

    mockSimulationService.simulateFixture.mockResolvedValue(simulationData);
    mockTransactionService.executeInTransaction.mockRejectedValue(new Error('Transaction failed'));

    // Act
    const result = await handler(event, {} as any);

    // Assert
    expect(result?.batchItemFailures).toHaveLength(1);
    expect(result?.batchItemFailures[0]!.itemIdentifier).toBe('msg-1');
  });

  it('should store fixture details for fixtures with human managers', async () => {
    // Arrange
    const fixture: SimulateFixturesEvent = {
      fixtureId: 'fixture-1',
      gameworldId: 'gameworld-1',
      leagueId: 'league-1',
      homeTeamId: 'home-team-1',
      awayTeamId: 'away-team-1',
      fixtureDate: Date.now(),
    };

    const event: SQSEvent<SimulateFixturesEvent> = {
      Records: [{ messageId: 'msg-1', body: fixture }],
      context: {
        repositories: createMockRepositories(),
      },
    } as any;

    const simulationData = {
      fixture: fixture,
      homeTeam: TeamsFactory.build({
        manager: ManagerFactory.build({ managerId: 'manager-1' }),
        teamId: 'home-team-1',
        teamName: 'Home Team',
      }),
      awayTeam: TeamsFactory.build({
        manager: undefined,
        teamId: 'away-team-1',
        teamName: 'Away Team',
      }),
      seed: 12345,
      result: {
        stats: { score: [2, 1] },
        events: [],
      },
      homeTeamConverted: { players: [] },
      awayTeamConverted: { players: [] },
    };

    mockSimulationService.simulateFixture.mockResolvedValue(simulationData);
    mockTransactionService.executeInTransaction.mockImplementation(async (callback: any) => {
      return await callback({});
    });

    // Act
    await handler(event, {} as any);

    // Assert
    expect(mockDatabaseService.storeFixtureDetails).toHaveBeenCalledWith(
      'fixture-1',
      'gameworld-1',
      'league-1',
      'home-team-1',
      'Home Team',
      'away-team-1',
      'Away Team',
      expect.any(Number),
      { score: [2, 1] },
      [],
      [],
      []
    );
  });
});
