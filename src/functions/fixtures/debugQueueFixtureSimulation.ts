import {
  debugProcessNextFixtures,
  sendFixturesToQueue,
} from '@/functions/fixtures/fixtureSimulationUtils.js';
import { httpMiddify } from '@/middleware/rest/index.js';
import { HttpEvent } from '@/middleware/rest/types.js';
import { SimulateFixturesEvent } from '@/types/generated/index.js';
import { buildResponse } from '@/utils/buildResponse.js';
import { logger } from '@/utils/logger.js';

interface PathParameters {
  gameworldId: string;
  leagueId: string;
}

interface QueryParameters {
  fixtureId?: string;
}

export type DebugQueueFixtureSimulationEvent = HttpEvent<void, PathParameters, QueryParameters>;

export const main = async function (event: DebugQueueFixtureSimulationEvent) {
  const { fixtureRepository } = event.context.repositories;

  logger.debug('Debug queue fixture simulation', {
    gameworldId: event.pathParameters.gameworldId,
    leagueId: event.pathParameters.leagueId,
    fixtureId: event.queryStringParameters?.fixtureId,
  });

  let fixturesToSimulate: SimulateFixturesEvent[];
  if (event.queryStringParameters?.fixtureId) {
    const fixture = await fixtureRepository.getFixture(event.pathParameters.gameworldId);
    if (!fixture) {
      return buildResponse(404, JSON.stringify({ error: 'Fixture not found' }));
    }
    fixturesToSimulate = [
      {
        fixtureId: fixture.fixtureId,
        gameworldId: fixture.gameworldId,
        leagueId: fixture.leagueId,
        homeTeamId: fixture.homeTeamId,
        awayTeamId: fixture.awayTeamId,
        fixtureDate: fixture.date,
      },
    ];
  } else {
    fixturesToSimulate = await debugProcessNextFixtures(
      fixtureRepository,
      event.pathParameters.gameworldId,
      event.pathParameters.leagueId
    );
    logger.debug('Fixtures to simulate', { fixturesToSimulate });
  }

  await sendFixturesToQueue(fixturesToSimulate);

  const response = buildResponse(200, JSON.stringify({ fixturesToSimulate }));
  return Promise.resolve(response);
};
export const handler = httpMiddify(main, {});
