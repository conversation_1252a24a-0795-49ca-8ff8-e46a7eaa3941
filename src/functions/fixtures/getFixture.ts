import { httpMiddify } from '@/middleware/rest/index.js';
import { HttpEvent } from '@/middleware/rest/types.js';
import { FixtureDetails } from '@/model/fixture.js';
import DynamoDbService from '@/services/database/dynamo/dynamo-db-service.js';
import { buildResponse } from '@/utils/buildResponse.js';

interface PathParameters {
  gameworldId: string;
  leagueId: string;
  fixtureId: string;
}

export type GetFixtureEvent = HttpEvent<void, PathParameters, void>;
const dynamoDb = new DynamoDbService();

export const main = async function (event: GetFixtureEvent) {
  const fixture = await dynamoDb.get<FixtureDetails>(process.env.FIXTURE_DETAIL_TABLE_NAME!, {
    fixtureId: event.pathParameters.fixtureId,
  });

  if (!fixture) {
    return buildResponse(404, JSON.stringify({ error: 'Fixture not found' }));
  }

  return buildResponse(200, JSON.stringify(fixture));
};

export const handler = httpMiddify(main, { injectRepositories: false });
