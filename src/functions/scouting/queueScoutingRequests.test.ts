import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';

import { EventWithRepositories } from '@/middleware/event/types.js';
import { SNS } from '@/services/sns/index.js';
import {
  mockScoutingRequestRepository,
  resetAllRepositoryMocks,
} from '@/testing/mockRepositories.js';
import { ScheduledEvent } from 'aws-lambda';
import { handler } from './queueScoutingRequests.js';

describe('Queue Scouting Requests Lambda', () => {
  const mockContext = {} as any;
  const publishBatchMock = vi.fn().mockResolvedValue({});

  const mockRequests = [
    {
      requestId: 'request-1',
      type: 'player',
      targetId: 'player-1',
      team: { teamId: 'team-1' },
      gameworldId: 'gameworld-1',
      processAfter: Date.now() - 1000,
    },
    {
      requestId: 'request-2',
      type: 'team',
      targetId: 'team-2',
      team: { teamId: 'team-1' },
      gameworldId: 'gameworld-1',
      processAfter: Date.now() - 500, // Ready to process
    },
  ];

  beforeEach(() => {
    vi.clearAllMocks();
    resetAllRepositoryMocks();
    process.env.SCOUTING_TOPIC_ARN = 'test-topic-arn';

    // Set up SNS mock
    vi.spyOn(SNS.prototype, 'publishBatch').mockImplementation(publishBatchMock);

    // Set up repository mock
    mockScoutingRequestRepository.getPendingScoutingRequests.mockResolvedValue(mockRequests);
    mockScoutingRequestRepository.markScoutingRequestAsProcessed.mockResolvedValue({} as any);
  });

  afterEach(() => {
    vi.clearAllMocks();
    delete process.env.SCOUTING_TOPIC_ARN;
  });

  it('should process ready requests and publish to SNS', async () => {
    // Create event with repositories
    const event = {
      context: {
        repositories: {
          scoutingRequestRepository: mockScoutingRequestRepository,
        },
      },
    } as unknown as ScheduledEvent & EventWithRepositories;

    await handler(event, mockContext);

    // Verify repository method was called
    expect(mockScoutingRequestRepository.getPendingScoutingRequests).toHaveBeenCalledWith(100);

    // Verify SNS publish was called with correct entries
    expect(publishBatchMock).toHaveBeenCalledWith('test-topic-arn', [
      {
        Id: '0',
        Message: expect.stringContaining('request-1'),
        MessageAttributes: {
          DataType: { DataType: 'String', StringValue: 'player' },
        },
      },
      {
        Id: '1',
        Message: expect.stringContaining('request-2'),
        MessageAttributes: {
          DataType: { DataType: 'String', StringValue: 'team' },
        },
      },
    ]);

    // Verify requests were marked as processed
    expect(mockScoutingRequestRepository.markScoutingRequestAsProcessed).toHaveBeenCalledTimes(2);
    expect(mockScoutingRequestRepository.markScoutingRequestAsProcessed).toHaveBeenCalledWith(
      'request-1'
    );
    expect(mockScoutingRequestRepository.markScoutingRequestAsProcessed).toHaveBeenCalledWith(
      'request-2'
    );
  });

  it('should handle empty request list', async () => {
    // Mock empty request list
    mockScoutingRequestRepository.getPendingScoutingRequests.mockResolvedValue([]);

    // Create event with repositories
    const event = {
      context: {
        repositories: {
          scoutingRequestRepository: mockScoutingRequestRepository,
        },
      },
    } as unknown as ScheduledEvent & EventWithRepositories;

    await handler(event, mockContext);

    // Verify SNS publish was not called
    expect(publishBatchMock).not.toHaveBeenCalled();
    expect(mockScoutingRequestRepository.markScoutingRequestAsProcessed).not.toHaveBeenCalled();
  });

  it('should process requests in batches of 10', async () => {
    // Create 15 mock requests
    const largeMockRequests = Array.from({ length: 15 }, (_, i) => ({
      requestId: `request-${i}`,
      type: 'player',
      targetId: `player-${i}`,
      team: { teamId: 'team-1' },
      gameworldId: 'gameworld-1',
      processAfter: Date.now() - 1000,
    }));

    mockScoutingRequestRepository.getPendingScoutingRequests.mockResolvedValue(largeMockRequests);

    // Create event with repositories
    const event = {
      context: {
        repositories: {
          scoutingRequestRepository: mockScoutingRequestRepository,
        },
      },
    } as unknown as ScheduledEvent & EventWithRepositories;

    await handler(event, mockContext);

    // Should have called publishBatch twice (10 items + 5 items)
    expect(publishBatchMock).toHaveBeenCalledTimes(2);

    // First batch should have 10 items
    expect(publishBatchMock.mock.calls[0]![1]).toHaveLength(10);
    // Second batch should have 5 items
    expect(publishBatchMock.mock.calls[1]![1]).toHaveLength(5);

    // Verify all requests were marked as processed
    expect(mockScoutingRequestRepository.markScoutingRequestAsProcessed).toHaveBeenCalledTimes(15);
  });

  it('should throw error if topic ARN is not set', async () => {
    delete process.env.SCOUTING_TOPIC_ARN;

    // Create event with repositories
    const event = {
      context: {
        repositories: {
          scoutingRequestRepository: mockScoutingRequestRepository,
        },
      },
    } as unknown as ScheduledEvent & EventWithRepositories;

    await expect(handler(event, mockContext)).rejects.toThrow(
      'SCOUTING_TOPIC_ARN environment variable not set'
    );
  });

  it('should handle SNS publish failures', async () => {
    // Mock SNS publish failure
    publishBatchMock.mockRejectedValue(new Error('SNS publish failed'));

    // Create event with repositories
    const event = {
      context: {
        repositories: {
          scoutingRequestRepository: mockScoutingRequestRepository,
        },
      },
    } as unknown as ScheduledEvent & EventWithRepositories;

    await expect(handler(event, mockContext)).rejects.toThrow('SNS publish failed');

    // Verify no requests were marked as processed
    expect(mockScoutingRequestRepository.markScoutingRequestAsProcessed).not.toHaveBeenCalled();
  });
});
