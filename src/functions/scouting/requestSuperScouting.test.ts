import { Manager } from '@/entities/Manager.js';
import { Team } from '@/entities/Team.js';
import { TransactionType } from '@/storage-interface/teams/index.js';
import createHttpEvent from '@/testing/createHttpEvent.js';
import { ManagerFactory } from '@/testing/factories/managerFactory.js';
import { TeamsFactory } from '@/testing/factories/teamFactory.js';
import { mockManagerRepository, mockTeamRepository } from '@/testing/mockRepositories.js';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { handler } from './requestSuperScouting.js';

vi.mock('@/utils/getUser', () => ({
  getUser: vi.fn().mockReturnValue('test-user-id'),
}));

vi.mock('@/services/sns/index.js', () => ({
  SNS: vi.fn().mockImplementation(() => ({
    publish: vi.fn().mockResolvedValue(undefined),
  })),
}));

describe('Request Super Scouting Lambda', () => {
  const mockContext = {} as any;

  const mockTeam: Team = TeamsFactory.build({
    teamId: 'test-team-id',
    gameworldId: 'test-gameworld-id',
    balance: 10000,
  });

  const mockManager: Manager = ManagerFactory.build({
    managerId: 'test-user-id',
    gameworldId: 'test-gameworld-id',
    superScoutTokens: 2,
    team: mockTeam,
  });

  beforeEach(() => {
    process.env.MANAGERS_TABLE_NAME = 'managers-table';
    process.env.TEAMS_TABLE_NAME = 'teams-table';
    process.env.SCOUTING_REQUESTS_TABLE_NAME = 'scouting-requests-table';
    process.env.SCOUTING_TOPIC_ARN = 'arn:aws:sns:region:account:topic';
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  it('should successfully process a valid super scouting request', async () => {
    const event = createHttpEvent({
      httpMethod: 'POST',
      body: {
        type: 'player',
        id: 'test-player-id',
      },
    });

    mockManagerRepository.getManagerById.mockResolvedValue({
      ...mockManager,
    });
    mockManagerRepository.updateManagerById.mockResolvedValue(undefined);
    mockTeamRepository.getTeam.mockResolvedValue(mockTeam);
    mockTeamRepository.updateTeamBalance.mockResolvedValue(undefined);

    const response = await handler(event, mockContext);
    const body = JSON.parse(response.body);

    expect(response.statusCode).toBe(200);
    expect(body).toEqual({
      message: 'Scouting request successful',
      remainingTokens: 1,
      newBalance: 5000,
    });

    expect(mockManagerRepository.getManagerById).toHaveBeenCalledWith('test-user-id');
    expect(mockManagerRepository.updateManagerById).toHaveBeenCalledWith('test-user-id', {
      superScoutTokens: 1,
    });
    expect(mockTeamRepository.getTeam).toHaveBeenCalledWith(
      'test-gameworld-id',
      mockManager.team!.teamId,
      false
    );
    expect(mockTeamRepository.updateTeamBalance).toHaveBeenCalledWith(
      'test-team-id',
      'test-gameworld-id',
      -5000,
      TransactionType.SCOUTING
    );
  });

  it('should return an error when no manager found', async () => {
    const event = createHttpEvent({
      httpMethod: 'POST',
      body: {
        type: 'player',
        id: 'test-player-id',
      },
    });

    mockManagerRepository.getManagerById.mockResolvedValue(null);

    const response = await handler(event, mockContext);
    expect(response.statusCode).toBe(404);
    const body = JSON.parse(response.body);
    expect(body.error).toBe('Manager not found');
  });

  it('should reject when manager has no super scout tokens', async () => {
    const event = createHttpEvent({
      httpMethod: 'POST',
      body: {
        type: 'player',
        id: 'test-player-id',
      },
    });

    mockManagerRepository.getManagerById.mockResolvedValue({
      ...mockManager,
      superScoutTokens: 0,
    });

    const response = await handler(event, mockContext);
    const body = JSON.parse(response.body);

    expect(response.statusCode).toBe(400);
    expect(body.error).toBe('No super scouting tokens remaining');
    expect(mockManagerRepository.updateManagerById).not.toHaveBeenCalled();
  });

  it('should reject when team has insufficient funds', async () => {
    const event = createHttpEvent({
      httpMethod: 'POST',
      body: {
        type: 'player',
        id: 'test-player-id',
      },
    });

    mockManagerRepository.getManagerById.mockResolvedValue({
      ...mockManager,
    });
    mockTeamRepository.getTeam.mockResolvedValue({ ...mockTeam, balance: 4000 });

    const response = await handler(event, mockContext);
    const body = JSON.parse(response.body);

    expect(response.statusCode).toBe(400);
    expect(body.error).toContain('Insufficient funds');
    expect(mockManagerRepository.updateManagerById).not.toHaveBeenCalled();
    expect(mockTeamRepository.updateTeamBalance).not.toHaveBeenCalled();
  });

  it('should return 404 when team is not found', async () => {
    const event = createHttpEvent({
      httpMethod: 'POST',
      body: {
        type: 'player',
        id: 'test-player-id',
      },
    });

    mockManagerRepository.getManagerById.mockResolvedValue({
      ...mockManager,
    });
    mockTeamRepository.getTeam.mockResolvedValue(null);

    const response = await handler(event, mockContext);
    const body = JSON.parse(response.body);

    expect(response.statusCode).toBe(404);
    expect(body.error).toBe('Team not found');
    expect(mockManagerRepository.updateManagerById).not.toHaveBeenCalled();
    expect(mockTeamRepository.updateTeamBalance).not.toHaveBeenCalled();
  });

  it.each(['player', 'team', 'league'])('should accept valid type: %s', async (type) => {
    const event = createHttpEvent({
      httpMethod: 'POST',
      body: {
        type,
        id: 'test-id',
      },
    });

    mockManagerRepository.getManagerById.mockResolvedValue({
      ...mockManager,
    });
    mockManagerRepository.updateManagerById.mockResolvedValue(undefined);
    mockTeamRepository.getTeam.mockResolvedValue(mockTeam);
    mockTeamRepository.updateTeamBalance.mockResolvedValue(undefined);

    const response = await handler(event, mockContext);
    expect(response.statusCode).toBe(200);
  });
});
