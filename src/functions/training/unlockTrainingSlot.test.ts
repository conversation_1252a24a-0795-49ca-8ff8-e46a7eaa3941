import { TransactionType } from '@/storage-interface/teams/index.js';
import createHttpEvent from '@/testing/createHttpEvent.js';
import {
  mockManagerRepository,
  mockTeamRepository,
  mockTrainingRepository,
} from '@/testing/mockRepositories.js';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { handler } from './unlockTrainingSlot.js';

describe('unlockTrainingSlot handler', () => {
  const mockContext = {} as any;

  beforeEach(() => {
    vi.clearAllMocks();
    mockManagerRepository.getManagerById.mockResolvedValue({
      managerId: 'test-user-id',
      team: { teamId: 'test-team-id', gameworldId: 'gw-1', balance: 5000000 },
      gameworldId: 'gw-1',
    });
    mockTrainingRepository.getSlotsByTeam.mockResolvedValue([]);
    mockTeamRepository.createFromPK.mockImplementation((teamId) => ({ teamId }));
  });

  it('returns early if slot is already unlocked', async () => {
    mockTrainingRepository.getSlotsByTeam.mockResolvedValue([{ slotIndex: 2 } as any]);
    const event = createHttpEvent({ body: { slotIndex: 2 }, httpMethod: 'POST' });
    const response = await handler(event, mockContext);
    expect(response.statusCode).toBe(200);
    expect(JSON.parse(response.body)).toEqual({ alreadyUnlocked: true });
  });

  it('returns 400 if insufficient funds', async () => {
    mockManagerRepository.getManagerById.mockResolvedValue({
      managerId: 'test-user-id',
      team: { teamId: 'test-team-id', gameworldId: 'gw-1', balance: 500 },
      gameworldId: 'gw-1',
    });
    const event = createHttpEvent({ body: { slotIndex: 2 }, httpMethod: 'POST' });
    const response = await handler(event, mockContext);
    expect(response.statusCode).toBe(400);
    expect(JSON.parse(response.body)).toEqual({ error: 'Insufficient funds' });
  });

  it('creates slot and deducts funds if locked', async () => {
    mockTrainingRepository.getSlotsByTeam.mockResolvedValue([]);
    mockTeamRepository.updateTeamBalance.mockResolvedValue({});
    mockTrainingRepository.createSlot.mockResolvedValue({});
    const event = createHttpEvent({ body: { slotIndex: 2 }, httpMethod: 'POST' });
    const response = await handler(event, mockContext);
    expect(response.statusCode).toBe(200);
    expect(JSON.parse(response.body)).toEqual({ balance: 2000000 });
    expect(mockTeamRepository.updateTeamBalance).toHaveBeenCalledWith(
      'test-team-id',
      'gw-1',
      -3000000,
      TransactionType.TRAINING
    );
    expect(mockTrainingRepository.createSlot).toHaveBeenCalledWith(
      expect.objectContaining({ slotIndex: 2 })
    );
  });

  it('returns 422 if slotIndex is out of range', async () => {
    const event = createHttpEvent({ body: { slotIndex: 0 }, httpMethod: 'POST' });
    const response = await handler(event, mockContext);
    expect(response.statusCode).toBe(422);
    const body = JSON.parse(response.body);
    expect(body[0]).toEqual({
      instancePath: '/body/slotIndex',
      message: 'must be >= 1',
    });
  });

  it('returns 404 if manager or team not found', async () => {
    mockManagerRepository.getManagerById.mockResolvedValue(null);
    const event = createHttpEvent({ body: { slotIndex: 2 }, httpMethod: 'POST' });
    const response = await handler(event, mockContext);
    expect(response.statusCode).toBe(404);
    expect(JSON.parse(response.body)).toEqual({ error: 'Manager or team not found' });
  });
});
