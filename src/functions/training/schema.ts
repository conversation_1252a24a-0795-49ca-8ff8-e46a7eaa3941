import { MAX_SLOTS } from '@/functions/training/constants.js';

export const getAssignTrainingSlotSchema = {
  $schema: 'http://json-schema.org/draft-07/schema#',
  type: 'object',
  properties: {
    body: {
      type: 'object',
      properties: {
        playerId: { type: 'string' },
        attribute: { type: 'string' },
        slotIndex: {
          type: 'number',
          minimum: 0,
          maximum: MAX_SLOTS - 1,
        },
      },
      required: ['playerId', 'attribute'],
      additionalProperties: false,
    },
    pathParameters: {
      type: 'object',
      properties: {
        slotId: { type: 'string' },
      },
      required: ['slotId'],
      additionalProperties: false,
    },
  },
  required: ['body', 'pathParameters'],
};

export const unlockTrainingSlotSchema = {
  $schema: 'http://json-schema.org/draft-07/schema#',
  type: 'object',
  properties: {
    body: {
      type: 'object',
      properties: {
        slotIndex: {
          type: 'number',
          minimum: 1, // 0 starts unlocked
          maximum: MAX_SLOTS - 1,
        },
      },
      required: ['slotIndex'],
      additionalProperties: false,
    },
  },
  required: ['body'],
};
