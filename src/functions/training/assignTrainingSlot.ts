import { TeamTrainingSlot } from '@/entities/TeamTrainingSlot.js';
import { getAssignTrainingSlotSchema } from '@/functions/training/schema.js';
import { httpMiddify } from '@/middleware/rest/index.js';
import { HttpEvent } from '@/middleware/rest/types.js';
import { CurrentAttributes } from '@/model/player.js';
import { getAttributeValue } from '@/simulation/player-utils.js';
import { buildResponse } from '@/utils/buildResponse.js';
import { getUser } from '@/utils/getUser.js';

interface Body {
  playerId: string;
  attribute: string;
  slotIndex: number;
}
interface PathParameters {
  slotId: string;
}

export const main = async (event: HttpEvent<Body, PathParameters, void>) => {
  const { trainingRepository, teamRepository, playerRepository, managerRepository } =
    event.context.repositories;
  const { playerId, attribute, slotIndex } = event.body;
  let { slotId } = event.pathParameters;

  const userId = getUser(event);
  const manager = await managerRepository.getManagerById(userId);
  if (!manager || !manager.team) {
    return buildResponse(404, JSON.stringify({ error: 'Manager or team not found' }));
  }

  const teamId = manager.team.teamId;

  const player = await playerRepository.getPlayer(manager.team.gameworldId, playerId);
  if (!player) {
    return buildResponse(404, JSON.stringify({ error: 'Player not found' }));
  }
  const startValue =
    attribute === 'stamina'
      ? player.attributes.stamina
      : getAttributeValue(player, attribute as keyof CurrentAttributes);

  let slot: TeamTrainingSlot | null = null;
  if (slotId === 'new') {
    // try to find a slot with the same index in case the client is out of date or cheating
    const existingSlot = (await trainingRepository.getSlotsByTeam(teamId)).find(
      (s) => s.slotIndex === slotIndex
    );
    if (existingSlot) {
      slot = existingSlot;
    }
  } else {
    slot = await trainingRepository.getSlotById(slotId);
  }

  if (!slot) {
    if (slotIndex === 0) {
      // Create the slot if it does not exist
      slot = new TeamTrainingSlot();
      slot.team = teamRepository.createFromPK(teamId);
      slot.slotIndex = slotIndex;
      slot.player = playerRepository.createFromPK(playerId);
      slot.attribute = attribute;
      slot.startValue = startValue;
      slot.assignedAt = BigInt(Date.now());
      await trainingRepository.createSlot(slot);
    } else {
      // This should be impossible as we have to unlock it first which would create the slot
      return buildResponse(404, JSON.stringify({ error: 'Slot not found' }));
    }
  } else {
    await trainingRepository.assignPlayerToSlot(slotId, playerId, attribute, startValue);
  }
  return buildResponse(200, JSON.stringify({ success: true }));
};

export const handler = httpMiddify(main, { schema: getAssignTrainingSlotSchema });
