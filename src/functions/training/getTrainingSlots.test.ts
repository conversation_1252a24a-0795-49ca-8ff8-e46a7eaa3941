import { TeamTrainingSlot } from '@/entities/TeamTrainingSlot.js';
import { HttpEvent } from '@/middleware/rest/types.ts';
import createHttpEvent from '@/testing/createHttpEvent.js';
import { mockManagerRepository, mockTrainingRepository } from '@/testing/mockRepositories.js';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { handler } from './getTrainingSlots.js';

function createDbSlot(overrides: Partial<TeamTrainingSlot> = {}): TeamTrainingSlot {
  return {
    id: overrides.id ?? 'slot-id',
    team: overrides.team ?? 'team-id',
    slotIndex: overrides.slotIndex ?? 0,
    player: overrides.player ?? undefined,
    attribute: overrides.attribute ?? undefined,
    assignedAt: overrides.assignedAt ?? undefined,
  } as TeamTrainingSlot;
}

describe('getTrainingSlots handler', () => {
  const mockContext = {} as any;
  let event: HttpEvent<void, void, void>;
  beforeEach(() => {
    vi.clearAllMocks();
    mockManagerRepository.getManagerById.mockResolvedValue({
      managerId: 'test-user-id',
      team: { teamId: 'team-id' },
      gameworldId: 'test-gameworld-id',
    });
    event = createHttpEvent({});
  });

  it('returns 5 slots, using DB values when present', async () => {
    mockTrainingRepository.getSlotsByTeam.mockResolvedValue([
      createDbSlot({
        id: 'db-0',
        slotIndex: 0,
        player: { playerId: 'p1', attributes: { finishingCurrent: 50 } } as any,
        attribute: 'finishing',
      }),
      createDbSlot({
        id: 'db-2',
        slotIndex: 2,
        player: { playerId: 'p2', attributes: { paceCurrent: 60 } } as any,
        attribute: 'pace',
      }),
    ]);

    const response = await handler(event, mockContext);
    const body = JSON.parse(response.body);

    expect(response.statusCode).toBe(200);
    expect(body.slots).toHaveLength(5);

    // Slot 0 and 2 should use DB values
    expect(body.slots[0]).toMatchObject({
      id: 'db-0',
      slotId: 0,
      player: { playerId: 'p1' },
      attribute: 'finishing',
      locked: false,
    });
    expect(body.slots[2]).toMatchObject({
      id: 'db-2',
      slotId: 2,
      player: { playerId: 'p2' },
      attribute: 'pace',
      locked: false,
    });

    // Other slots should be defaults
    expect(body.slots[1].locked).toBe(true);
    expect(body.slots[3].locked).toBe(true);
    expect(body.slots[4].locked).toBe(true);
  });

  it('returns all default slots if DB is empty', async () => {
    mockTrainingRepository.getSlotsByTeam.mockResolvedValue([]);

    const response = await handler(event, mockContext);
    const body = JSON.parse(response.body);

    expect(response.statusCode).toBe(200);
    expect(body.slots).toHaveLength(5);
    expect(body.slots.filter((s: any) => s.locked).length).toBe(4);
    expect(body.slots[0].locked).toBe(false);
    expect(body.slots[0].id).toBeUndefined();
  });

  it('maps playerId correctly if player is undefined', async () => {
    mockTrainingRepository.getSlotsByTeam.mockResolvedValue([
      createDbSlot({ id: 'db-1', slotIndex: 1 }),
    ]);

    const response = await handler(event, mockContext);
    const body = JSON.parse(response.body);

    expect(body.slots[1]).toMatchObject({
      id: 'db-1',
      slotId: 1,
      locked: false,
    });
  });

  it('returns unlockCost for locked slots', async () => {
    mockTrainingRepository.getSlotsByTeam.mockResolvedValue([]);

    const response = await handler(event, mockContext);
    const body = JSON.parse(response.body);

    expect(body.slots[1].unlockCost).toBe(1000000);
    expect(body.slots[2].unlockCost).toBe(3000000);
    expect(body.slots[3].unlockCost).toBe(7000000);
    expect(body.slots[4].unlockCost).toBe(15000000);
  });

  it('returns 404 if the manager is not found', async () => {
    mockManagerRepository.getManagerById.mockResolvedValue(null);
    const response = await handler(event, mockContext);
    expect(response.statusCode).toBe(404);
    expect(JSON.parse(response.body)).toEqual({ error: 'Manager or team not found' });
  });
});
