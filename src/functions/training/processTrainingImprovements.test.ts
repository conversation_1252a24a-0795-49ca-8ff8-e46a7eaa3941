import { EventWithRepositories } from '@/middleware/event/types.ts';
import { SQSEvent } from '@/middleware/sqs/types.js';
import { NotificationManager } from '@/services/notifications/NotificationManager.js';
import createSqsEvent from '@/testing/createSqsEvent.js';
import { mockPlayerRepository } from '@/testing/mockRepositories.js';
import { TrainingImprovementEvent } from '@/types/generated/training-improvement-event.ts';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { handler } from './processTrainingImprovements.js';

describe('processTrainingImprovements handler', () => {
  const mockContext = {} as any;
  let mockEvent: SQSEvent<TrainingImprovementEvent> & EventWithRepositories;

  const loadManagerPreferencesSpy = vi
    .spyOn(NotificationManager.prototype, 'loadManagerPreferences')
    .mockResolvedValue(undefined);
  const sendTrainingCompleteNotificationSpy = vi
    .spyOn(NotificationManager.prototype, 'sendTrainingCompleteNotification')
    .mockResolvedValue(undefined);

  beforeEach(() => {
    vi.clearAllMocks();
    mockEvent = createSqsEvent([
      {
        body: {
          playerId: 'player-1',
          attribute: 'finishing',
          current: 50,
          potential: 100,
          trainingMultiplier: 10,
        },
      },
      {
        body: {
          playerId: 'player-2',
          attribute: 'passing',
          current: 30,
          potential: 100,
          trainingMultiplier: 5,
        },
      },
    ]) as any;
  });

  it('updates player attributes for valid training improvement events', async () => {
    await handler(mockEvent, mockContext);

    expect(mockPlayerRepository.updatePlayerAttributesBatch).toHaveBeenCalledWith([
      { playerId: 'player-1', attribute: 'finishingCurrent', attributeIncrement: 5.5 },
      { playerId: 'player-2', attribute: 'passingCurrent', attributeIncrement: 3.65 },
    ]);
    expect(loadManagerPreferencesSpy).not.toHaveBeenCalled();
    expect(sendTrainingCompleteNotificationSpy).not.toHaveBeenCalled();
  });

  it('handles edge case where current equals potential', async () => {
    mockEvent = createSqsEvent([
      {
        body: {
          playerId: 'player-1',
          attribute: 'finishing',
          current: 100,
          potential: 100,
          trainingMultiplier: 10,
          managerId: 'manager-1',
          playerName: 'Player One',
        },
      },
    ]) as any;

    await handler(mockEvent, mockContext);

    expect(mockPlayerRepository.updatePlayerAttributesBatch).not.toHaveBeenCalled();
    expect(loadManagerPreferencesSpy).toHaveBeenCalledWith('manager-1', expect.anything());
    expect(sendTrainingCompleteNotificationSpy).toHaveBeenCalledWith('Player One', 'finishing');
  });

  it('updates stamina attribute directly for stamina training improvement event', async () => {
    mockEvent = createSqsEvent([
      {
        body: {
          playerId: 'player-stamina',
          attribute: 'stamina',
          current: 0.8,
          potential: 1,
          trainingMultiplier: 0.2,
        },
      },
    ]) as any;

    await handler(mockEvent, mockContext);

    // Calculate expected increment
    const progress_percentage = 0.8 / 1;
    const scaling_factor = 1 - 0.9 * progress_percentage;
    const attributeIncrement = Math.min(scaling_factor * 0.2, 1 - 0.8);
    expect(mockPlayerRepository.updatePlayerAttributesBatch).toHaveBeenCalledWith([
      { playerId: 'player-stamina', attribute: 'stamina', attributeIncrement },
    ]);
  });

  it('sends notification and does not update if stamina is at potential', async () => {
    mockEvent = createSqsEvent([
      {
        body: {
          playerId: 'player-stamina',
          attribute: 'stamina',
          current: 1,
          potential: 1,
          trainingMultiplier: 0.2,
          managerId: 'manager-2',
          playerName: 'Stamina Player',
        },
      },
    ]) as any;

    await handler(mockEvent, mockContext);

    expect(mockPlayerRepository.updatePlayerAttributesBatch).not.toHaveBeenCalled();
    expect(loadManagerPreferencesSpy).toHaveBeenCalledWith('manager-2', expect.anything());
    expect(sendTrainingCompleteNotificationSpy).toHaveBeenCalledWith('Stamina Player', 'stamina');
  });
});
