type SubscriberAttributes = {
  [key: string]: {
    updated_at_ms: number;
    value: string;
  };
};

export enum RevenueCatEventType {
  INITIAL_PURCHASE = 'INITIAL_PURCHASE',
  RENEWAL = 'RENEWAL',
  CANCELLATION = 'CANCELLATION',
  UNCANCELLATION = 'UNCANCELLATION',
  NON_RENEWING_PURCHASE = 'NON_RENEWING_PURCHASE',
  SUBSCRIPTION_PAUSED = 'SUBSCRIPTION_PAUSED',
  BILLING_ISSUE = 'BILLING_ISSUE',
  EXPIRATION = 'EXPIRATION',
  TRANSFER = 'TRANSFER',
  REFUND = 'REFUND',
  REFUND_REVERSED = 'REFUND_REVERSED',
  PRODUCT_CHANGE = 'PRODUCT_CHANGE',
  SUBSCRIPTION_EXTENDED = 'SUBSCRIPTION_EXTENDED',
  TEMPORARY_ENTITLEMENT_GRANT = 'TEMPORARY_ENTITLEMENT_GRANT',
  VIRTUAL_CURRENCY_TRANSACTION = 'VIRTUAL_CURRENCY_TRANSACTION',
  INVOICE_ISSUANCE = 'INVOICE_ISSUANCE',
}

export enum RevenueCatCancelReason {
  UNSUBSCRIBE = 'UNSUBSCRIBE',
  BILLING_ERROR = 'BILLING_ERROR',
  DEVELOPER_INITIATED = 'DEVELOPER_INITIATED',
  PRICE_INCREASE = 'PRICE_INCREASE',
  CUSTOMER_SUPPORT = 'CUSTOMER_SUPPORT',
  UNKNOWN = 'UNKNOWN',
}

export enum RevenueCatExpirationReason {
  UNSUBSCRIBE = 'UNSUBSCRIBE',
  BILLING_ERROR = 'BILLING_ERROR',
  DEVELOPER_INITIATED = 'DEVELOPER_INITIATED',
  PRICE_INCREASE = 'PRICE_INCREASE',
  CUSTOMER_SUPPORT = 'CUSTOMER_SUPPORT',
  UNKNOWN = 'UNKNOWN',
}

export type WebhookEvent = {
  event: {
    event_timestamp_ms: number;
    product_id?: string;
    period_type?: string;
    purchased_at_ms?: number;
    expiration_at_ms?: number | null;
    environment: string;
    entitlement_id?: string | null;
    entitlement_ids?: string[];
    presented_offering_id?: string | null;
    transaction_id?: string;
    original_transaction_id?: string;
    is_family_share?: boolean;
    country_code?: string;
    app_user_id: string;
    aliases?: string[];
    original_app_user_id?: string;
    currency?: string;
    price?: number;
    price_in_purchased_currency?: number;
    subscriber_attributes?: SubscriberAttributes;
    store?: string;
    takehome_percentage?: number;
    offer_code?: string | null;
    type: RevenueCatEventType;
    id: string;
    app_id: string;
    // Additional fields for specific event types
    cancel_reason?: RevenueCatCancelReason;
    expiration_reason?: RevenueCatExpirationReason;
    new_product_id?: string; // For PRODUCT_CHANGE
    auto_resume_at_ms?: number; // For SUBSCRIPTION_PAUSED
    transferred_from?: string[]; // For TRANSFER
    transferred_to?: string[]; // For TRANSFER
    is_trial_conversion?: boolean; // For RENEWAL
    renewal_number?: number; // For some events
    tax_percentage?: number;
    commission_percentage?: number;
    metadata?: any;
  };
  api_version: string;
};
