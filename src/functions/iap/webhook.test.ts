import { RevenueCatEventType, WebhookEvent } from '@/functions/iap/types.js';
import { HttpEvent } from '@/middleware/rest/types.js';
import createHttpEvent from '@/testing/createHttpEvent.js';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { handler } from './webhook.js';

// Mock the IAPService
vi.mock('@/services/iap/IAPService.js', () => ({
  IAPService: vi.fn().mockImplementation(() => ({
    processWebhookEvent: vi.fn(),
  })),
}));

describe('IAP Webhook Handler', () => {
  let mockContext = {} as any;
  let mockEvent: HttpEvent<WebhookEvent, void, void>;

  beforeEach(() => {
    const sampleWebhookEvent: WebhookEvent = {
      event: {
        event_timestamp_ms: Date.now(),
        product_id: 'jfg_superfan_1:sf1',
        period_type: 'NORMAL',
        purchased_at_ms: Date.now(),
        expiration_at_ms: Date.now() + 30 * 24 * 60 * 60 * 1000, // 30 days from now
        environment: 'PRODUCTION',
        entitlement_id: null,
        entitlement_ids: ['pro'],
        presented_offering_id: null,
        transaction_id: '123456789012345',
        original_transaction_id: '123456789012345',
        is_family_share: false,
        country_code: 'US',
        app_user_id: 'test-manager-id',
        aliases: ['$RCAnonymousID:8069238d6049ce87cc529853916d624c'],
        original_app_user_id: '$RCAnonymousID:87c6049c58069238dce29853916d624c',
        currency: 'USD',
        price: 4.99,
        price_in_purchased_currency: 4.99,
        subscriber_attributes: {
          $email: {
            updated_at_ms: 1662955084635,
            value: '<EMAIL>',
          },
        },
        store: 'APP_STORE',
        takehome_percentage: 0.7,
        offer_code: null,
        type: RevenueCatEventType.INITIAL_PURCHASE,
        id: '12345678-1234-1234-1234-123456789012',
        app_id: '1234567890',
      },
      api_version: '1.0',
    };

    mockEvent = createHttpEvent({
      body: sampleWebhookEvent,
      httpMethod: 'POST',
    });

    vi.clearAllMocks();
  });

  it('should process webhook event successfully', async () => {
    const { IAPService } = await import('@/services/iap/IAPService.js');
    const mockProcessWebhookEvent = vi.fn();
    (IAPService as any).mockImplementation(() => ({
      processWebhookEvent: mockProcessWebhookEvent,
    }));

    const response = await handler(mockEvent, mockContext);

    expect(response.statusCode).toBe(200);
    expect(mockProcessWebhookEvent).toHaveBeenCalledWith(mockEvent.body);
  });

  it('should return 400 for missing app_user_id', async () => {
    mockEvent = createHttpEvent({
      body: {
        ...mockEvent.body,
        event: {
          ...mockEvent.body.event,
          app_user_id: '',
        },
      },
      httpMethod: 'POST',
    });

    const response = await handler(mockEvent, mockContext);

    expect(response.statusCode).toBe(400);
    expect(JSON.parse(response.body)).toEqual({ error: 'Missing app_user_id' });
  });

  it('should return 200 even when processing fails', async () => {
    const { IAPService } = await import('@/services/iap/IAPService.js');
    const mockProcessWebhookEvent = vi.fn().mockRejectedValue(new Error('Processing failed'));
    (IAPService as any).mockImplementation(() => ({
      processWebhookEvent: mockProcessWebhookEvent,
    }));

    const response = await handler(mockEvent, mockContext);

    expect(response.statusCode).toBe(200);
    expect(JSON.parse(response.body)).toEqual({ error: 'Internal server error' });
  });
});
