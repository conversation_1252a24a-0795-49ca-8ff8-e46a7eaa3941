import { httpMiddify } from '@/middleware/rest/index.js';
import { HttpEvent } from '@/middleware/rest/types.js';
import { ManagerRepository } from '@/storage-interface/managers/index.js';
import { TeamRepository } from '@/storage-interface/teams/index.js';
import { buildResponse } from '@/utils/buildResponse.js';
import { logger } from '@/utils/logger.js';
import axios from 'axios';
import crypto from 'crypto';

const GOOGLE_AD_KEY_URL = 'https://gstatic.com/admob/reward/verifier-keys.json';

interface GoogleKey {
  keyId: string;
  pem: string;
  base64: string;
}

interface GoogleKeysResponse {
  keys: GoogleKey[];
}

interface QueryString {
  ad_network: string;
  ad_unit: string;
  key_id: string;
  reward_amount: string;
  reward_item: string;
  signature: string;
  timestamp: string;
  transaction_id: string;
  user_id: string;
}

/**
 * Fetches the google public keys for the admob providers.
 * These keys changes time to time.
 */
async function getGoogleKeysMap() {
  let googleKeyRes = await axios.get<GoogleKeysResponse>(GOOGLE_AD_KEY_URL);
  let { keys } = googleKeyRes.data;
  if (!keys) {
    throw new Error('No keys found from google keys');
  }
  /** For each of the keys array save it base 64 in decoded form in the key map */
  let keyMap: { [key: string]: crypto.KeyObject } = {};
  keys.forEach((k) => {
    keyMap[k.keyId] = crypto.createPublicKey(k.pem);
    logger.debug('keymap', { key: keyMap[`${k.keyId}`] });
  });
  return keyMap;
}

async function awardReward(
  managerRepository: ManagerRepository,
  teamRepository: TeamRepository,
  userId: string,
  quantity: string,
  item: string
) {
  switch (item) {
    case 'MagicSponge':
      await managerRepository.updateMagicSpongeCount(userId, Number(quantity));
      break;
    case 'CardAppeal':
      await managerRepository.updateCardAppealCount(userId, Number(quantity));
      break;
    case 'TeamNameReset':
      await managerRepository.updateManagerById(userId, { changedTeamName: false });
      break;
    default:
      throw new Error('Invalid reward item');
  }
}

export const main = async function (event: HttpEvent<void, void, QueryString>) {
  const { managerRepository, teamRepository } = event.context.repositories;
  const { signature, key_id } = event.queryStringParameters;
  if (!signature) {
    throw new Error('No signature value exist in the URL param');
  }

  logger.debug('Signature and KeyId ---');
  logger.debug(signature, key_id);

  // verify the query string parameters that arent signature and key_id
  // create a query string from the query string parameters
  const queryString = Object.keys(event.queryStringParameters)
    .filter((k) => k !== 'signature' && k !== 'key_id')
    // @ts-ignore
    .map((k) => `${k}=${event.queryStringParameters[k]}`)
    .join('&');

  logger.debug('Query string', queryString);

  let keyMap = await getGoogleKeysMap();

  if (keyMap[`${key_id}`]) {
    let publicKey = keyMap[`${key_id}`];
    const verifier = crypto.createVerify('RSA-SHA256');
    verifier.update(queryString);
    let result = verifier.verify(publicKey!, signature, 'base64');
    if (result) {
      logger.debug('Result ---', { result });
      await awardReward(
        managerRepository,
        teamRepository,
        event.queryStringParameters.user_id,
        event.queryStringParameters.reward_amount,
        event.queryStringParameters.reward_item
      );
      return buildResponse(200, '');
    } else {
      logger.debug('Failure ---', { result });
      throw new Error('Invalid Signature Supplied');
    }
  } else {
    logger.debug("Key id provided doesn't exist ---");
    throw new Error("Key id provided doesn't exist in the google public keys");
  }
};

export const handler = httpMiddify(main, {});
