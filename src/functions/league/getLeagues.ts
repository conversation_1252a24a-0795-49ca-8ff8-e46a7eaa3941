import { httpMiddify } from '@/middleware/rest/index.js';
import { HttpEvent } from '@/middleware/rest/types.js';
// Repositories are injected by middleware
import { buildResponse } from '@/utils/buildResponse.js';

interface PathParameters {
  gameworldId: string;
}

export type GetLeaguesEvent = HttpEvent<void, PathParameters, void>;

// Repository is injected by middleware

export const main = async function (event: GetLeaguesEvent) {
  // Get repository from context (injected by middleware)
  const { leagueRepository } = event.context.repositories;
  const leagues = await leagueRepository.getLeaguesByGameworld(event.pathParameters.gameworldId);

  const response = buildResponse(
    200,
    JSON.stringify({
      leagues: leagues,
    })
  );
  return Promise.resolve(response);
};

export const handler = httpMiddify(main, {});
