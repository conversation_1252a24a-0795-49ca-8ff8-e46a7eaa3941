import { Manager } from '@/entities/Manager.js';
import { Player } from '@/entities/Player.js';
import { Team } from '@/entities/Team.js';
import {
  processPlayerAging,
  sendRetirementNotifications,
  shouldPlayerRetire,
} from '@/functions/league/logic/PlayerRetirement.js';
import { NotificationManager } from '@/services/notifications/NotificationManager.js';
import { TeamsFactory } from '@/testing/factories/teamFactory.js';
import {
  createMockRepositories,
  mockEntityManager,
  mockManagerRepository,
  mockPlayerRepository,
} from '@/testing/mockRepositories.js';
import { beforeEach, describe, expect, it, vi } from 'vitest';

// Mock the NotificationManager
vi.mock('@/services/notifications/NotificationManager.js', () => ({
  NotificationManager: {
    getInstance: vi.fn(() => ({
      assignManagerPreferences: vi.fn(),
      sendPlayerRetirementNotification: vi.fn(),
    })),
  },
}));

describe('PlayerRetirement', () => {
  const mockNotificationManager = {
    assignManagerPreferences: vi.fn(),
    sendPlayerRetirementNotification: vi.fn(),
  };
  const mockRepositories = createMockRepositories();

  beforeEach(() => {
    vi.clearAllMocks();

    // Reset entity manager mocks
    mockEntityManager.nativeUpdate.mockReset();
    mockEntityManager.nativeDelete.mockReset();
    mockEntityManager.find.mockReset();

    // Reset specific repository mocks we use
    mockPlayerRepository.getPlayersByGameworld.mockReset();
    mockManagerRepository.getManagerById.mockReset();

    // Setup NotificationManager mock
    (NotificationManager.getInstance as any).mockReturnValue(mockNotificationManager);
  });

  describe('shouldPlayerRetire', () => {
    it('should return false for players under 37', () => {
      // Mock Math.random to always return 0.5
      vi.spyOn(Math, 'random').mockReturnValue(0.5);

      expect(shouldPlayerRetire(36)).toBe(false);
      expect(shouldPlayerRetire(30)).toBe(false);
      expect(shouldPlayerRetire(25)).toBe(false);
    });

    it('should return true for 37-year-old with 25% probability when random < 0.25', () => {
      vi.spyOn(Math, 'random').mockReturnValue(0.2);
      expect(shouldPlayerRetire(37)).toBe(true);
    });

    it('should return false for 37-year-old with 25% probability when random >= 0.25', () => {
      vi.spyOn(Math, 'random').mockReturnValue(0.3);
      expect(shouldPlayerRetire(37)).toBe(false);
    });

    it('should return true for 38-year-old with 50% probability when random < 0.5', () => {
      vi.spyOn(Math, 'random').mockReturnValue(0.4);
      expect(shouldPlayerRetire(38)).toBe(true);
    });

    it('should return false for 38-year-old with 50% probability when random >= 0.5', () => {
      vi.spyOn(Math, 'random').mockReturnValue(0.6);
      expect(shouldPlayerRetire(38)).toBe(false);
    });

    it('should always return true for 39-year-old (100% probability)', () => {
      vi.spyOn(Math, 'random').mockReturnValue(0.9);
      expect(shouldPlayerRetire(39)).toBe(true);

      vi.spyOn(Math, 'random').mockReturnValue(0.1);
      expect(shouldPlayerRetire(39)).toBe(true);
    });

    it('should return true for players over 39', () => {
      vi.spyOn(Math, 'random').mockReturnValue(0.5);
      expect(shouldPlayerRetire(40)).toBe(true);
      expect(shouldPlayerRetire(45)).toBe(true);
    });
  });

  describe('processPlayerAging', () => {
    it('should process aging, removal, and retirement efficiently', async () => {
      mockEntityManager.find.mockResolvedValueOnce([
        createMockPlayer({ age: 37, retiringAtEndOfSeason: false }),
        createMockPlayer({ age: 38, retiringAtEndOfSeason: false }),
      ]);

      // Mock bulk age increment
      mockEntityManager.nativeUpdate.mockResolvedValueOnce(100); // 100 players aged

      // Mock removal of previously marked players
      const previouslyMarkedPlayers = [createMockPlayer({ age: 38, retiringAtEndOfSeason: true })];
      mockPlayerRepository.getPlayersByGameworld.mockResolvedValueOnce(previouslyMarkedPlayers);
      mockEntityManager.nativeDelete.mockResolvedValueOnce(1); // 1 player removed
      mockEntityManager.nativeDelete.mockResolvedValueOnce(1); // 1 player removed
      mockEntityManager.nativeDelete.mockResolvedValueOnce(1); // 1 player removed

      // Mock eligible players for retirement
      const eligiblePlayers = [
        createMockPlayer({ age: 37, retiringAtEndOfSeason: false }),
        createMockPlayer({ age: 38, retiringAtEndOfSeason: false }),
      ];
      mockEntityManager.find.mockResolvedValueOnce(eligiblePlayers);

      // Mock retirement decisions - first player retires, second doesn't
      vi.spyOn(Math, 'random')
        .mockReturnValueOnce(0.2) // 37-year-old retires (< 0.25)
        .mockReturnValueOnce(0.6); // 38-year-old doesn't retire (>= 0.5)

      // Mock bulk update for retirement marking
      mockEntityManager.nativeUpdate.mockResolvedValueOnce(undefined);

      const result = await processPlayerAging('gameworld-1', 'league-1', mockRepositories);

      expect(result.totalPlayersAged).toBe(100);
      expect(result.removedPlayers).toBe(1);
      expect(result.newlyRetiringPlayers).toHaveLength(1);
      expect(result.newlyRetiringPlayers[0]!.age).toBe(37);

      // Verify bulk operations were called
      expect(mockEntityManager.nativeUpdate).toHaveBeenCalledTimes(4); // Age increment + retirement marking + unlink training slots + adjust health
      expect(mockEntityManager.nativeDelete).toHaveBeenCalledTimes(3); // Delete players and transfer lists and transfer requests
      expect(mockEntityManager.find).toHaveBeenCalledTimes(2);
    });

    it('should handle case with no retiring players', async () => {
      mockEntityManager.find.mockResolvedValueOnce([]);
      // Mock bulk age increment
      mockEntityManager.nativeUpdate.mockResolvedValueOnce(50); // 50 players aged

      // Mock no previously marked players
      mockPlayerRepository.getPlayersByGameworld.mockResolvedValueOnce([]);

      // Mock no eligible players for retirement
      mockEntityManager.find.mockResolvedValueOnce([]);

      mockEntityManager.nativeDelete.mockResolvedValueOnce(0); // 1 player removed

      const result = await processPlayerAging('gameworld-1', 'league-1', mockRepositories);

      expect(result.totalPlayersAged).toBe(50);
      expect(result.removedPlayers).toBe(0);
      expect(result.newlyRetiringPlayers).toHaveLength(0);

      // Verify only age increment was called
      expect(mockEntityManager.nativeUpdate).toHaveBeenCalledTimes(2);
      expect(mockEntityManager.nativeDelete).toHaveBeenCalledTimes(1);
      expect(mockEntityManager.find).toHaveBeenCalledTimes(2);
    });
  });

  describe('sendRetirementNotifications', () => {
    it('should send notifications for players with teams', async () => {
      const team = createMockTeam();
      const manager = createMockManager();
      const retiringPlayers = [
        createMockPlayer({
          firstName: 'John',
          surname: 'Doe',
          age: 37,
          team,
        }),
        createMockPlayer({
          firstName: 'Jane',
          surname: 'Smith',
          age: 39,
          team,
        }),
      ];

      mockManagerRepository.getManagerById.mockResolvedValue(manager);

      await sendRetirementNotifications(retiringPlayers, mockRepositories);

      expect(mockManagerRepository.getManagerById).toHaveBeenCalledTimes(1);
      expect(mockNotificationManager.assignManagerPreferences).toHaveBeenCalledTimes(1);
      expect(mockNotificationManager.sendPlayerRetirementNotification).toHaveBeenCalledWith(
        retiringPlayers
      );
    });

    it('should skip players without teams', async () => {
      const retiringPlayers = [
        createMockPlayer({
          firstName: 'Free',
          surname: 'Agent',
          age: 37,
          team: undefined,
        }),
      ];

      await sendRetirementNotifications(retiringPlayers, mockRepositories);

      expect(mockManagerRepository.getManagerById).not.toHaveBeenCalled();
      expect(mockNotificationManager.sendPlayerRetirementNotification).not.toHaveBeenCalled();
    });

    it('should handle missing managers gracefully', async () => {
      const team = createMockTeam();
      const retiringPlayers = [
        createMockPlayer({
          firstName: 'John',
          surname: 'Doe',
          age: 37,
          team,
        }),
      ];

      mockManagerRepository.getManagerById.mockResolvedValue(null);

      await sendRetirementNotifications(retiringPlayers, mockRepositories);

      expect(mockManagerRepository.getManagerById).toHaveBeenCalledWith(team.teamId);
      expect(mockNotificationManager.sendPlayerRetirementNotification).not.toHaveBeenCalled();
    });
  });
});

// Helper functions to create mock objects
function createMockPlayer(overrides: Partial<Player> = {}): Player {
  return {
    playerId: 'player-id',
    gameworldId: 'gameworld-1',
    firstName: 'Test',
    surname: 'Player',
    age: 25,
    seed: 12345,
    value: 100000,
    energy: 100,
    lastMatchPlayed: 0,
    suspendedForGames: 0,
    isTransferListed: false,
    retiringAtEndOfSeason: false,
    team: undefined,
    attributes: {} as any,
    overallStats: undefined,
    matchHistory: {} as any,
    injuredUntil: undefined,
    ...overrides,
  } as Player;
}

function createMockTeam(): Team {
  return TeamsFactory.build();
}

function createMockManager(): Manager {
  return {
    managerId: 'manager-id',
    email: '<EMAIL>',
    gameworldId: 'gameworld-1',
    team: createMockTeam(),
  } as Manager;
}
