import { League } from '@/entities/League.js';
import { Team } from '@/entities/Team.js';
import { logger } from '@/utils/logger.js';

export interface LeagueTeamCounts {
  [leagueId: string]: number;
}

export interface TeamMovement {
  teamId: string;
  fromLeagueId: string;
  toLeagueId: string;
}

export interface TeamStandings {
  teamName: string;
  points: number;
  goalDiff: number;
  goalsFor: number;
  wins: number;
}

export class LeagueProcessor {
  static groupTeamsByLeague(teams: Team[]): Map<string, Team[]> {
    return teams.reduce((acc, team) => {
      const leagueTeams = acc.get(team.league.id) || [];
      leagueTeams.push(team);
      acc.set(team.league.id, leagueTeams);
      return acc;
    }, new Map<string, Team[]>());
  }

  static sortTeamsInLeague(teams: Team[]): Team[] {
    return [...teams].sort((a, b) => {
      if (a.points !== b.points) {
        return b.points - a.points;
      }

      const aGoalDiff = a.goalsFor - a.goalsAgainst;
      const bGoalDiff = b.goalsFor - b.goalsAgainst;
      if (aGoalDiff !== bGoalDiff) {
        return bGoalDiff - aGoalDiff;
      }

      if (a.goalsFor !== b.goalsFor) {
        return b.goalsFor - a.goalsFor;
      }

      if (a.wins !== b.wins) {
        return b.wins - a.wins;
      }

      return a.teamName.localeCompare(b.teamName);
    });
  }

  static validateTeamCounts(teamCounts: LeagueTeamCounts, leagueMap: Map<string, League>): boolean {
    return Object.entries(teamCounts).every(([leagueId, count]) => {
      const league = leagueMap.get(leagueId);
      if (!league || !league.leagueRules) {
        logger.error(`League not found or missing rules: ${leagueId}`);
        return false;
      }
      logger.debug(`${count} === ${league.leagueRules.teamCount}`);
      return count === league.leagueRules.teamCount;
    });
  }

  static getTeamStandings(team: Team): TeamStandings {
    return {
      teamName: team.teamName,
      points: team.points,
      goalDiff: team.goalsFor - team.goalsAgainst,
      goalsFor: team.goalsFor,
      wins: team.wins,
    };
  }

  static processPromotionsAndRelegations(
    sortedLeagues: Map<string, Team[]>,
    leagues: League[]
  ): TeamMovement[] {
    const leagueMap = new Map(leagues.map((league) => [league.id, league]));
    const movements: TeamMovement[] = [];
    const teamCounts: LeagueTeamCounts = {};

    for (const [leagueId, teams] of sortedLeagues) {
      teamCounts[leagueId] = teams.length;
    }

    LeagueProcessor.processPromotions(sortedLeagues, leagueMap, teamCounts, movements);
    LeagueProcessor.processRelegations(sortedLeagues, leagueMap, teamCounts, movements);

    const isValid = LeagueProcessor.validateTeamCounts(teamCounts, leagueMap);
    if (!isValid) {
      logger.error('Invalid team distribution after promotions/relegations', { teamCounts });
      throw new Error('Invalid team distribution after promotions/relegations');
    }

    return movements;
  }

  private static processPromotions(
    sortedLeagues: Map<string, Team[]>,
    leagueMap: Map<string, League>,
    teamCounts: LeagueTeamCounts,
    movements: TeamMovement[]
  ): void {
    const sortedLeaguesByTier = [...leagueMap.values()].sort((a, b) => b.tier - a.tier);

    for (const league of sortedLeaguesByTier) {
      const teams = sortedLeagues.get(league.id);
      if (!teams || !league.parentLeague) continue;

      const parentLeague = leagueMap.get(league.parentLeague.id);
      if (!parentLeague || !league.leagueRules) continue;

      const teamsToPromote = teams.slice(0, league.leagueRules.promotionSpots);
      for (const team of teamsToPromote) {
        movements.push({
          teamId: team.teamId,
          fromLeagueId: league.id,
          toLeagueId: parentLeague.id,
        });
        teamCounts[league.id]!--;
        teamCounts[parentLeague.id] = (teamCounts[parentLeague.id] || 0) + 1;
      }
    }
  }

  private static processRelegations(
    sortedLeagues: Map<string, Team[]>,
    leagueMap: Map<string, League>,
    teamCounts: LeagueTeamCounts,
    movements: TeamMovement[]
  ): void {
    const sortedLeaguesTopDown = [...leagueMap.values()].sort((a, b) => a.tier - b.tier);

    for (const league of sortedLeaguesTopDown) {
      const teams = sortedLeagues.get(league.id);
      if (!teams || !league.leagueRules) continue;

      // Get child leagues
      const childLeagues = [...leagueMap.values()].filter((l) => l.parentLeague?.id === league.id);
      if (childLeagues.length === 0) continue;

      const totalRelegation = league.leagueRules.relegationSpots;
      const teamsPerChild = totalRelegation / childLeagues.length;

      if (!Number.isInteger(teamsPerChild)) {
        logger.error('Invalid relegation configuration', {
          leagueId: league.id,
          totalRelegation,
          childLeagues: childLeagues.length,
        });
        continue;
      }

      const teamsToRelegate = teams.slice(-totalRelegation);
      LeagueProcessor.distributeRelegatedTeams(
        teamsToRelegate,
        league,
        childLeagues.map((l) => l.id),
        teamsPerChild,
        teamCounts,
        movements
      );
    }
  }

  private static distributeRelegatedTeams(
    teamsToRelegate: Team[],
    league: League,
    childLeagueIds: string[],
    teamsPerChild: number,
    teamCounts: LeagueTeamCounts,
    movements: TeamMovement[]
  ): void {
    for (let i = 0; i < teamsToRelegate.length; i++) {
      const childLeagueIndex = Math.floor(i / teamsPerChild);
      const childLeagueId = childLeagueIds[childLeagueIndex];

      if (!childLeagueId) {
        logger.error('Invalid child league index', {
          leagueId: league.id,
          childIndex: childLeagueIndex,
        });
        continue;
      }

      movements.push({
        teamId: teamsToRelegate[i]!.teamId,
        fromLeagueId: league.id,
        toLeagueId: childLeagueId,
      });
      teamCounts[league.id]!--;
      teamCounts[childLeagueId] = (teamCounts[childLeagueId] || 0) + 1;
    }
  }
}
