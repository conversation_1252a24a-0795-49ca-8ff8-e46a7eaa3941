import { Player } from '@/entities/Player.js';
import { TeamTrainingSlot } from '@/entities/TeamTrainingSlot.js';
import { TransferListedPlayer } from '@/entities/TransferListedPlayer.js';
import { TransferRequest } from '@/entities/TransferRequest.js';
import { Repositories } from '@/middleware/database/types.js';
import { NotificationManager } from '@/services/notifications/NotificationManager.js';
import { logger } from '@/utils/logger.js';
import { raw } from '@mikro-orm/core';

/**
 * Retirement probabilities by age
 */
const RETIREMENT_PROBABILITIES: Record<number, number> = {
  37: 0.25, // 25%
  38: 0.5, // 50%
  39: 1.0, // 100%
};

/**
 * Determines if a player should retire based on their age
 * @param age The player's age
 * @returns true if the player should retire, false otherwise
 */
export function shouldPlayerRetire(age: number): boolean {
  if (age < 37) {
    return false;
  }
  if (age > 39) {
    return true;
  }
  const probability = RETIREMENT_PROBABILITIES[age];
  if (!probability) {
    return false;
  }

  return Math.random() < probability;
}

interface PlayersByTeam {
  teamId: string | null;
  players: Player[];
}

interface RetirementProcessingResult {
  newlyRetiringPlayers: Player[];
  totalPlayersAged: number;
  removedPlayers: number;
}

/**
 * Bulk set player energy to 100 at the end of the season
 * @param gameworldId
 * @param leagueId
 * @param repositories
 */
async function bulkSetPlayerEnergy(
  gameworldId: string,
  leagueId: string,
  repositories: Repositories
) {
  return repositories.playerRepository.getEntityManager().nativeUpdate(
    Player,
    {
      gameworldId,
      team: { league: { id: leagueId } },
    },
    { energy: 100 }
  );
}

/**
 * Processes player aging and retirement for a gameworld using efficient bulk operations
 * @param gameworldId The gameworld ID
 * @param leagueId The league ID
 * @param repositories The repositories
 * @returns Object containing newly retiring players and processing stats
 */
export async function processPlayerAging(
  gameworldId: string,
  leagueId: string,
  repositories: Repositories
): Promise<RetirementProcessingResult> {
  logger.debug('Processing player aging and retirement', { gameworldId });

  // Step 1: Bulk increment all player ages using raw SQL for efficiency
  const totalPlayersAged = await bulkIncrementPlayerAges(gameworldId, leagueId, repositories);

  // Step 2: Remove players who were marked for retirement in the previous season
  const removedPlayers = await removePlayersMarkedForRetirement(
    gameworldId,
    leagueId,
    repositories
  );

  // Step 3: Get players who are now eligible for retirement (after age increment)
  const eligiblePlayers = await getPlayersEligibleForRetirement(
    gameworldId,
    leagueId,
    repositories
  );

  // Step 4: Process retirement decisions and mark new retiring players
  const newlyRetiringPlayers = await processRetirementDecisions(eligiblePlayers, repositories);

  // Step 5: Give all players full energy
  await bulkSetPlayerEnergy(gameworldId, leagueId, repositories);

  logger.info('Completed player aging and retirement processing', {
    gameworldId,
    totalPlayersAged,
    removedPlayers,
    newlyRetiringPlayers: newlyRetiringPlayers.length,
  });

  return {
    newlyRetiringPlayers,
    totalPlayersAged,
    removedPlayers,
  };
}

/**
 * Bulk increment all player ages in a gameworld using raw SQL for efficiency
 * @param gameworldId The gameworld ID
 * @param leagueId The league ID
 * @param repositories The repositories
 * @returns Number of players whose ages were incremented
 */
async function bulkIncrementPlayerAges(
  gameworldId: string,
  leagueId: string,
  repositories: Repositories
): Promise<number> {
  const { playerRepository } = repositories;

  try {
    // Use MikroORM's entity manager to execute raw SQL for bulk update
    const em = playerRepository.getEntityManager();

    const result = await em.nativeUpdate(
      Player,
      {
        gameworldId,
        team: { league: { id: leagueId } },
      },
      { age: raw('age + 1') }
    );

    logger.debug('Bulk incremented player ages', {
      gameworldId,
      playersUpdated: result,
    });

    return result;
  } catch (error) {
    logger.error('Failed to bulk increment player ages', {
      gameworldId,
      error,
    });
    throw error;
  }
}

async function sendRetiredNotifications(
  playersByTeam: PlayersByTeam[],
  repositories: Repositories
) {
  const { managerRepository } = repositories;
  const notificationManager = NotificationManager.getInstance();

  // Process each team's retiring players
  for (const { teamId, players } of playersByTeam) {
    if (!teamId) {
      continue;
    }

    try {
      // Get the manager for this team
      const manager = await managerRepository.getManagerById(teamId);
      if (!manager) {
        continue;
      }

      // Set up notification manager with manager context
      notificationManager.assignManagerPreferences(manager, repositories);

      try {
        await notificationManager.sendPlayerRetiredNotification(players);
      } catch (error) {
        logger.error('Failed to send retired notification for players', {
          players,
          teamId,
          error,
        });
      }

      logger.info('Completed retirement notifications for team', {
        teamId,
        managerId: manager.managerId,
        retiringPlayerCount: players.length,
      });
    } catch (error) {
      logger.error('Failed to process retirement notifications for team', {
        teamId,
        retiringPlayerCount: players.length,
        error,
      });
    }
  }
}

/**
 * Remove players who were marked for retirement in the previous season
 * @param gameworldId The gameworld ID
 * @param leagueId The league ID
 * @param repositories The repositories
 * @returns Array of removed players
 */
async function removePlayersMarkedForRetirement(
  gameworldId: string,
  leagueId: string,
  repositories: Repositories
): Promise<number> {
  const { playerRepository } = repositories;

  try {
    const em = playerRepository.getEntityManager();

    // Get player IDs to be deleted
    const playersToDelete = await em.find(
      Player,
      {
        gameworldId,
        retiringAtEndOfSeason: true,
        team: { league: { id: leagueId } },
      },
      {
        populate: ['team', 'team.manager'],
      }
    );
    const playerIds = playersToDelete.map((p) => p.playerId);
    const groupedPlayers = groupPlayersByTeam(playersToDelete);
    await sendRetiredNotifications(groupedPlayers, repositories);

    if (playerIds.length > 0) {
      // Delete transfer-listed players linked to these players
      await em.nativeDelete(TransferListedPlayer, {
        player: { $in: playerIds },
        gameworldId,
      });
      // Delete transfer requests linked to these players
      await em.nativeDelete(TransferRequest, {
        player: { $in: playerIds },
      });

      // Unlink retiring players from training slots
      await em.nativeUpdate(
        TeamTrainingSlot,
        {
          player: { $in: playerIds },
        },
        {
          player: null,
          attribute: null,
          startValue: null,
          assignedAt: null,
        }
      );
    }

    // Now delete the players
    const deletedCount = await em.nativeDelete(Player, {
      gameworldId,
      retiringAtEndOfSeason: true,
      team: { league: { id: leagueId } },
    });

    logger.info('Removed players marked for retirement', {
      gameworldId,
      playersRemoved: deletedCount,
      deletedCount,
    });

    return deletedCount;
  } catch (error) {
    logger.error('Failed to remove players marked for retirement', {
      gameworldId,
      error,
    });
    throw error;
  }
}

/**
 * Get players who are eligible for retirement (ages 37, 38, 39) and not already marked
 * @param gameworldId The gameworld ID
 * @param leagueId The league ID
 * @param repositories The repositories
 * @returns Array of players eligible for retirement
 */
async function getPlayersEligibleForRetirement(
  gameworldId: string,
  leagueId: string,
  repositories: Repositories
): Promise<Player[]> {
  const { playerRepository } = repositories;

  try {
    const em = playerRepository.getEntityManager();

    // Get players aged 37, 38, or 39 who are not already marked for retirement
    const eligiblePlayers = await em.find(
      Player,
      {
        gameworldId,
        team: { league: { id: leagueId } },
        age: { $gte: 37 },
        retiringAtEndOfSeason: false,
      },
      {
        populate: ['team', 'team.manager'],
      }
    );

    logger.debug('Found players eligible for retirement', {
      gameworldId,
      eligibleCount: eligiblePlayers.length,
    });

    return eligiblePlayers;
  } catch (error) {
    logger.error('Failed to get players eligible for retirement', {
      gameworldId,
      error,
    });
    throw error;
  }
}

/**
 * Process retirement decisions for eligible players and mark them for retirement
 * @param eligiblePlayers Players eligible for retirement
 * @param repositories The repositories
 * @returns Array of players newly marked for retirement
 */
async function processRetirementDecisions(
  eligiblePlayers: Player[],
  repositories: Repositories
): Promise<Player[]> {
  const { playerRepository } = repositories;

  if (eligiblePlayers.length === 0) {
    return [];
  }

  const newlyRetiringPlayers: Player[] = [];

  // Process retirement decisions
  for (const player of eligiblePlayers) {
    if (shouldPlayerRetire(player.age)) {
      player.retiringAtEndOfSeason = true;
      newlyRetiringPlayers.push(player);

      logger.debug('Player marked for retirement', {
        playerId: player.playerId,
        playerName: `${player.firstName} ${player.surname}`,
        age: player.age,
        teamId: player.team?.teamId,
      });
    }
  }

  // Bulk update retirement status for efficiency
  if (newlyRetiringPlayers.length > 0) {
    try {
      const em = playerRepository.getEntityManager();
      const playerIds = newlyRetiringPlayers.map((p) => p.playerId);

      await em.nativeUpdate(
        Player,
        {
          playerId: { $in: playerIds },
        },
        {
          retiringAtEndOfSeason: true,
        }
      );

      logger.info('Marked players for retirement and unlinked from training slots', {
        playersMarked: newlyRetiringPlayers.length,
      });
    } catch (error) {
      logger.error('Failed to mark players for retirement or unlink training slots', {
        error,
        playerCount: newlyRetiringPlayers.length,
      });
      throw error;
    }
  }

  return newlyRetiringPlayers;
}

/**
 * Sends retirement notifications for players, grouped by team for efficiency
 * @param retiringPlayers The players who are retiring
 * @param repositories The repositories
 */
export async function sendRetirementNotifications(
  retiringPlayers: Player[],
  repositories: Repositories
): Promise<void> {
  const { managerRepository } = repositories;
  const notificationManager = NotificationManager.getInstance();

  logger.debug('Sending retirement notifications', {
    playerCount: retiringPlayers.length,
  });

  if (retiringPlayers.length === 0) {
    return;
  }

  // Group players by team for efficient processing
  const playersByTeam = groupPlayersByTeam(retiringPlayers);

  // Process each team's retiring players
  for (const { teamId, players } of playersByTeam) {
    if (!teamId) {
      continue;
    }

    try {
      // Get the manager for this team
      const manager = await managerRepository.getManagerById(teamId);
      if (!manager) {
        continue;
      }

      // Set up notification manager with manager context
      notificationManager.assignManagerPreferences(manager, repositories);

      try {
        await notificationManager.sendPlayerRetirementNotification(players);
      } catch (error) {
        logger.error('Failed to send retirement notification for players', {
          players,
          teamId,
          error,
        });
      }

      logger.info('Completed retirement notifications for team', {
        teamId,
        managerId: manager.managerId,
        retiringPlayerCount: players.length,
      });
    } catch (error) {
      logger.error('Failed to process retirement notifications for team', {
        teamId,
        retiringPlayerCount: players.length,
        error,
      });
    }
  }

  logger.info('Completed all retirement notifications', {
    totalRetiringPlayers: retiringPlayers.length,
    teamsProcessed: playersByTeam.filter((g) => g.teamId).length,
  });
}

/**
 * Groups players by their team ID for efficient processing
 * @param players Array of players to group
 * @returns Array of team groups with their players
 */
function groupPlayersByTeam(players: Player[]): PlayersByTeam[] {
  const teamGroups = new Map<string | null, Player[]>();

  for (const player of players) {
    const teamId = player.team?.teamId || null;

    if (!teamGroups.has(teamId)) {
      teamGroups.set(teamId, []);
    }

    teamGroups.get(teamId)!.push(player);
  }

  return Array.from(teamGroups.entries()).map(([teamId, players]) => ({
    teamId,
    players,
  }));
}
