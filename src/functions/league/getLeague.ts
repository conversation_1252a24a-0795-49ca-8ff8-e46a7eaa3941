import { httpMiddify } from '@/middleware/rest/index.js';
import { HttpEvent } from '@/middleware/rest/types.js';
// Repositories are injected by middleware
import { buildResponse } from '@/utils/buildResponse.js';
import { jsonStringifySafe } from '@/utils/misc.js';

interface PathParameters {
  gameworldId: string;
  leagueId: string;
}

interface QueryParameters {
  includeTeams: boolean;
}

export type GetLeagueEvent = HttpEvent<void, PathParameters, QueryParameters>;

// Repository is injected by middleware

export const main = async function (event: GetLeagueEvent) {
  // Get repository from context (injected by middleware)
  const { leagueRepository } = event.context.repositories;
  const league = await leagueRepository.getLeague(
    event.pathParameters.gameworldId,
    event.pathParameters.leagueId
  );

  if (!league) {
    return buildResponse(404, JSON.stringify({ error: 'League not found' }));
  }

  const response = buildResponse(
    200,
    jsonStringifySafe({
      league,
    })
  );
  return Promise.resolve(response);
};

export const handler = httpMiddify(main, {});
