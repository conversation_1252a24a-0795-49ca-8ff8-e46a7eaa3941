import { Fixture } from '@/entities/Fixture.js';
import { Gameworld } from '@/entities/Gameworld.js';
import { League } from '@/entities/League.js';
import { LeagueRules } from '@/entities/LeagueRules.js';
import { Manager } from '@/entities/Manager.js';
import { Player } from '@/entities/Player.js';
import { PlayerAttributes } from '@/entities/PlayerAttributes.js';
import { PlayerMatchHistory } from '@/entities/PlayerMatchHistory.js';
import { PlayerOverallStats } from '@/entities/PlayerOverallStats.js';
import { Team } from '@/entities/Team.js';
import { Transactions } from '@/entities/Transactions.js';
import createSqsEvent from '@/testing/createSqsEvent.ts';
import { EndOfSeasonEvent } from '@/types/generated/end-of-season-event.js';
import { GeneratePlayersEvent } from '@/types/generated/generate-players-event.js';
import { logger } from '@/utils/logger.js';
import { SendMessageBatchCommand, SQSClient } from '@aws-sdk/client-sqs';
import { BatchWriteCommand, DynamoDBDocumentClient, QueryCommand } from '@aws-sdk/lib-dynamodb';
import { MikroORM } from '@mikro-orm/core';
import { PostgreSqlDriver } from '@mikro-orm/postgresql';
import { mockClient } from 'aws-sdk-client-mock';
import { v4 as uuidv4 } from 'uuid';
import { beforeAll, beforeEach, describe, expect, it, vi } from 'vitest';
import mikroOrmConfig from '../../mikro-orm.config.js';
import { handler } from './processEndOfSeason.js';

// Mock AWS clients
const sqsMock = mockClient(SQSClient);
const dynamoMock = mockClient(DynamoDBDocumentClient);

describe('processEndOfSeason E2E Test', () => {
  let orm: MikroORM<PostgreSqlDriver>;
  let gameworld: Gameworld;
  let league: League;
  let teams: Team[];
  let managers: Manager[];
  let players: Player[];
  let oldFixtures: Fixture[];

  beforeAll(async () => {
    if (!process.env.DATABASE_URL?.includes('localhost')) {
      throw new Error('Tests aborted: DATABASE_URL must be localhost');
    }

    vi.restoreAllMocks();
    vi.resetModules();
    vi.unmock('../../storage-interface/database-initializer.js');
    await MikroORM.init<PostgreSqlDriver>(mikroOrmConfig)
      .then(async (_orm) => {
        orm = _orm;
        // Create schema
        await _orm.em.getConnection().execute('CREATE EXTENSION IF NOT EXISTS "uuid-ossp";');
        await _orm.schema.refreshDatabase({ dropDb: false });
      })
      .catch((error) => {
        logger.error('Failed to initialize database', { error });
      });
  });

  beforeEach(async () => {
    // Reset AWS mocks
    sqsMock.reset();
    dynamoMock.reset();

    // Set up environment variables
    process.env.GENERATE_PLAYERS_QUEUE_URL =
      'https://sqs.test.amazonaws.com/123456789012/generate-players-queue';
    process.env.FIXTURE_GENERATION_QUEUE_URL =
      'https://sqs.test.amazonaws.com/123456789012/fixture-generation-queue';
    process.env.FIXTURE_DETAIL_TABLE_NAME = 'test-fixture-details';

    // Mock SQS responses
    sqsMock.on(SendMessageBatchCommand).resolves({});

    // Mock DynamoDB responses for fixture details cleanup
    const gameWorldId = uuidv4();
    const leagueId = uuidv4();
    dynamoMock.on(QueryCommand).resolves({
      Items: [
        { fixtureId: uuidv4(), gameworldId: gameWorldId, leagueId: leagueId },
        { fixtureId: uuidv4(), gameworldId: gameWorldId, leagueId: leagueId },
      ],
    });
    dynamoMock.on(BatchWriteCommand).resolves({});

    // Clear all data
    await clearTestData();

    // Create test data
    await createTestData();
  });

  async function clearTestData() {
    const em = orm.em.fork();
    await em.nativeDelete(PlayerMatchHistory, {});
    await em.nativeDelete(PlayerOverallStats, {});
    await em.nativeDelete(Transactions, {});
    await em.nativeDelete(Fixture, {});
    await em.nativeDelete(Player, {});
    await em.nativeDelete(Manager, {});
    await em.nativeDelete(Team, {});
    await em.nativeDelete(LeagueRules, {});
    await em.nativeDelete(League, {});
    await em.nativeDelete(Gameworld, {});
  }

  async function createTestData() {
    const em = orm.em.fork();

    // Create gameworld
    gameworld = new Gameworld();
    gameworld.id = uuidv4();
    gameworld.endDate = BigInt(Date.now() - 86400000); // 1 day ago (season ended)
    gameworld.highestManageableTier = 3;
    em.persist(gameworld);

    // Create league
    league = new League();
    league.id = uuidv4();
    league.gameworld = gameworld;
    league.name = 'Test League';
    league.tier = 1;
    em.persist(league);

    // Create league rules
    const leagueRules = new LeagueRules();
    leagueRules.league = league;
    leagueRules.promotionSpots = 1;
    leagueRules.relegationSpots = 1;
    leagueRules.teamCount = 4;
    leagueRules.maximumPrize = 100000;
    leagueRules.minimumPrize = 10000;
    em.persist(leagueRules);

    // Create teams
    teams = [];
    managers = [];
    for (let i = 1; i <= 4; i++) {
      const team = new Team();
      team.teamId = uuidv4();
      team.gameworldId = gameworld.id;
      team.league = league;
      team.tier = 1;
      team.teamName = `Test Team ${i}`;
      team.balance = 1000000;
      team.played = 6; // Full season played
      team.points = 15 - i * 3; // Descending points for league table
      team.goalsFor = 10;
      team.goalsAgainst = 5;
      team.wins = 5 - i;
      team.draws = 1;
      team.losses = i;
      em.persist(team);
      teams.push(team);

      // Create manager for each team
      const manager = new Manager();
      manager.managerId = uuidv4();
      manager.createdAt = BigInt(Date.now() - 86400000);
      manager.lastActive = BigInt(Date.now());
      manager.firstName = `Manager${i}`;
      manager.lastName = `Surname${i}`;
      manager.email = `manager${i}@test.com`;
      manager.team = team;
      manager.gameworldId = gameworld.id;
      manager.wins = 5 - i;
      manager.draws = 1;
      manager.defeats = i;
      manager.goalsScored = 10;
      manager.goalsConceded = 5;
      em.persist(manager);
      managers.push(manager);
    }

    // Create players for teams (including some old players who should retire)
    players = [];
    for (let teamIndex = 0; teamIndex < teams.length; teamIndex++) {
      const team = teams[teamIndex]!;
      for (let playerIndex = 1; playerIndex <= 15; playerIndex++) {
        const player = new Player();
        player.playerId = uuidv4();
        player.gameworldId = team.gameworldId;
        player.team = team;

        // Create some players of different ages for retirement testing
        if (playerIndex <= 2) {
          player.age = 39; // Will definitely retire
        } else if (playerIndex <= 4) {
          player.age = 38; // 50% chance to retire
        } else if (playerIndex <= 6) {
          player.age = 37; // 25% chance to retire
        } else {
          player.age = 20 + (playerIndex % 15); // Normal ages
        }

        player.seed = BigInt(Date.now() + teamIndex * 1000 + playerIndex);
        player.firstName = `Player${playerIndex}`;
        player.surname = `Team${teamIndex + 1}`;
        player.value = 100000 + playerIndex * 10000;
        player.energy = 50; // Low energy to test energy reset
        player.lastMatchPlayed = BigInt(Date.now() - 604800000); // 1 week ago
        player.suspendedForGames = 0;
        player.retiringAtEndOfSeason = false;
        em.persist(player);

        // Create player attributes
        const attributes = new PlayerAttributes();
        attributes.player = player;
        attributes.isGoalkeeper = playerIndex === 1;

        const baseValue = 15 + (playerIndex % 10);
        attributes.paceCurrent = baseValue;
        attributes.pacePotential = baseValue + 5;
        attributes.finishingCurrent = baseValue;
        attributes.finishingPotential = baseValue + 5;
        attributes.passingCurrent = baseValue;
        attributes.passingPotential = baseValue + 5;
        attributes.tacklingCurrent = baseValue;
        attributes.tacklingPotential = baseValue + 5;
        attributes.reflexesCurrent = playerIndex === 1 ? 20 : 5;
        attributes.reflexesPotential = attributes.reflexesCurrent + 5;
        attributes.positioningCurrent = baseValue;
        attributes.positioningPotential = baseValue + 5;
        attributes.shotStoppingCurrent = playerIndex === 1 ? 20 : 5;
        attributes.shotStoppingPotential = attributes.shotStoppingCurrent + 5;
        attributes.markingCurrent = baseValue;
        attributes.markingPotential = baseValue + 5;
        attributes.headingCurrent = baseValue;
        attributes.headingPotential = baseValue + 5;
        attributes.crossingCurrent = baseValue;
        attributes.crossingPotential = baseValue + 5;
        attributes.visionCurrent = baseValue;
        attributes.visionPotential = baseValue + 5;
        attributes.ballControlCurrent = baseValue;
        attributes.ballControlPotential = baseValue + 5;
        attributes.stamina = 0.8;
        em.persist(attributes);

        players.push(player);
      }
    }

    // Create some old fixtures from the completed season
    oldFixtures = [];
    for (let i = 1; i <= 6; i++) {
      const fixture = new Fixture();
      fixture.fixtureId = uuidv4();
      fixture.gameworldId = gameworld.id;
      fixture.league = league;
      fixture.homeTeam = teams[i % 2]!;
      fixture.awayTeam = teams[(i + 1) % 2]!;
      fixture.date = BigInt(Date.now() - 86400000 * (7 - i)); // Past dates
      fixture.played = true;
      fixture.score = [2, 1];
      fixture.simulatedAt = BigInt(Date.now() - 86400000 * (7 - i));
      em.persist(fixture);
      oldFixtures.push(fixture);
    }

    await em.flush();
  }

  it('should successfully process end of season and perform all required operations', async () => {
    // Arrange
    const endOfSeasonEvent: EndOfSeasonEvent = {
      gameworldId: gameworld.id,
      leagueId: league.id,
    };

    const sqsEvent = createSqsEvent([
      {
        messageId: 'test-message-1',
        body: endOfSeasonEvent,
      },
    ]) as any;

    const em = orm.em.fork();
    // Get initial state for comparison
    const initialPlayerCount = await em.count(Player, { gameworldId: gameworld.id });
    const initialOldestPlayers = await em.find(Player, {
      gameworldId: gameworld.id,
      age: { $gte: 37 },
    });

    // Act
    const result = await handler(sqsEvent, {} as any);

    // Assert - No batch failures
    expect(result).toBeDefined();
    expect(result!.batchItemFailures).toHaveLength(0);

    // Verify player aging occurred
    const allPlayersAfter = await em.find(Player, { gameworldId: gameworld.id });
    for (const player of allPlayersAfter) {
      const originalPlayer = players.find((p) => p.playerId === player.playerId);
      if (originalPlayer && !player.retiringAtEndOfSeason) {
        expect(player.age).toBe(originalPlayer.age + 1);
      }
    }

    // Verify player energy was reset to 100
    for (const player of allPlayersAfter) {
      expect(player.energy).toBe(100);
    }

    // Verify some players were marked for retirement (age 39 should definitely retire)
    const retiringPlayers = await em.find(Player, {
      gameworldId: gameworld.id,
      retiringAtEndOfSeason: true,
    });
    expect(retiringPlayers.length).toBeGreaterThan(0);

    // Verify SQS calls were made (youth players)
    expect(sqsMock.commandCalls(SendMessageBatchCommand)).toHaveLength(1);

    // First call should be for youth players
    const youthPlayersCall = sqsMock.commandCalls(SendMessageBatchCommand)[0]!;
    const youthPlayersArgs = youthPlayersCall.args[0].input;
    expect(youthPlayersArgs.QueueUrl).toBe(process.env.GENERATE_PLAYERS_QUEUE_URL);
    expect(youthPlayersArgs.Entries).toHaveLength(teams.length);

    // Verify each team got a youth player generation request
    for (let i = 0; i < teams.length; i++) {
      const entry = youthPlayersArgs.Entries![i]!;
      const messageBody = JSON.parse(entry.MessageBody!) as GeneratePlayersEvent;

      expect(messageBody.gameworldId).toBe(gameworld.id);
      expect(messageBody.teamId).toBe(teams[i]!.teamId);
      expect(messageBody.requiredPlayers).toBe(3); // Youth players per team
      expect(messageBody.minAge).toBe(16);
      expect(messageBody.maxAge).toBe(18);
    }
  });

  it('should handle teams without managers correctly', async () => {
    // Arrange - Remove one manager to test unmanaged team handling
    const em = orm.em.fork();
    await em.nativeDelete(Manager, { managerId: managers[0]!.managerId });

    const endOfSeasonEvent: EndOfSeasonEvent = {
      gameworldId: gameworld.id,
      leagueId: league.id,
    };

    const sqsEvent = createSqsEvent([
      {
        messageId: 'test-message-2',
        body: endOfSeasonEvent,
      },
    ]) as any;

    // Act
    const result = await handler(sqsEvent, {} as any);

    // Assert - Should still process successfully
    expect(result).toBeDefined();
    expect(result!.batchItemFailures).toHaveLength(0);

    // Verify SQS calls were still made (youth players )
    expect(sqsMock.commandCalls(SendMessageBatchCommand)).toHaveLength(1);
    const youthPlayersCall = sqsMock.commandCalls(SendMessageBatchCommand)[0]!;
    const youthPlayersArgs = youthPlayersCall.args[0]!.input;
    expect(youthPlayersArgs.Entries).toHaveLength(teams.length); // All teams should get youth players
  });
});
