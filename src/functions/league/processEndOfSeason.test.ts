/* eslint-disable jest/unbound-method */
import {
  processPlayerAging,
  sendRetirementNotifications,
} from '@/functions/league/logic/PlayerRetirement.js';
import { handler } from '@/functions/league/processEndOfSeason.js';
import { SQS } from '@/services/sqs/sqs.js';
import createSqsEvent from '@/testing/createSqsEvent.js';
import { LeagueFactory } from '@/testing/factories/leagueFactory.js';
import { TeamsFactory } from '@/testing/factories/teamFactory.js';
import {
  mockLeagueRepository,
  mockTeamRepository,
  resetAllRepositoryMocks,
} from '@/testing/mockRepositories.js';
import { SQSBatchResponse } from 'aws-lambda';
import { beforeEach, describe, expect, it, MockInstance, vi } from 'vitest';

// Mock PlayerRetirement
vi.mock('@/functions/league/logic/PlayerRetirement.js', () => ({
  processPlayerAging: vi.fn(),
  sendRetirementNotifications: vi.fn(),
}));

describe('Process End of Season', () => {
  const context: any = {};
  let mockSqsSendBatch: MockInstance;

  beforeEach(() => {
    vi.clearAllMocks();
    resetAllRepositoryMocks();

    process.env.GENERATE_PLAYERS_QUEUE_URL = 'test-queue-url'; // Add this line

    // Get the mocked SQS instance
    mockSqsSendBatch = vi.spyOn(SQS.prototype, 'sendBatch').mockResolvedValue();

    // Mock player retirement functions with default empty responses
    (processPlayerAging as any).mockResolvedValue({
      newlyRetiringPlayers: [],
      totalPlayersAged: 0,
      removedPlayers: [],
    });
    (sendRetirementNotifications as any).mockResolvedValue(undefined);
  });

  it('should process player aging and request youth players', async () => {
    const team1 = TeamsFactory.build({
      teamId: 'team1',
      teamName: 'Team 1',
      gameworldId: 'test-gameworld',
      tier: 1,
    });

    const team2 = TeamsFactory.build({
      teamId: 'team2',
      teamName: 'Team 2',
      gameworldId: 'test-gameworld',
      tier: 1,
    });

    // Mock repository methods
    mockTeamRepository.getTeamsByLeague.mockResolvedValue([team1, team2]);

    const event = createSqsEvent([
      {
        body: {
          gameworldId: 'test-gameworld',
          leagueId: 'league1',
        },
      },
    ]) as any;

    await handler(event, context);

    // Verify player aging was called
    expect(processPlayerAging).toHaveBeenCalledWith(
      'test-gameworld',
      'league1',
      expect.any(Object)
    );

    // Verify league was fetched
    expect(mockTeamRepository.getTeamsByLeague).toHaveBeenCalledWith('league1', false);

    // Verify youth players were requested via SQS
    expect(mockSqsSendBatch).toHaveBeenCalledWith(
      process.env.GENERATE_PLAYERS_QUEUE_URL,
      expect.arrayContaining([
        expect.objectContaining({
          Id: 'team1',
          MessageBody: expect.stringContaining('"teamId":"team1"'),
        }),
        expect.objectContaining({
          Id: 'team2',
          MessageBody: expect.stringContaining('"teamId":"team2"'),
        }),
      ])
    );
  });

  it('should handle league not found', async () => {
    // Mock repository to return null
    mockLeagueRepository.getLeague.mockResolvedValue(null);

    const event = createSqsEvent([
      {
        body: {
          gameworldId: 'test-gameworld',
          leagueId: 'nonexistent-league',
        },
      },
    ]) as any;

    const response = (await handler(event, context)) as SQSBatchResponse;
    expect(response.batchItemFailures).toHaveLength(1);
    expect(response.batchItemFailures[0]!.itemIdentifier).toBe(event.Records[0]!.messageId);

    // Verify league lookup was attempted
    expect(mockTeamRepository.getTeamsByLeague).toHaveBeenCalledWith('nonexistent-league', false);

    // Verify no youth players were requested
    expect(mockSqsSendBatch).not.toHaveBeenCalled();
  });

  it('should send retirement notifications when players are retiring', async () => {
    const newlyRetiringPlayers = [
      { playerId: 'player1', firstName: 'John', surname: 'Doe', age: 37 },
      { playerId: 'player2', firstName: 'Jane', surname: 'Smith', age: 39 },
    ];

    const removedPlayers = [{ playerId: 'player3', firstName: 'Old', surname: 'Player', age: 38 }];

    // Mock player aging to return retiring players
    (processPlayerAging as any).mockResolvedValue({
      newlyRetiringPlayers,
      totalPlayersAged: 150,
      removedPlayers,
    });

    const mockTeam = TeamsFactory.build({
      teamId: 'team1',
      gameworldId: 'test-gameworld',
    });

    const league = LeagueFactory.build({
      id: 'league1',
      teams: {
        getItems: () => [mockTeam],
        length: 1,
      } as any,
    });

    mockLeagueRepository.getLeague.mockResolvedValue(league);

    const event = createSqsEvent([
      {
        body: {
          gameworldId: 'test-gameworld',
          leagueId: 'league1',
        },
      },
    ]) as any;

    await handler(event, context);

    // Verify player aging was called
    expect(processPlayerAging).toHaveBeenCalledWith(
      'test-gameworld',
      'league1',
      expect.any(Object)
    );

    // Verify retirement notifications were sent
    expect(sendRetirementNotifications).toHaveBeenCalledWith(
      newlyRetiringPlayers,
      expect.any(Object)
    );
  });

  it('should skip retirement notifications when no players are retiring', async () => {
    // Mock player aging to return no retiring players
    (processPlayerAging as any).mockResolvedValue({
      newlyRetiringPlayers: [],
      totalPlayersAged: 100,
      removedPlayers: [],
    });

    const league = LeagueFactory.build({
      id: 'league1',
      teams: {
        getItems: () => [],
        length: 0,
      } as any,
    });

    mockLeagueRepository.getLeague.mockResolvedValue(league);

    const event = createSqsEvent([
      {
        body: {
          gameworldId: 'test-gameworld',
          leagueId: 'league1',
        },
      },
    ]) as any;

    await handler(event, context);

    // Verify player aging was called
    expect(processPlayerAging).toHaveBeenCalledWith(
      'test-gameworld',
      'league1',
      expect.any(Object)
    );

    // Verify retirement notifications were NOT sent
    expect(sendRetirementNotifications).not.toHaveBeenCalled();
  });

  it('should generate correct youth player requests', async () => {
    const team1 = TeamsFactory.build({
      teamId: 'team1',
      gameworldId: 'test-gameworld',
      tier: 2,
    });

    const team2 = TeamsFactory.build({
      teamId: 'team2',
      gameworldId: 'test-gameworld',
      tier: 2,
    });
    const league = LeagueFactory.build({
      teams: {
        getItems: () => [team1, team2],
        length: 2,
      } as any,
    });

    team1.league = league;
    team2.league = league;

    mockTeamRepository.getTeamsByLeague.mockResolvedValue([team1, team2]);

    const event = createSqsEvent([
      {
        body: {
          gameworldId: 'test-gameworld',
          leagueId: league.id,
        },
      },
    ]) as any;

    await handler(event, context);

    // Verify the youth player request structure
    expect(mockSqsSendBatch).toHaveBeenCalledWith(process.env.GENERATE_PLAYERS_QUEUE_URL, [
      {
        Id: team1.teamId,
        MessageBody: JSON.stringify({
          gameworldId: 'test-gameworld',
          leagueId: league.id,
          tier: 2,
          teamId: team1.teamId,
          requiredPlayers: 3,
          minAge: 16,
          maxAge: 18,
          minSkill: 1,
          maxSkill: 10,
          minPotential: 1,
          maxPotential: 40,
        }),
      },
      {
        Id: team2.teamId,
        MessageBody: JSON.stringify({
          gameworldId: 'test-gameworld',
          leagueId: league.id,
          tier: 2,
          teamId: team2.teamId,
          requiredPlayers: 3,
          minAge: 16,
          maxAge: 18,
          minSkill: 1,
          maxSkill: 10,
          minPotential: 1,
          maxPotential: 40,
        }),
      },
    ]);
  });
});
