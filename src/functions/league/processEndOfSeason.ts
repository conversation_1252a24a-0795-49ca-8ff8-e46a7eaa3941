import { LeagueRules } from '@/entities/LeagueRules.js';
import { Team } from '@/entities/Team.js';
import {
  processPlayerAging,
  sendRetirementNotifications,
} from '@/functions/league/logic/PlayerRetirement.js';
import { sqsMiddify } from '@/middleware/sqs/index.js';
import { SQSEvent } from '@/middleware/sqs/types.js';
import { SQS } from '@/services/sqs/sqs.js';
import {
  TeamRepository,
  TransactionType,
} from '@/storage-interface/teams/team-repository.interface.js';
import { EndOfSeasonEvent } from '@/types/generated/end-of-season-event.js';
import { GeneratePlayersEvent } from '@/types/generated/generate-players-event.js';
import { logger } from '@/utils/logger.js';
import { tracer } from '@/utils/tracer.js';

const sqs = new SQS({ tracer });

export async function awardPrizeMoney(
  sortedTeams: Team[],
  leagueRules: LeagueRules,
  teamRepository: TeamRepository
) {
  const prizeDifference = leagueRules.maximumPrize - leagueRules.minimumPrize;
  const prizeDifferencePerPosition = prizeDifference / (leagueRules.teamCount - 1);

  const promises = sortedTeams.map((team, index) => {
    const prizeMoney =
      leagueRules.minimumPrize + (leagueRules.teamCount - 1 - index) * prizeDifferencePerPosition;
    return teamRepository.updateTeamBalance(
      team.teamId,
      team.gameworldId,
      prizeMoney,
      TransactionType.PRIZE_MONEY
    );
  });

  await Promise.all(promises);
}

async function requestYouthPlayers(sortedTeams: Team[]) {
  const payload: GeneratePlayersEvent[] = sortedTeams.map((team) => ({
    gameworldId: team.gameworldId,
    leagueId: team.league.id,
    tier: team.tier,
    teamId: team.teamId,
    managerId: team.manager?.managerId,
    requiredPlayers: 3,
    minAge: 16,
    maxAge: 18,
    minSkill: 1,
    maxSkill: 10,
    minPotential: 1,
    maxPotential: 40,
  }));

  await sqs.sendBatch(
    process.env.GENERATE_PLAYERS_QUEUE_URL!,
    payload.map((event) => ({
      Id: event.teamId,
      MessageBody: JSON.stringify(event),
    }))
  );
}

export const main = async function (
  event: SQSEvent<EndOfSeasonEvent>
): Promise<{ batchItemFailures: { itemIdentifier: string }[] }> {
  logger.debug('Processing end of season');

  const batchItemFailures: { itemIdentifier: string }[] = [];

  for (const record of event.Records) {
    const { gameworldId, leagueId } = record.body;
    logger.debug('Processing league', { gameworldId, leagueId });

    try {
      // Get repositories from context (injected by middleware)
      const { teamRepository } = event.context.repositories;

      // Process player ageing and retirement first
      logger.debug('Processing player aging and retirement', { gameworldId });
      const { newlyRetiringPlayers, totalPlayersAged, removedPlayers } = await processPlayerAging(
        gameworldId,
        leagueId,
        event.context.repositories
      );

      // Send retirement notifications for newly retiring players
      if (newlyRetiringPlayers.length > 0) {
        await sendRetirementNotifications(newlyRetiringPlayers, event.context.repositories);
      }

      logger.info('Player aging and retirement completed', {
        gameworldId,
        totalPlayersAged,
        newlyRetiringPlayers: newlyRetiringPlayers.length,
        removedPlayers: removedPlayers,
      });

      const teams = await teamRepository.getTeamsByLeague(leagueId, false);
      if (!teams) {
        logger.error('Could not find teams for league', { leagueId });
        throw new Error('Could not find teams');
      }
      await requestYouthPlayers(teams);
    } catch (err) {
      logger.error('Error processing end of season for record', { error: err, record });
      batchItemFailures.push({ itemIdentifier: record.messageId });
    }
  }

  return { batchItemFailures };
};

export const handler = sqsMiddify<EndOfSeasonEvent>(main, {});
