import { completePlayerTransfer } from '@/functions/transfers/transferUtils.js';
import createHttpEvent from '@/testing/createHttpEvent.js';
import {
  mockManagerRepository,
  mockTransferRepository,
  resetAllRepositoryMocks,
} from '@/testing/mockRepositories.js';
import { getUser } from '@/utils/getUser.js';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { handler } from './acceptTransferRequest.js';

// Mock the getUser function
vi.mock('@/utils/getUser.js', () => ({
  getUser: vi.fn().mockReturnValue('test-user-id'),
}));

// Mock the transferUtils
vi.mock('@/functions/transfers/transferUtils.js', () => ({
  completePlayerTransfer: vi.fn(),
}));

describe('acceptTransferRequest', () => {
  const mockContext = {} as any;
  beforeEach(() => {
    vi.clearAllMocks();
    resetAllRepositoryMocks();
    vi.mocked(getUser).mockReturnValue('test-request-id');
  });

  it('should return 401 if user is not authenticated', async () => {
    vi.mocked(getUser).mockReturnValue('');

    const event = createHttpEvent({
      body: { transferRequestId: 'test-request-id' },
      httpMethod: 'POST',
    });

    const response = await handler(event, mockContext);

    expect(response.statusCode).toBe(401);
    expect(JSON.parse(response.body)).toEqual({ error: 'Unauthorized' });
  });

  it('should return 404 if manager not found', async () => {
    mockManagerRepository.getManagerById.mockResolvedValue(null);

    const event = createHttpEvent({
      body: { transferRequestId: 'test-request-id' },
      httpMethod: 'POST',
    });

    const response = await handler(event, mockContext);

    expect(response.statusCode).toBe(404);
    expect(JSON.parse(response.body)).toEqual({ error: 'Manager or team not found' });
  });

  it('should return 404 if transfer request not found', async () => {
    mockManagerRepository.getManagerById.mockResolvedValue({
      team: { teamId: 'user-team-id' },
    });
    mockTransferRepository.getTransferRequest.mockResolvedValue(null);

    const event = createHttpEvent({
      body: { transferRequestId: 'test-request-id' },
      httpMethod: 'POST',
    });

    const response = await handler(event, mockContext);

    expect(response.statusCode).toBe(404);
    expect(JSON.parse(response.body)).toEqual({ error: 'Transfer request not found' });
  });

  it('should allow seller to accept original offer', async () => {
    const mockTransferRequest = {
      id: 'test-request-id',
      value: 1000000,
      date: Date.now() - 1000,
      counterOfferTime: 0,
      counterOfferValue: 0,
      player: { playerId: 'player-id', firstName: 'John', surname: 'Doe' },
      buyer: { teamId: 'buyer-team-id' },
      seller: { teamId: 'user-team-id' },
    };

    mockManagerRepository.getManagerById.mockResolvedValue({
      team: { teamId: 'user-team-id' },
    });
    mockTransferRepository.getTransferRequest.mockResolvedValue(mockTransferRequest);

    const event = createHttpEvent({
      body: { transferRequestId: 'test-request-id' },
      httpMethod: 'POST',
    });

    const response = await handler(event, mockContext);

    expect(response.statusCode).toBe(200);
    expect(completePlayerTransfer).toHaveBeenCalledWith(
      mockTransferRequest.player,
      mockTransferRequest.buyer,
      mockTransferRequest.seller,
      1000000,
      expect.any(Object)
    );
    expect(mockTransferRepository.deleteTransferRequest).toHaveBeenCalledWith('test-request-id');
  });

  it('should allow buyer to accept counter offer', async () => {
    const now = Date.now();
    const mockTransferRequest = {
      id: 'test-request-id',
      value: 1000000,
      date: now - 2000,
      counterOfferTime: now - 1000,
      counterOfferValue: 1200000,
      player: { playerId: 'player-id', firstName: 'John', surname: 'Doe' },
      buyer: { teamId: 'user-team-id' },
      seller: { teamId: 'seller-team-id' },
    };

    mockManagerRepository.getManagerById.mockResolvedValue({
      team: { teamId: 'user-team-id' },
    });
    mockTransferRepository.getTransferRequest.mockResolvedValue(mockTransferRequest);

    const event = createHttpEvent({
      body: { transferRequestId: 'test-request-id' },
      httpMethod: 'POST',
    });

    const response = await handler(event, mockContext);

    expect(response.statusCode).toBe(200);
    expect(completePlayerTransfer).toHaveBeenCalledWith(
      mockTransferRequest.player,
      mockTransferRequest.buyer,
      mockTransferRequest.seller,
      1200000,
      expect.any(Object)
    );
  });

  it('should return 403 if user is not buyer or seller', async () => {
    const mockTransferRequest = {
      id: 'test-request-id',
      value: 1000000,
      date: Date.now() - 1000,
      counterOfferTime: 0,
      counterOfferValue: 0,
      player: { playerId: 'player-id', firstName: 'John', surname: 'Doe' },
      buyer: { teamId: 'other-buyer-team-id' },
      seller: { teamId: 'other-seller-team-id' },
    };

    mockManagerRepository.getManagerById.mockResolvedValue({
      team: { teamId: 'user-team-id' },
    });
    mockTransferRepository.getTransferRequest.mockResolvedValue(mockTransferRequest);

    const event = createHttpEvent({
      body: { transferRequestId: 'test-request-id' },
      httpMethod: 'POST',
    });

    const response = await handler(event, mockContext);

    expect(response.statusCode).toBe(403);
    expect(JSON.parse(response.body)).toEqual({
      error:
        'You are not authorized to accept this transfer request. You must be either the buyer or seller.',
    });
  });

  it('should return 400 if buyer tries to accept without valid counter offer', async () => {
    const mockTransferRequest = {
      id: 'test-request-id',
      value: 1000000,
      date: Date.now() - 1000,
      counterOfferTime: 0,
      counterOfferValue: 0,
      player: { playerId: 'player-id', firstName: 'John', surname: 'Doe' },
      buyer: { teamId: 'user-team-id' },
      seller: { teamId: 'seller-team-id' },
    };

    mockManagerRepository.getManagerById.mockResolvedValue({
      team: { teamId: 'user-team-id' },
    });
    mockTransferRepository.getTransferRequest.mockResolvedValue(mockTransferRequest);

    const event = createHttpEvent({
      body: { transferRequestId: 'test-request-id' },
      httpMethod: 'POST',
    });

    const response = await handler(event, mockContext);

    expect(response.statusCode).toBe(400);
    expect(JSON.parse(response.body)).toEqual({
      error:
        'No valid counter offer to accept. Counter offers must be more recent than the original offer.',
    });
  });
});
