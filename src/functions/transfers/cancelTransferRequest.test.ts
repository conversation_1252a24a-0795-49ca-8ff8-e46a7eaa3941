import createHttpEvent from '@/testing/createHttpEvent.js';
import {
  mockManagerRepository,
  mockTransferRepository,
  resetAllRepositoryMocks,
} from '@/testing/mockRepositories.js';
import { getUser } from '@/utils/getUser.js';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { handler } from './cancelTransferRequest.js';

// Mock the getUser function
vi.mock('@/utils/getUser.js', () => ({
  getUser: vi.fn().mockReturnValue('test-user-id'),
}));

// Mock the NotificationManager
const mockNotificationManagerInstance = vi.hoisted(() => ({
  assignManagerPreferences: vi.fn(),
  transferRequestWithdrawn: vi.fn(),
}));

vi.mock('@/services/notifications/NotificationManager.js', () => ({
  NotificationManager: {
    getInstance: vi.fn(() => mockNotificationManagerInstance),
  },
}));

describe('cancelTransferRequest', () => {
  const mockContext = {} as any;

  beforeEach(() => {
    resetAllRepositoryMocks();
    vi.clearAllMocks();
    mockNotificationManagerInstance.assignManagerPreferences.mockClear();
    mockNotificationManagerInstance.transferRequestWithdrawn.mockClear();
  });

  it('should successfully cancel a transfer request when user is the buyer', async () => {
    const transferRequestId = 'test-transfer-request-id';
    const userId = 'test-user-id';
    const userTeamId = 'buyer-team-id';

    vi.mocked(getUser).mockReturnValue(userId);

    // Mock manager
    const mockManager = {
      managerId: userId,
      team: {
        teamId: userTeamId,
        teamName: 'Buyer Team',
      },
    };

    // Mock transfer request
    const mockTransferRequest = {
      id: transferRequestId,
      player: {
        playerId: 'player-id',
        firstName: 'John',
        surname: 'Doe',
      },
      buyer: {
        teamId: userTeamId,
        teamName: 'Buyer Team',
      },
      seller: {
        teamId: 'seller-team-id',
        teamName: 'Seller Team',
        manager: {
          managerId: 'seller-manager-id',
          email: '<EMAIL>',
        },
      },
      value: 100000,
    };

    mockManagerRepository.getManagerById.mockResolvedValue(mockManager);
    mockTransferRepository.getTransferRequest.mockResolvedValue(mockTransferRequest);
    mockTransferRepository.deleteTransferRequest.mockResolvedValue(undefined);

    const event = createHttpEvent({
      body: { transferRequestId },
      httpMethod: 'POST',
    });

    const response = await handler(event, mockContext);

    expect(response.statusCode).toBe(200);
    expect(JSON.parse(response.body)).toEqual({
      message: 'Transfer request cancelled successfully',
      transfer: {
        id: transferRequestId,
        player: {
          id: 'player-id',
          name: 'John Doe',
        },
        buyer: userTeamId,
        seller: 'seller-team-id',
        value: 100000,
      },
    });

    expect(mockTransferRepository.deleteTransferRequest).toHaveBeenCalledWith(transferRequestId);
    expect(mockNotificationManagerInstance.transferRequestWithdrawn).toHaveBeenCalledWith(
      mockTransferRequest,
      'Buyer Team'
    );
  });

  it('should return 401 when user is not authenticated', async () => {
    vi.mocked(getUser).mockReturnValue('');

    const event = createHttpEvent({
      body: { transferRequestId: 'test-id' },
      httpMethod: 'POST',
    });

    const response = await handler(event, mockContext);

    expect(response.statusCode).toBe(401);
    expect(JSON.parse(response.body)).toEqual({ error: 'Unauthorized' });
  });

  it('should return 404 when manager is not found', async () => {
    vi.mocked(getUser).mockReturnValue('test-user-id');
    mockManagerRepository.getManagerById.mockResolvedValue(null);

    const event = createHttpEvent({
      body: { transferRequestId: 'test-id' },
      httpMethod: 'POST',
    });

    const response = await handler(event, mockContext);

    expect(response.statusCode).toBe(404);
    expect(JSON.parse(response.body)).toEqual({ error: 'Manager or team not found' });
  });

  it('should return 404 when transfer request is not found', async () => {
    vi.mocked(getUser).mockReturnValue('test-user-id');
    const mockManager = {
      managerId: 'test-user-id',
      team: {
        teamId: 'team-id',
        teamName: 'Team Name',
      },
    };

    mockManagerRepository.getManagerById.mockResolvedValue(mockManager);
    mockTransferRepository.getTransferRequest.mockResolvedValue(null);

    const event = createHttpEvent({
      body: { transferRequestId: 'non-existent-id' },
      httpMethod: 'POST',
    });

    const response = await handler(event, mockContext);

    expect(response.statusCode).toBe(404);
    expect(JSON.parse(response.body)).toEqual({ error: 'Transfer request not found' });
  });

  it('should return 403 when user is not the buyer', async () => {
    const transferRequestId = 'test-transfer-request-id';
    const userId = 'test-user-id';
    const userTeamId = 'user-team-id';

    vi.mocked(getUser).mockReturnValue(userId);

    // Mock manager
    const mockManager = {
      managerId: userId,
      team: {
        teamId: userTeamId,
        teamName: 'User Team',
      },
    };

    // Mock transfer request where user is NOT the buyer
    const mockTransferRequest = {
      id: transferRequestId,
      player: {
        playerId: 'player-id',
        firstName: 'John',
        surname: 'Doe',
      },
      buyer: {
        teamId: 'different-buyer-team-id',
        teamName: 'Different Buyer Team',
      },
      seller: {
        teamId: userTeamId,
        teamName: 'User Team',
      },
      value: 100000,
    };

    mockManagerRepository.getManagerById.mockResolvedValue(mockManager);
    mockTransferRepository.getTransferRequest.mockResolvedValue(mockTransferRequest);

    const event = createHttpEvent({
      body: { transferRequestId },
      httpMethod: 'POST',
    });

    const response = await handler(event, mockContext);

    expect(response.statusCode).toBe(403);
    expect(JSON.parse(response.body)).toEqual({
      error:
        'You are not authorized to cancel this transfer request. Only the buying team can cancel their own requests.',
    });

    expect(mockTransferRepository.deleteTransferRequest).not.toHaveBeenCalled();
  });

  it('should not send notification when seller has no manager', async () => {
    const transferRequestId = 'test-transfer-request-id';
    const userId = 'test-user-id';
    const userTeamId = 'buyer-team-id';

    vi.mocked(getUser).mockReturnValue(userId);

    // Mock manager
    const mockManager = {
      managerId: userId,
      team: {
        teamId: userTeamId,
        teamName: 'Buyer Team',
      },
    };

    // Mock transfer request with seller having no manager
    const mockTransferRequest = {
      id: transferRequestId,
      player: {
        playerId: 'player-id',
        firstName: 'John',
        surname: 'Doe',
      },
      buyer: {
        teamId: userTeamId,
        teamName: 'Buyer Team',
      },
      seller: {
        teamId: 'seller-team-id',
        teamName: 'Seller Team',
        manager: null, // No manager
      },
      value: 100000,
    };

    mockManagerRepository.getManagerById.mockResolvedValue(mockManager);
    mockTransferRepository.getTransferRequest.mockResolvedValue(mockTransferRequest);
    mockTransferRepository.deleteTransferRequest.mockResolvedValue(undefined);

    const event = createHttpEvent({
      body: { transferRequestId },
      httpMethod: 'POST',
    });

    const response = await handler(event, mockContext);

    expect(response.statusCode).toBe(200);
    expect(mockTransferRepository.deleteTransferRequest).toHaveBeenCalledWith(transferRequestId);
    expect(mockNotificationManagerInstance.transferRequestWithdrawn).not.toHaveBeenCalled();
  });

  it('should handle database errors gracefully', async () => {
    vi.mocked(getUser).mockReturnValue('test-user-id');
    const mockManager = {
      managerId: 'test-user-id',
      team: {
        teamId: 'team-id',
        teamName: 'Team Name',
      },
    };

    mockManagerRepository.getManagerById.mockResolvedValue(mockManager);
    mockTransferRepository.getTransferRequest.mockRejectedValue(new Error('Database error'));

    const event = createHttpEvent({
      body: { transferRequestId: 'test-id' },
      httpMethod: 'POST',
    });

    const response = await handler(event, mockContext);

    expect(response.statusCode).toBe(500);
    expect(JSON.parse(response.body)).toEqual({ error: 'Internal server error' });
  });
});
