import { cancelTransferRequestSchema } from '@/functions/transfers/schema.js';
import { httpMiddify } from '@/middleware/rest/index.js';
import { HttpEvent } from '@/middleware/rest/types.js';
import { NotificationManager } from '@/services/notifications/NotificationManager.js';
import { buildResponse } from '@/utils/buildResponse.js';
import { getUser } from '@/utils/getUser.js';
import { logger } from '@/utils/logger.js';

interface Body {
  transferRequestId: string;
}

const notificationManager = NotificationManager.getInstance();

/**
 * Lambda function to cancel a transfer request
 * Only the buyer (team that made the request) can cancel it
 * Removes the request from the database and notifies the seller if they have a manager
 */
export const main = async function (event: HttpEvent<Body, void, void>) {
  try {
    const { transferRepository, managerRepository } = event.context.repositories;
    const { transferRequestId } = event.body;

    logger.debug('Cancelling transfer request', { transferRequestId });

    // Get the current user ID
    const userId = getUser(event);
    if (!userId) {
      return buildResponse(401, JSON.stringify({ error: 'Unauthorized' }));
    }

    // Get the manager to find the team ID
    const manager = await managerRepository.getManagerById(userId);
    if (!manager || !manager.team) {
      return buildResponse(404, JSON.stringify({ error: 'Manager or team not found' }));
    }

    const userTeamId = manager.team.teamId;

    // Get the transfer request with all related data
    const transferRequest = await transferRepository.getTransferRequest(transferRequestId);
    if (!transferRequest) {
      return buildResponse(404, JSON.stringify({ error: 'Transfer request not found' }));
    }

    // Validate authorization - only the buyer can cancel the request
    if (transferRequest.buyer.teamId !== userTeamId) {
      logger.debug('User is not the buyer', {
        transferRequestId,
        userTeamId,
        buyerTeamId: transferRequest.buyer.teamId,
        sellerTeamId: transferRequest.seller.teamId,
      });
      return buildResponse(
        403,
        JSON.stringify({
          error: 'You are not authorized to cancel this transfer request. Only the buying team can cancel their own requests.',
        })
      );
    }

    // Delete the transfer request from the database
    await transferRepository.deleteTransferRequest(transferRequestId);

    // Send notification to the seller if they have a manager
    if (transferRequest.seller.manager) {
      notificationManager.assignManagerPreferences(transferRequest.seller.manager, event.context.repositories);
      await notificationManager.transferRequestWithdrawn(transferRequest, transferRequest.buyer.teamName);
    }

    logger.info('Transfer request cancelled successfully', {
      transferRequestId,
      playerId: transferRequest.player.playerId,
      buyerTeamId: transferRequest.buyer.teamId,
      sellerTeamId: transferRequest.seller.teamId,
      userId,
      userTeamId,
    });

    return buildResponse(
      200,
      JSON.stringify({
        message: 'Transfer request cancelled successfully',
        transfer: {
          id: transferRequestId,
          player: {
            id: transferRequest.player.playerId,
            name: `${transferRequest.player.firstName} ${transferRequest.player.surname}`,
          },
          buyer: transferRequest.buyer.teamId,
          seller: transferRequest.seller.teamId,
          value: transferRequest.value,
        },
      })
    );
  } catch (error) {
    logger.error('Failed to cancel transfer request', { error, body: event.body });
    return buildResponse(500, JSON.stringify({ error: 'Internal server error' }));
  }
};

export const handler = httpMiddify(main, {
  schema: cancelTransferRequestSchema,
});
