export const submitOfferSchema = {
  $schema: 'http://json-schema.org/draft-07/schema#',
  type: 'object',
  properties: {
    body: {
      type: 'object',
      properties: {
        myTeam: {
          type: 'string',
        },
        theirTeam: {
          type: 'string',
        },
        player: {
          type: 'string',
        },
        offer: {
          type: 'number',
        },
      },
      required: ['myTeam', 'theirTeam', 'player', 'offer'],
      additionalProperties: false,
    },
  },
  required: ['body'],
};

export const submitBidSchema = {
  $schema: 'http://json-schema.org/draft-07/schema#',
  type: 'object',
  properties: {
    body: {
      type: 'object',
      properties: {
        myTeam: {
          type: 'string',
        },
        player: {
          type: 'string',
        },
        maxBid: {
          type: 'number',
        },
      },
      required: ['myTeam', 'player', 'maxBid'],
      additionalProperties: false,
    },
  },
  required: ['body'],
};

export const acceptTransferRequestSchema = {
  $schema: 'http://json-schema.org/draft-07/schema#',
  type: 'object',
  properties: {
    body: {
      type: 'object',
      properties: {
        transferRequestId: {
          type: 'string',
        },
      },
      required: ['transferRequestId'],
      additionalProperties: false,
    },
  },
  required: ['body'],
};

export const cancelTransferRequestSchema = {
  $schema: 'http://json-schema.org/draft-07/schema#',
  type: 'object',
  properties: {
    body: {
      type: 'object',
      properties: {
        transferRequestId: {
          type: 'string',
        },
      },
      required: ['transferRequestId'],
      additionalProperties: false,
    },
  },
  required: ['body'],
};

export const releasePlayerSchema = {
  $schema: 'http://json-schema.org/draft-07/schema#',
  type: 'object',
  properties: {
    body: {
      type: 'object',
      properties: {
        playerId: {
          type: 'string',
        },
        teamId: {
          type: 'string',
        },
      },
      required: ['playerId', 'teamId'],
      additionalProperties: false,
    },
  },
  required: ['body'],
};
