import { releasePlayerSchema } from '@/functions/transfers/schema.js';
import { httpMiddify } from '@/middleware/rest/index.js';
import { HttpEvent } from '@/middleware/rest/types.js';
import { buildResponse } from '@/utils/buildResponse.js';
import { getUser } from '@/utils/getUser.js';
import { logger } from '@/utils/logger.js';

interface Body {
  playerId: string;
  teamId: string;
}

/**
 * Lambda function to release a player from a team
 * This sets the player's team to null and adds them to the transfer list
 */
export const main = async function (event: HttpEvent<Body, void, void>) {
  try {
    const { playerId, teamId } = event.body;
    const { playerRepository, transferRepository, managerRepository } = event.context.repositories;

    logger.debug('Releasing player from team', { playerId, teamId });

    // Get the current user ID
    const userId = getUser(event);
    if (!userId) {
      return buildResponse(401, JSON.stringify({ error: 'Unauthorized' }));
    }

    // Get the manager and verify they own the team
    const manager = await managerRepository.getManagerById(userId);
    if (!manager || !manager.team) {
      return buildResponse(404, JSON.stringify({ error: 'Manager or team not found' }));
    }

    // Verify the user owns the team they're trying to release the player from
    if (manager.team.teamId !== teamId) {
      return buildResponse(
        403,
        JSON.stringify({
          error: 'You are not authorized to release players from this team',
        })
      );
    }

    // Get the player to verify they exist and belong to the team
    const player = await playerRepository.getPlayer(manager.team.gameworldId, playerId);
    if (!player) {
      return buildResponse(404, JSON.stringify({ error: 'Player not found' }));
    }

    // Verify the player belongs to the team
    if (!player.team || player.team.teamId !== teamId) {
      return buildResponse(
        400,
        JSON.stringify({
          error: 'Player does not belong to this team',
        })
      );
    }

    // Remove the player from the team
    await playerRepository.removePlayerFromTeam(manager.team.gameworldId, playerId);
    logger.debug('Removed player from team', { playerId, teamId });

    // Add the player to the transfer list
    await transferRepository.addTransferListedPlayer(player);
    logger.debug('Added player to transfer list', { playerId });

    return buildResponse(
      200,
      JSON.stringify({
        message: 'Player released successfully',
        playerId,
        playerName: `${player.firstName} ${player.surname}`,
      })
    );
  } catch (error) {
    logger.error('Failed to release player', { error, body: event.body });
    return buildResponse(500, JSON.stringify({ error: 'Internal server error' }));
  }
};

export const handler = httpMiddify(main, {
  schema: releasePlayerSchema,
});
