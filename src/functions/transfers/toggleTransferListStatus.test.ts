import createHttpEvent from '@/testing/createHttpEvent.js';
import {
  mockManagerRepository,
  mockPlayerRepository,
  mockTransferRepository,
} from '@/testing/mockRepositories.ts';
import { getUser } from '@/utils/getUser.ts';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { handler } from './toggleTransferListStatus.js';

vi.mock('@/utils/getUser.js', () => ({
  getUser: vi.fn().mockReturnValue('test-user-id'),
}));

describe('toggleTransferListStatus', () => {
  const mockContext = {} as any;
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should return 404 if manager not found', async () => {
    vi.mocked(getUser).mockReturnValue('user1');
    mockManagerRepository.getManagerById.mockResolvedValue(null);
    const event = createHttpEvent({
      body: { playerId: 'p1', gameworldId: 'g1', isTransferListed: true },
      httpMethod: 'POST',
    });
    const response = await handler(event, mockContext);
    expect(response.statusCode).toBe(404);
    expect(JSON.parse(response.body)).toEqual({ error: 'Manager not found' });
  });

  it('should return 404 if player not found', async () => {
    vi.mocked(getUser).mockReturnValue('user1');
    mockManagerRepository.getManagerById.mockResolvedValue({
      team: { teamId: 't1' },
      role: 'manager',
    });
    mockPlayerRepository.getPlayer.mockResolvedValue(null);
    const event = createHttpEvent({
      body: { playerId: 'p1', gameworldId: 'g1', isTransferListed: true },
      httpMethod: 'POST',
    });
    const response = await handler(event, mockContext);
    expect(response.statusCode).toBe(404);
    expect(JSON.parse(response.body)).toEqual({ error: 'Player not found' });
  });

  it('should return 403 if manager does not own the team and is not admin', async () => {
    vi.mocked(getUser).mockReturnValue('user1');
    mockManagerRepository.getManagerById.mockResolvedValue({
      team: { teamId: 't1' },
      role: 'manager',
    });
    mockPlayerRepository.getPlayer.mockResolvedValue({
      team: { teamId: 't2' },
      isTransferListed: false,
    });
    const event = createHttpEvent({
      body: { playerId: 'p1', gameworldId: 'g1', isTransferListed: true },
      httpMethod: 'POST',
    });
    const response = await handler(event, mockContext);
    expect(response.statusCode).toBe(403);
    expect(JSON.parse(response.body)).toEqual({
      error: 'You cannot change the status of players in another team',
    });
  });

  it('should remove player from transfer list', async () => {
    vi.mocked(getUser).mockReturnValue('user1');
    mockManagerRepository.getManagerById.mockResolvedValue({
      team: { teamId: 't1' },
      role: 'manager',
    });
    mockPlayerRepository.getPlayer.mockResolvedValue({
      playerId: 'p1',
      team: { teamId: 't1' },
      isTransferListed: true,
    });
    mockTransferRepository.deleteTransferListedPlayer.mockResolvedValue(undefined);
    const event = createHttpEvent({
      body: { playerId: 'p1', gameworldId: 'g1', isTransferListed: false },
      httpMethod: 'POST',
    });
    const response = await handler(event, mockContext);
    expect(response.statusCode).toBe(200);
    expect(JSON.parse(response.body)).toEqual({ message: 'Player transfer status updated' });
    expect(mockTransferRepository.deleteTransferListedPlayer).toHaveBeenCalledWith(undefined, 'p1');
  });

  it('should add player to transfer list', async () => {
    vi.mocked(getUser).mockReturnValue('user1');
    mockManagerRepository.getManagerById.mockResolvedValue({
      team: { teamId: 't1' },
      role: 'manager',
    });
    mockPlayerRepository.getPlayer.mockResolvedValue({
      playerId: 'p1',
      team: { teamId: 't1' },
      isTransferListed: false,
    });
    mockTransferRepository.addTransferListedPlayer.mockResolvedValue(undefined);
    const event = createHttpEvent({
      body: { playerId: 'p1', gameworldId: 'g1', isTransferListed: true },
      httpMethod: 'POST',
    });
    const response = await handler(event, mockContext);
    expect(response.statusCode).toBe(200);
    expect(JSON.parse(response.body)).toEqual({ message: 'Player transfer status updated' });
    expect(mockTransferRepository.addTransferListedPlayer).toHaveBeenCalledWith({
      playerId: 'p1',
      team: { teamId: 't1' },
      isTransferListed: false,
    });
  });

  it('should return 200 if player is already on transfer list', async () => {
    vi.mocked(getUser).mockReturnValue('user1');
    mockManagerRepository.getManagerById.mockResolvedValue({
      team: { teamId: 't1' },
      role: 'manager',
    });
    mockPlayerRepository.getPlayer.mockResolvedValue({
      playerId: 'p1',
      team: { teamId: 't1' },
      isTransferListed: true,
    });
    const event = createHttpEvent({
      body: { playerId: 'p1', gameworldId: 'g1', isTransferListed: true },
      httpMethod: 'POST',
    });
    const response = await handler(event, mockContext);
    expect(response.statusCode).toBe(200);
    expect(JSON.parse(response.body)).toEqual({ message: 'Player already on transfer list' });
  });
});
