import { acceptTransferRequestSchema } from '@/functions/transfers/schema.js';
import { completePlayerTransfer } from '@/functions/transfers/transferUtils.js';
import { httpMiddify } from '@/middleware/rest/index.js';
import { HttpEvent } from '@/middleware/rest/types.js';
import { buildResponse } from '@/utils/buildResponse.js';
import { getUser } from '@/utils/getUser.js';
import { logger } from '@/utils/logger.js';

interface Body {
  transferRequestId: string;
}

/**
 * Lambda function to accept a transfer request
 * Handles two scenarios:
 * 1. Someone wants to buy our player and we (seller) are happy with the amount offered
 * 2. A seller has given us (buyer) a counter offer and we're happy with the price quoted
 */
export const main = async function (event: HttpEvent<Body, void, void>) {
  try {
    const { transferRepository, managerRepository } = event.context.repositories;
    const { transferRequestId } = event.body;

    logger.debug('Accepting transfer request', { transferRequestId });

    // Get the current user ID
    const userId = getUser(event);
    if (!userId) {
      return buildResponse(401, JSON.stringify({ error: 'Unauthorized' }));
    }

    // Get the manager to find the team ID
    const manager = await managerRepository.getManagerById(userId);
    if (!manager || !manager.team) {
      return buildResponse(404, JSON.stringify({ error: 'Manager or team not found' }));
    }

    const userTeamId = manager.team.teamId;

    // Get the transfer request with all related data (already populated by the repository method)
    const transferRequest = await transferRepository.getTransferRequest(transferRequestId);
    if (!transferRequest) {
      return buildResponse(404, JSON.stringify({ error: 'Transfer request not found' }));
    }

    // Determine which scenario we're in and validate authorization
    let transferValue: bigint;
    let isAuthorized = false;
    let scenario: 'seller_accepts_offer' | 'buyer_accepts_counter_offer';

    // Scenario 1: We are the seller and someone wants to buy our player
    if (transferRequest.seller.teamId === userTeamId) {
      isAuthorized = true;
      scenario = 'seller_accepts_offer';

      // Check if there's a valid counter offer that's more recent than the original offer
      if (
        transferRequest.counterOfferTime > 0n &&
        transferRequest.counterOfferTime > transferRequest.date
      ) {
        // Use the counter offer value if it exists and is more recent
        transferValue = transferRequest.counterOfferValue;
        logger.debug('Seller accepting counter offer', {
          transferRequestId,
          originalOffer: transferRequest.value,
          counterOffer: transferRequest.counterOfferValue,
          acceptedValue: transferValue,
        });
      } else {
        // Use the original offer value
        transferValue = transferRequest.value;
        logger.debug('Seller accepting original offer', {
          transferRequestId,
          acceptedValue: transferValue,
        });
      }
    }
    // Scenario 2: We are the buyer and the seller has given us a counter offer
    else if (transferRequest.buyer.teamId === userTeamId) {
      // We can only accept if there's a valid counter offer
      if (
        transferRequest.counterOfferTime > 0 &&
        transferRequest.counterOfferTime > transferRequest.date
      ) {
        isAuthorized = true;
        scenario = 'buyer_accepts_counter_offer';
        transferValue = transferRequest.counterOfferValue;

        logger.debug('Buyer accepting counter offer', {
          transferRequestId,
          originalOffer: transferRequest.value,
          counterOffer: transferRequest.counterOfferValue,
          acceptedValue: transferValue,
        });

        // Check if the buyer's team has enough money to complete the transfer
        if (manager.team.balance < transferValue) {
          return buildResponse(
            400,
            JSON.stringify({
              error: 'Your team does not have enough money to complete this transfer.',
            })
          );
        }
      } else {
        return buildResponse(
          400,
          JSON.stringify({
            error:
              'No valid counter offer to accept. Counter offers must be more recent than the original offer.',
          })
        );
      }
    } else {
      logger.debug('User is not buyer or seller', {
        transferRequestId,
        userTeamId,
        buyerTeamId: transferRequest.buyer.teamId,
        sellerTeamId: transferRequest.seller.teamId,
      });
      return buildResponse(
        403,
        JSON.stringify({
          error:
            'You are not authorized to accept this transfer request. You must be either the buyer or seller.',
        })
      );
    }

    if (!isAuthorized) {
      return buildResponse(
        403,
        JSON.stringify({
          error:
            'You are not authorized to accept this transfer request. You must be either the buyer or seller.',
        })
      );
    }

    // Complete the player transfer using the transfer utils
    await completePlayerTransfer(
      transferRequest.player,
      transferRequest.buyer,
      transferRequest.seller,
      Number(transferValue), // Explicitly convert to Number to avoid BigInt type issues
      event.context.repositories
    );

    // Delete the transfer request now that it's been completed
    await transferRepository.deleteTransferRequest(transferRequestId);

    logger.info('Transfer request accepted and completed', {
      transferRequestId,
      scenario,
      playerId: transferRequest.player.playerId,
      buyerTeamId: transferRequest.buyer.teamId,
      sellerTeamId: transferRequest.seller.teamId,
      transferValue,
      userId,
      userTeamId,
    });

    return buildResponse(
      200,
      JSON.stringify({
        message: 'Transfer request accepted successfully',
        transfer: {
          id: transferRequestId,
          player: {
            id: transferRequest.player.playerId,
            name: `${transferRequest.player.firstName} ${transferRequest.player.surname}`,
          },
          buyer: transferRequest.buyer.teamId,
          seller: transferRequest.seller.teamId,
          value: Number(transferValue),
          scenario,
        },
      })
    );
  } catch (error) {
    logger.error('Failed to accept transfer request', { error, body: event.body });
    return buildResponse(500, JSON.stringify({ error: 'Internal server error' }));
  }
};

export const handler = httpMiddify(main, {
  schema: acceptTransferRequestSchema,
});
