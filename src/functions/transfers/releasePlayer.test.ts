import createHttpEvent from '@/testing/createHttpEvent.js';
import {
  mockManagerRepository,
  mockPlayerRepository,
  mockTransferRepository,
  resetAllRepositoryMocks,
} from '@/testing/mockRepositories.js';
import { getUser } from '@/utils/getUser.js';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { handler } from './releasePlayer.js';

// Mock the getUser function
vi.mock('@/utils/getUser.js', () => ({
  getUser: vi.fn().mockReturnValue('test-user-id'),
}));

describe('releasePlayer', () => {
  const mockContext = {} as any;

  beforeEach(() => {
    vi.clearAllMocks();
    resetAllRepositoryMocks();
    vi.mocked(getUser).mockReturnValue('test-user-id');
  });

  it('should return 401 if user is not authenticated', async () => {
    vi.mocked(getUser).mockReturnValue('');

    const event = createHttpEvent({
      body: { playerId: 'test-player-id', teamId: 'test-team-id' },
      httpMethod: 'POST',
    });

    const response = await handler(event, mockContext);

    expect(response.statusCode).toBe(401);
    expect(JSON.parse(response.body)).toEqual({ error: 'Unauthorized' });
  });

  it('should return 404 if manager not found', async () => {
    mockManagerRepository.getManagerById.mockResolvedValue(null);

    const event = createHttpEvent({
      body: { playerId: 'test-player-id', teamId: 'test-team-id' },
      httpMethod: 'POST',
    });

    const response = await handler(event, mockContext);

    expect(response.statusCode).toBe(404);
    expect(JSON.parse(response.body)).toEqual({ error: 'Manager or team not found' });
  });

  it('should return 403 if user does not own the team', async () => {
    mockManagerRepository.getManagerById.mockResolvedValue({
      team: { teamId: 'different-team-id', gameworldId: 'test-gameworld' },
    });

    const event = createHttpEvent({
      body: { playerId: 'test-player-id', teamId: 'test-team-id' },
      httpMethod: 'POST',
    });

    const response = await handler(event, mockContext);

    expect(response.statusCode).toBe(403);
    expect(JSON.parse(response.body)).toEqual({
      error: 'You are not authorized to release players from this team',
    });
  });

  it('should return 404 if player not found', async () => {
    mockManagerRepository.getManagerById.mockResolvedValue({
      team: { teamId: 'test-team-id', gameworldId: 'test-gameworld' },
    });
    mockPlayerRepository.getPlayer.mockResolvedValue(null);

    const event = createHttpEvent({
      body: { playerId: 'test-player-id', teamId: 'test-team-id' },
      httpMethod: 'POST',
    });

    const response = await handler(event, mockContext);

    expect(response.statusCode).toBe(404);
    expect(JSON.parse(response.body)).toEqual({ error: 'Player not found' });
  });

  it('should return 400 if player does not belong to the team', async () => {
    mockManagerRepository.getManagerById.mockResolvedValue({
      team: { teamId: 'test-team-id', gameworldId: 'test-gameworld' },
    });
    mockPlayerRepository.getPlayer.mockResolvedValue({
      playerId: 'test-player-id',
      firstName: 'John',
      surname: 'Doe',
      team: { teamId: 'different-team-id' },
      isTransferListed: false,
    });

    const event = createHttpEvent({
      body: { playerId: 'test-player-id', teamId: 'test-team-id' },
      httpMethod: 'POST',
    });

    const response = await handler(event, mockContext);

    expect(response.statusCode).toBe(400);
    expect(JSON.parse(response.body)).toEqual({
      error: 'Player does not belong to this team',
    });
  });

  it('should return 400 if player has no team', async () => {
    mockManagerRepository.getManagerById.mockResolvedValue({
      team: { teamId: 'test-team-id', gameworldId: 'test-gameworld' },
    });
    mockPlayerRepository.getPlayer.mockResolvedValue({
      playerId: 'test-player-id',
      firstName: 'John',
      surname: 'Doe',
      team: null,
      isTransferListed: false,
    });

    const event = createHttpEvent({
      body: { playerId: 'test-player-id', teamId: 'test-team-id' },
      httpMethod: 'POST',
    });

    const response = await handler(event, mockContext);

    expect(response.statusCode).toBe(400);
    expect(JSON.parse(response.body)).toEqual({
      error: 'Player does not belong to this team',
    });
  });

  it('should successfully release a player', async () => {
    const mockPlayer = {
      playerId: 'test-player-id',
      firstName: 'John',
      surname: 'Doe',
      team: { teamId: 'test-team-id' },
      isTransferListed: false,
    };

    mockManagerRepository.getManagerById.mockResolvedValue({
      team: { teamId: 'test-team-id', gameworldId: 'test-gameworld' },
    });
    mockPlayerRepository.getPlayer.mockResolvedValue(mockPlayer);
    mockPlayerRepository.removePlayerFromTeam.mockResolvedValue({});
    mockTransferRepository.addTransferListedPlayer.mockResolvedValue({});

    const event = createHttpEvent({
      body: { playerId: 'test-player-id', teamId: 'test-team-id' },
      httpMethod: 'POST',
    });

    const response = await handler(event, mockContext);

    expect(response.statusCode).toBe(200);
    expect(JSON.parse(response.body)).toEqual({
      message: 'Player released successfully',
      playerId: 'test-player-id',
      playerName: 'John Doe',
    });

    expect(mockPlayerRepository.removePlayerFromTeam).toHaveBeenCalledWith(
      'test-gameworld',
      'test-player-id'
    );
    expect(mockTransferRepository.addTransferListedPlayer).toHaveBeenCalledWith(mockPlayer);
  });

  it('should return 500 if an error occurs during processing', async () => {
    mockManagerRepository.getManagerById.mockRejectedValue(new Error('Database error'));

    const event = createHttpEvent({
      body: { playerId: 'test-player-id', teamId: 'test-team-id' },
      httpMethod: 'POST',
    });

    const response = await handler(event, mockContext);

    expect(response.statusCode).toBe(500);
    expect(JSON.parse(response.body)).toEqual({ error: 'Internal server error' });
  });
});
