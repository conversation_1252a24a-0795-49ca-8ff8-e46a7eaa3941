import { submitBidSchema } from '@/functions/transfers/schema.js';
import { httpMiddify } from '@/middleware/rest/index.js';
import { HttpEvent } from '@/middleware/rest/types.js';
import { buildResponse } from '@/utils/buildResponse.js';

interface Body {
  player: string;
  maxBid: number;
  myTeam: string;
}

export const main = async function (event: HttpEvent<Body, void, void>) {
  const { transferRepository } = event.context.repositories;

  const { player, maxBid, myTeam } = event.body;

  const { maxBid: newMaxBid, highestBidder } = await transferRepository.submitBid(
    player,
    maxBid,
    myTeam,
    event.context.repositories
  );

  return buildResponse(200, JSON.stringify({ maxBid: newMaxBid, highestBidder }));
};
export const handler = httpMiddify(main, {
  schema: submitBidSchema,
});
