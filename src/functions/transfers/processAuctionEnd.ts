import { BidHistory } from '@/entities/BidHistory.js';
import { Player } from '@/entities/Player.js';
import { TransferListedPlayer } from '@/entities/TransferListedPlayer.js';
import { completePlayerTransfer } from '@/functions/transfers/transferUtils.js';
import { Repositories } from '@/middleware/database/types.js';
import { eventMiddify } from '@/middleware/event/index.js';
import { EventHandler } from '@/middleware/event/types.js';
import { NotificationManager } from '@/services/notifications/NotificationManager.js';
import { SQS } from '@/services/sqs/sqs.js';
import { Auction, TransferRepository } from '@/storage-interface/transfers/index.js';
import { GenerateUnattachedPlayersEvent } from '@/types/generated/index.js';
import { logger } from '@/utils/logger.js';
import { tracer } from '@/utils/tracer.js';
import { Collection } from '@mikro-orm/core';

const notificationManager = NotificationManager.getInstance();
const sqs = new SQS({ tracer });

async function restartAuction(
  auction: TransferListedPlayer,
  transferRepository: TransferRepository
) {
  auction.auctionListingCounter++;
  auction.bidHistory.removeAll(); // Clear the bid history for the new auction
  auction.auctionEndTime = auction.auctionEndTime + BigInt(1000 * 60 * 60 * 24); // 24 hours from last start time
  auction.auctionStartPrice = auction.auctionStartPrice * 0.9; // Reduce start price by 10%
  await transferRepository.updateTransferListedPlayer(auction);
}

async function emailManagerAuctionFailed(
  managerId: string,
  repositories: Repositories,
  auction: Auction
) {
  await notificationManager.loadManagerPreferences(managerId, repositories);
  await notificationManager.auctionFailed(auction);
}

async function getWinningBid(
  currentBidAmount: number,
  bidHistory: Collection<BidHistory>,
  player: Player
) {
  // sort bids by maximum bid amount
  const sortedBids = bidHistory.getItems().sort((a, b) => b.maximumBid - a.maximumBid);
  // starting with the highest bid, check if the team has enough balance
  for (let i = 0; i < sortedBids.length; i++) {
    const bid = sortedBids[i]!;
    const team = bid.team;
    const bidAmount = i === 0 ? currentBidAmount : bid.maximumBid;
    if (team.balance >= bidAmount) {
      return { bid, bidAmount };
    }
    if (team.manager) {
      await notificationManager.bidFailed(currentBidAmount, player);
    }
  }
  return null;
}

const gameworldIdsToProcess = new Set<string>();

async function noValidBids(auction: Auction, repositories: Repositories) {
  if (auction.auctionListingCounter < 2) {
    logger.info(
      `Restarting auction for player ${auction.player.playerId}, attempt ${auction.auctionListingCounter + 1}`
    );
    await restartAuction(auction, repositories.transferRepository);
  } else {
    logger.info(`Deleting auction for player ${auction.player.playerId} after 3 failed attempts`);
    if (auction.player.team) {
      const team = auction.player.team;
      if (team.manager) {
        const manager = team.manager;
        if (manager.email) {
          await emailManagerAuctionFailed(manager.managerId, repositories, auction);
        }
      }
    }
    await repositories.transferRepository.deleteTransferListedPlayer(auction.id);
  }
}

export const main: EventHandler<void, void> = async function (event) {
  const { transferRepository } = event.context.repositories;

  logger.info('Processing completed auctions');
  const completedAuctions = await transferRepository.getCompletedAuctions();
  logger.info(`Found ${completedAuctions.length} completed auctions`);

  // if any auction has not had any bids, increment the listing counter and restart the auction
  // if any auction has already run 3 times then delete it instead
  for (const auction of completedAuctions) {
    gameworldIdsToProcess.add(auction.gameworldId);
    if (auction.bidHistory.length === 0) {
      await noValidBids(auction, event.context.repositories);
    } else {
      // Process successful auctions here
      logger.info(
        `Auction for player ${auction.player.playerId} completed with ${auction.bidHistory.length} bids`
      );
      // Get the winning bid
      const result = await getWinningBid(
        auction.auctionCurrentPrice,
        auction.bidHistory,
        auction.player
      );
      if (!result) {
        await noValidBids(auction, event.context.repositories);
      } else {
        const winningTeam = result.bid.team;
        const sellingTeam = auction.player.team || null;

        logger.debug('About to complete player transfer', { auction });
        await completePlayerTransfer(
          auction.player,
          winningTeam,
          sellingTeam,
          result.bidAmount,
          event.context.repositories
        );
        // remove the player from the auction
        await transferRepository.deleteTransferListedPlayer(auction.id);
      }
    }
  }

  // for every gameworld we've removed players from, ensure that there are at least 100 players in the transfer list
  for (const gameworldId of gameworldIdsToProcess) {
    const activeAuctions = await transferRepository.getActiveAuctions(gameworldId);
    logger.debug('activeAuctions', { activeAuctions });
    if (activeAuctions < 100) {
      logger.info(`Not enough active auctions. Generating ${100 - activeAuctions} new players`);

      const unattachedPlayersEvent: GenerateUnattachedPlayersEvent = {
        gameworldId: gameworldId,
        requiredPlayers: 100 - activeAuctions,
      };
      await sqs.send(
        process.env.UNATTACHED_PLAYERS_QUEUE_URL!,
        JSON.stringify(unattachedPlayersEvent)
      );
    }
  }
};

export const handler = eventMiddify(main);
