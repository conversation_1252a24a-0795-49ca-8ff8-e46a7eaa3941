import { eventMiddify } from '@/middleware/event/index.js';
import { EventHandler } from '@/middleware/event/types.js';
import { SQS } from '@/services/sqs/sqs.js';
import { SimulateAiTransfers } from '@/types/generated/simulate-ai-transfers.js';
import { logger } from '@/utils/logger.js';
import { tracer } from '@/utils/tracer.js';
import { SendMessageBatchRequestEntry } from '@aws-sdk/client-sqs/dist-types/models/models_0.js';

const sqsClient = new SQS({ tracer });

export const main: EventHandler<void, void> = async (event) => {
  const { teamRepository } = event.context.repositories;

  const aiTeams = await teamRepository.getTeamsWithoutManager(false);
  if (!aiTeams || aiTeams.length === 0) {
    logger.debug('No AI teams found to process');
    return;
  }

  const aiTransfersQueueUrl = process.env.AI_TRANSFERS_QUEUE_URL;

  if (!aiTransfersQueueUrl) {
    throw new Error('AI_TRANSFERS_QUEUE_URL is not defined');
  }

  const payloads: SendMessageBatchRequestEntry[] = aiTeams.map((record) => {
    return {
      Id: record.teamId,
      MessageBody: JSON.stringify({
        teamId: record.teamId,
        gameworldId: record.gameworldId,
      } as SimulateAiTransfers),
    };
  });

  await sqsClient.sendBatch(aiTransfersQueueUrl, payloads);
  logger.debug(`${aiTeams.length} AI transfers simulation messages sent to SQS`);
};

export const handler = eventMiddify(main);
