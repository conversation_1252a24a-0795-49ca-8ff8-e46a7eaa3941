import { httpMiddify } from '@/middleware/rest/index.js';
import { HttpEvent } from '@/middleware/rest/types.js';
import { buildResponse } from '@/utils/buildResponse.js';
import { getUser } from '@/utils/getUser.js';
import { logger } from '@/utils/logger.js';
import { englishDataset, englishRecommendedTransformers, RegExpMatcher } from 'obscenity';

interface RequestBody {
  firstName?: string;
  lastName?: string;
}

const matcher = new RegExpMatcher({
  ...englishDataset.build(),
  ...englishRecommendedTransformers,
});

export type UpdateManagerNameEvent = HttpEvent<RequestBody, void, void>;

export const main = async function (event: UpdateManagerNameEvent) {
  const { managerRepository } = event.context.repositories;

  // Get the current user ID from the event
  const userId = getUser(event);

  // Check if the manager exists
  const manager = await managerRepository.getManagerById(userId);
  if (!manager) {
    return buildResponse(404, JSON.stringify({ error: 'Manager not found' }));
  }

  // Extract first and last name from the request body
  const { firstName, lastName } = event.body || {};

  // Validate that at least one field is provided
  if (!firstName && !lastName) {
    return buildResponse(
      400,
      JSON.stringify({ error: 'At least one of firstName or lastName must be provided' })
    );
  }

  if ((firstName && matcher.hasMatch(firstName)) || (lastName && matcher.hasMatch(lastName))) {
    return buildResponse(406, JSON.stringify({ error: 'Oi! Children present!' }));
  }

  // Create updates object with only the fields that were provided
  const updates: { firstName?: string; lastName?: string } = {};
  if (firstName !== undefined) updates.firstName = firstName;
  if (lastName !== undefined) updates.lastName = lastName;

  try {
    // Update the manager
    await managerRepository.updateManagerById(userId, updates);

    // Get the updated manager to return in the response
    const updatedManager = await managerRepository.getManagerById(userId);

    return buildResponse(200, JSON.stringify(updatedManager));
  } catch (error) {
    logger.error('Error updating manager:', { error });
    return buildResponse(500, JSON.stringify({ error: 'Failed to update manager' }));
  }
};

export const handler = httpMiddify(main, {});
