import { Manager } from '@/entities/Manager.js';
import { httpMiddify } from '@/middleware/rest/index.js';
import { HttpEvent } from '@/middleware/rest/types.js';
import { ManagerRestorationServiceImpl } from '@/services/manager/manager-restoration-service.ts';
import { getMikroOrmService } from '@/storage-interface/database-initializer.js';
import { buildResponse } from '@/utils/buildResponse.js';
import { getUser } from '@/utils/getUser.js';

interface PathParameters {
  managerId?: string;
}
export type GetManagerEvent = HttpEvent<void, PathParameters, void>;

export const main = async function (event: GetManagerEvent) {
  const { managerRepository } = event.context.repositories;

  let thisIsMe = true;
  let userId = getUser(event);
  if (event.pathParameters && event.pathParameters.managerId) {
    userId = event.pathParameters.managerId;
    thisIsMe = false;
  }

  let manager: Manager | null = null;
  try {
    manager = await managerRepository.getManagerById(userId);
    if (manager && thisIsMe && manager.isArchived) {
      const mikroOrmService = await getMikroOrmService();
      const em = mikroOrmService.getEntityManager();
      const managerRestorationService = new ManagerRestorationServiceImpl(em);
      manager = await managerRestorationService.restoreManager(userId);
    }
  } catch (error) {}
  if (!manager) {
    return buildResponse(404, JSON.stringify({ error: 'Manager not found' }));
  } else {
    return buildResponse(200, JSON.stringify(manager));
  }
};

export const handler = httpMiddify(main, {});
