import { ArchivedManager } from '@/entities/ArchivedManager.js';
import { ArchivedPlayer } from '@/entities/ArchivedPlayer.js';
import { ArchivedPlayerAttributes } from '@/entities/ArchivedPlayerAttributes.js';
import { ArchivedPlayerOverallStats } from '@/entities/ArchivedPlayerOverallStats.js';
import { ArchivedTeam } from '@/entities/ArchivedTeam.js';
import { Fixture } from '@/entities/Fixture.js';
import { Gameworld } from '@/entities/Gameworld.js';
import { League } from '@/entities/League.js';
import { LeagueRules } from '@/entities/LeagueRules.js';
import { Manager } from '@/entities/Manager.js';
import { Player } from '@/entities/Player.js';
import { PlayerAttributes } from '@/entities/PlayerAttributes.ts';
import { PlayerMatchHistory } from '@/entities/PlayerMatchHistory.js';
import { PlayerOverallStats } from '@/entities/PlayerOverallStats.js';
import { Team } from '@/entities/Team.js';
import { GameworldFactory } from '@/testing/factories/gameworldFactory.js';
import { LeagueFactory } from '@/testing/factories/leagueFactory.js';
import { ManagerFactory } from '@/testing/factories/managerFactory.js';
import { PlayerAttributesFactory } from '@/testing/factories/playerAttributesFactory.js';
import { PlayerFactory } from '@/testing/factories/playerFactory.js';
import { TeamsFactory } from '@/testing/factories/teamFactory.js';
import { logger } from '@/utils/logger.js';
import { Collection, MikroORM } from '@mikro-orm/core';
import { PostgreSqlDriver } from '@mikro-orm/postgresql';
import { v4 as uuidv4 } from 'uuid';
import { afterAll, beforeAll, beforeEach, describe, expect, it, vi } from 'vitest';
import mikroOrmConfig from '../../mikro-orm.config.js';
import { handler } from './processIdleManagers.js';

describe('processIdleManagers E2E Test', () => {
  let orm: MikroORM<PostgreSqlDriver>;
  let gameworld: Gameworld;
  let league: League;
  let idleManager: Manager;
  let activeManager: Manager;
  let idleTeam: Team;
  let activeTeam: Team;
  let idlePlayers: Player[];
  let activePlayers: Player[];
  const mockContext = {} as any;

  beforeAll(async () => {
    if (!process.env.DATABASE_URL?.includes('localhost')) {
      throw new Error('Tests aborted: DATABASE_URL must be localhost');
    }

    vi.restoreAllMocks();
    vi.resetModules();
    vi.unmock('../../storage-interface/database-initializer.js');

    await MikroORM.init<PostgreSqlDriver>(mikroOrmConfig)
      .then(async (_orm) => {
        orm = _orm;
        // Create schema
        await _orm.em.getConnection().execute('CREATE EXTENSION IF NOT EXISTS "uuid-ossp";');
        await _orm.schema.refreshDatabase({ dropDb: false });
      })
      .catch((error) => {
        logger.error('Failed to initialize database', { error });
      });
  });

  afterAll(async () => {
    await orm.close();
  });

  beforeEach(async () => {
    // Clear all data
    await clearTestData();

    // Create test data
    await createTestData();
  });

  async function clearTestData() {
    await orm.em.nativeDelete(Fixture, {});
    await orm.em.nativeDelete(ArchivedPlayerAttributes, {});
    await orm.em.nativeDelete(ArchivedPlayerOverallStats, {});
    await orm.em.nativeDelete(ArchivedPlayer, {});
    await orm.em.nativeDelete(ArchivedTeam, {});
    await orm.em.nativeDelete(ArchivedManager, {});
    await orm.em.nativeDelete(Player, {});
    await orm.em.nativeDelete(Manager, {});
    await orm.em.nativeDelete(Team, {});
    await orm.em.nativeDelete(LeagueRules, {});
    await orm.em.nativeDelete(League, {});
    await orm.em.nativeDelete(Gameworld, {});
  }

  async function createTestData() {
    const em = orm.em.fork();

    // Create gameworld
    gameworld = new Gameworld();
    Object.assign(
      gameworld,
      GameworldFactory.build({
        endDate: BigInt(Date.now() + 365 * 24 * 60 * 60 * 1000),
        highestManageableTier: 3,
      })
    );

    // Create league
    league = new League();
    const leagueRules = new LeagueRules();
    Object.assign(league, LeagueFactory.build({ tier: 1, name: 'Test League' }));
    Object.assign(leagueRules, {
      promotionSpots: 1,
      relegationSpots: 1,
      teamCount: 4,
      maximumPrize: 100000,
      minimumPrize: 10000,
      league: league,
    });
    league.leagueRules = leagueRules;
    league.gameworld = gameworld;
    league.leagueChildren = new Collection<League>(league, []);
    league.teams = new Collection<Team>(league, []);
    gameworld.leagues = new Collection<League>(gameworld, [league]);

    // Create idle manager (inactive for 3 weeks)
    const threeWeeksAgo = Date.now() - 21 * 24 * 60 * 60 * 1000;
    idleManager = new Manager();
    Object.assign(
      idleManager,
      ManagerFactory.build({
        managerId: uuidv4(),
        createdAt: BigInt(Date.now() - 30 * 24 * 60 * 60 * 1000),
        lastActive: BigInt(threeWeeksAgo),
        firstName: 'Idle',
        lastName: 'Manager',
        email: '<EMAIL>',
        gameworldId: gameworld.id,
        isArchived: false,
        wins: 10,
        draws: 5,
        defeats: 3,
        goalsScored: 25,
        goalsConceded: 15,
        scoutTokens: 5,
      })
    );

    // Create active manager (active yesterday)
    const yesterday = Date.now() - 1 * 24 * 60 * 60 * 1000;
    activeManager = new Manager();
    Object.assign(
      activeManager,
      ManagerFactory.build({
        managerId: uuidv4(),
        createdAt: BigInt(Date.now() - 30 * 24 * 60 * 60 * 1000),
        lastActive: BigInt(yesterday),
        firstName: 'Active',
        lastName: 'Manager',
        email: '<EMAIL>',
        gameworldId: gameworld.id,
        isArchived: false,
        wins: 1,
        draws: 50,
        defeats: 30,
        goalsScored: 2,
        goalsConceded: 1,
        scoutTokens: 58,
      })
    );

    // Create idle team
    idleTeam = new Team();
    Object.assign(
      idleTeam,
      TeamsFactory.build({
        teamId: uuidv4(),
        gameworldId: gameworld.id,
        tier: 1,
        teamName: 'Idle FC',
        balance: 1000000,
        points: 15,
        wins: 5,
        draws: 1,
        losses: 1,
      })
    );
    idleTeam.players = new Collection<Player>(idleTeam, []);
    idleTeam.league = league;
    idleTeam.manager = idleManager;
    idleManager.team = idleTeam;
    league.teams.add(idleTeam);

    // Create active team
    activeTeam = new Team();
    Object.assign(
      activeTeam,
      TeamsFactory.build({
        teamId: uuidv4(),
        gameworldId: gameworld.id,
        tier: 1,
        teamName: 'Active United',
        balance: 10000,
        points: 5,
        wins: 57,
        draws: 16,
        losses: 14,
      })
    );
    activeTeam.players = new Collection<Player>(activeTeam, []);
    activeTeam.league = league;
    activeTeam.manager = activeManager;
    activeManager.team = activeTeam;
    league.teams.add(activeTeam);

    // Create players for idle team
    idlePlayers = [];
    for (let i = 0; i < 3; i++) {
      const player = await createPlayerWithStats(em, idleTeam, `IdlePlayer${i}`);
      idlePlayers.push(player);
    }

    // Create players for active team
    activePlayers = [];
    for (let i = 0; i < 2; i++) {
      const player = await createPlayerWithStats(em, activeTeam, `ActivePlayer${i}`);
      activePlayers.push(player);
    }

    // Persist all entities
    em.persist([gameworld]);
    await em.flush();
  }

  async function createPlayerWithStats(em: any, team: Team, name: string): Promise<Player> {
    const player = new Player();
    Object.assign(player, PlayerFactory.build({ firstName: name, gameworldId: team.gameworldId }));
    player.team = team;
    const attributes = new PlayerAttributes();
    Object.assign(attributes, PlayerAttributesFactory.build());
    player.attributes = attributes;
    attributes.player = player;
    team.players.add(player);

    // Create overall stats
    const stats = new PlayerOverallStats();
    stats.player = player;
    stats.yellowCards = 2;
    stats.redCards = 0;
    stats.goals = 5;
    stats.shots = 20;
    stats.passesAttempted = 100;
    player.overallStats = stats;

    // Create match history
    const matchHistory = new PlayerMatchHistory();
    matchHistory.player = player;
    matchHistory.fixtureId = uuidv4();
    matchHistory.goals = 1;
    matchHistory.shots = 3;
    matchHistory.passesAttempted = 25;
    player.matchHistory = new Collection<PlayerMatchHistory>(player, [matchHistory]);

    em.persist([player]);
    return player;
  }

  it('should archive idle managers and reset their teams for AI management', async () => {
    // Arrange
    const mockEvent = {
      source: 'aws.events',
      'detail-type': 'Scheduled Event',
      detail: {},
    } as any;

    let em = orm.em.fork();

    // Get initial counts
    const initialManagerCount = await em.count(Manager, { gameworldId: gameworld.id });
    const initialTeamCount = await em.count(Team, { gameworldId: gameworld.id });
    const initialPlayerCount = await em.count(Player, { gameworldId: gameworld.id });
    const initialMatchHistoryCount = await em.count(PlayerMatchHistory, {});
    const initialPlayerAttributesCount = await em.count(PlayerAttributes, {});

    expect(initialManagerCount).toBe(2); // idle + active
    expect(initialTeamCount).toBe(2);
    expect(initialPlayerCount).toBe(5); // 3 idle + 2 active
    expect(initialMatchHistoryCount).toBe(5); // Should be some match history
    expect(initialPlayerAttributesCount).toBe(5); // Should be some player attributes

    // Act
    await handler(mockEvent, mockContext);

    // wait a second
    await new Promise((resolve) => setTimeout(resolve, 1000));

    // Assert - Refresh entity manager to see changes
    em = orm.em.fork();

    // Check that idle manager was marked as archived
    const allManagers = await em.find(Manager, {});
    expect(allManagers).toHaveLength(2); // Both managers should still exist

    const updatedIdleManager = allManagers.find((m) => m.managerId === idleManager.managerId);
    const updatedActiveManager = allManagers.find((m) => m.managerId === activeManager.managerId);

    expect(updatedIdleManager).toBeDefined();
    expect(updatedActiveManager).toBeDefined();
    expect(updatedIdleManager!.isArchived).toBe(true);
    expect(updatedActiveManager!.isArchived).toBe(false);

    // Check that teams still exist but idle team has no manager
    const teams = await em.find(Team, { gameworldId: gameworld.id }, { populate: ['manager'] });
    expect(teams).toHaveLength(2);

    const updatedIdleTeam = teams.find((t) => t.teamId === idleTeam.teamId);
    const updatedActiveTeam = teams.find((t) => t.teamId === activeTeam.teamId);

    expect(updatedIdleTeam).toBeDefined();
    expect(updatedActiveTeam).toBeDefined();
    expect(updatedIdleTeam!.manager).toBeNull();
    expect(updatedActiveTeam!.manager).toBeDefined();
    expect(updatedIdleTeam!.teamName).not.toBe('Idle FC'); // Should have new random name
    expect(updatedActiveTeam!.teamName).toBe('Active United'); // Should remain unchanged

    // Check that players still exist but match history was cleaned up
    const remainingPlayers = await em.find(Player, { gameworldId: gameworld.id });
    expect(remainingPlayers).toHaveLength(5); // All players should remain

    const remainingMatchHistory = await em.count(PlayerMatchHistory, {});
    expect(remainingMatchHistory).toBeLessThan(initialMatchHistoryCount); // Should be cleaned up for idle players

    // Check archived entities were created
    const archivedManagers = await em.find(ArchivedManager, {});
    expect(archivedManagers).toHaveLength(1);

    const archivedManager = archivedManagers[0]!;
    expect(archivedManager.managerId).toBe(idleManager.managerId);
    expect(archivedManager.firstName).toBe('Idle');
    expect(archivedManager.lastName).toBe('Manager');
    expect(archivedManager.scoutTokens).toBe(5);
    expect(archivedManager.wins).toBe(10);
    expect(archivedManager.originalTeamId).toBe(idleTeam.teamId);

    const archivedTeams = await em.find(ArchivedTeam, {});
    expect(archivedTeams).toHaveLength(1);

    const archivedTeam = archivedTeams[0]!;
    expect(archivedTeam.teamName).toBe('Idle FC');
    expect(archivedTeam.balance).toBe(1000000);
    expect(archivedTeam.points).toBe(15);
    expect(archivedTeam.originalTeamId).toBe(idleTeam.teamId);

    const archivedPlayers = await em.find(ArchivedPlayer, {});
    expect(archivedPlayers).toHaveLength(3); // Only idle team players

    const archivedAttributes = await em.find(ArchivedPlayerAttributes, {});
    expect(archivedAttributes).toHaveLength(3);

    const archivedStats = await em.find(ArchivedPlayerOverallStats, {});
    expect(archivedStats).toHaveLength(3);

    // Verify data integrity in archived entities
    for (const archivedPlayer of archivedPlayers) {
      expect(archivedPlayer.firstName).toContain('IdlePlayer');
      expect(archivedPlayer.originalPlayerId).toBeDefined();
      expect(archivedPlayer.archivedAt).toBeGreaterThan(0);
    }

    logger.info('Idle manager archival test completed successfully', {
      archivedManagerCount: archivedManagers.length,
      archivedTeamCount: archivedTeams.length,
      archivedPlayerCount: archivedPlayers.length,
      totalManagerCount: allManagers.length,
      archivedManagersCount: allManagers.filter((m) => m.isArchived).length,
      newTeamName: updatedIdleTeam!.teamName,
    });
  });

  it('should not archive active managers', async () => {
    // Arrange
    const mockEvent = {
      source: 'aws.events',
      'detail-type': 'Scheduled Event',
      detail: {},
    } as any;

    // Act
    await handler(mockEvent, mockContext);

    // Assert
    const em = orm.em.fork();

    // Active manager should still exist
    const activeManagerStillExists = await em.findOne(Manager, {
      managerId: activeManager.managerId,
    });
    expect(activeManagerStillExists).toBeDefined();

    // No archived manager should be created for active manager
    const archivedManagers = await em.find(ArchivedManager, { managerId: activeManager.managerId });
    expect(archivedManagers).toHaveLength(0);
  });
});
