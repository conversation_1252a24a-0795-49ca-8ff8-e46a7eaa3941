import { Gameworld } from '@/entities/Gameworld.js';
import { Manager } from '@/entities/Manager.js';
import { Player } from '@/entities/Player.js';
import { PlayerAttributes } from '@/entities/PlayerAttributes.js';
import { Repositories } from '@/middleware/database/types.js';
import { eventMiddify } from '@/middleware/event/index.js';
import { EventHandler, EventWithRepositories } from '@/middleware/event/types.js';
import { IdleManagerDetectionServiceImpl } from '@/services/manager/idle-manager-detection-service.js';
import { ManagerArchivalServiceImpl } from '@/services/manager/manager-archival-service.js';
import { NotificationManager } from '@/services/notifications/NotificationManager.ts';
import { TeamResetServiceImpl } from '@/services/team/team-reset-service.js';
import { getMikroOrmService } from '@/storage-interface/database-initializer.js';
import { logger } from '@/utils/logger.js';
import { jsonStringifySafe } from '@/utils/misc.js';

interface IdleManagerProcessingResult {
  gameworldId: string;
  processedManagers: number;
  archivedManagers: string[];
  errors: string[];
}

const notificationManager = NotificationManager.getInstance();

/**
 * Lambda handler for processing idle managers
 * Triggered daily at 2am UTC to check for managers inactive for over 2 weeks
 */
export const main: EventHandler<EventWithRepositories, void> = async (event): Promise<void> => {
  logger.info('Starting idle manager processing');

  const mikroOrmService = await getMikroOrmService();
  const em = mikroOrmService.getEntityManager();

  try {
    // Get all gameworlds
    const gameworlds = await em.find(Gameworld, {});

    logger.info('Processing idle managers for gameworlds', {
      gameworldCount: gameworlds.length,
      gameworldIds: gameworlds.map((g) => g.id),
    });

    const results: IdleManagerProcessingResult[] = [];

    // Process each gameworld
    for (const gameworld of gameworlds) {
      const result = await processGameworldIdleManagers(
        em,
        gameworld.id,
        event.context.repositories
      );
      results.push(result);
    }

    // Log summary
    const totalProcessed = results.reduce((sum, r) => sum + r.processedManagers, 0);
    const totalErrors = results.reduce((sum, r) => sum + r.errors.length, 0);

    logger.info('Idle manager processing completed', {
      totalGameworlds: gameworlds.length,
      totalProcessedManagers: totalProcessed,
      totalErrors,
      results: jsonStringifySafe(results),
    });

    if (totalErrors > 0) {
      logger.warn('Some errors occurred during idle manager processing', {
        totalErrors,
        errorDetails: results.flatMap((r) => r.errors),
      });
    }
  } catch (error) {
    logger.error('Fatal error in idle manager processing', {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
    });
    throw error;
  } finally {
    await em.getConnection().close();
  }
};

/**
 * Process idle managers for a specific gameworld
 */
async function processGameworldIdleManagers(
  em: any,
  gameworldId: string,
  repositories: Repositories
): Promise<IdleManagerProcessingResult> {
  const result: IdleManagerProcessingResult = {
    gameworldId,
    processedManagers: 0,
    archivedManagers: [],
    errors: [],
  };

  try {
    logger.info('Processing idle managers for gameworld', { gameworldId });

    // Initialize services
    const detectionService = new IdleManagerDetectionServiceImpl(em);

    // Find idle managers
    const idleManagers = await detectionService.findIdleManagers(gameworldId);

    if (idleManagers.length === 0) {
      logger.info('No idle managers found', { gameworldId });
      return result;
    }

    logger.info('Found idle managers to process', {
      gameworldId,
      count: idleManagers.length,
      managerIds: idleManagers.map((m) => m.managerId),
    });

    // Process each idle manager
    for (const manager of idleManagers) {
      try {
        await processIdleManager(em, manager);
        notificationManager.assignManagerPreferences(manager, repositories);
        await notificationManager.teamArchived();

        result.processedManagers++;
        result.archivedManagers.push(manager.managerId);

        logger.info('Successfully processed idle manager', {
          managerId: manager.managerId,
          gameworldId,
        });
      } catch (error) {
        const errorMessage = `Failed to process manager ${manager.managerId}: ${error instanceof Error ? error.message : String(error)}`;
        result.errors.push(errorMessage);

        logger.error('Error processing idle manager', {
          managerId: manager.managerId,
          gameworldId,
          error: error instanceof Error ? error.message : String(error),
        });
      }
    }

    // Commit all changes for this gameworld
    await em.flush();

    logger.info('Completed processing idle managers for gameworld', {
      gameworldId,
      processedCount: result.processedManagers,
      errorCount: result.errors.length,
    });
  } catch (error) {
    const errorMessage = `Failed to process gameworld ${gameworldId}: ${error instanceof Error ? error.message : String(error)}`;
    result.errors.push(errorMessage);

    logger.error('Error processing gameworld', {
      gameworldId,
      error: error instanceof Error ? error.message : String(error),
    });
  }

  return result;
}

/**
 * Process a single idle manager
 */
async function processIdleManager(em: any, manager: Manager): Promise<void> {
  logger.info('Processing idle manager', {
    managerId: manager.managerId,
    teamId: manager.team?.teamId,
    lastActive: new Date(Number(manager.lastActive)).toISOString(),
  });

  // Ensure we have the team and players loaded
  if (!manager.team) {
    throw new Error(`Manager ${manager.managerId} has no team to process`);
  }

  const team = manager.team;

  const players = await em.find(Player, { team: team.teamId, gameworldId: team.gameworldId });
  await em.find(PlayerAttributes, { player: { $in: players.map((p: Player) => p.playerId) } });

  logger.info('Loaded team data for archival', {
    managerId: manager.managerId,
    teamId: team.teamId,
    playerCount: players.length,
  });

  // Start transaction for this manager
  await em.transactional(async (transactionalEm: any) => {
    // Update services to use transactional entity manager
    const txArchivalService = new ManagerArchivalServiceImpl(transactionalEm);
    const txTeamResetService = new TeamResetServiceImpl(transactionalEm);

    // 1. Archive the manager
    const archivedManager = await txArchivalService.archiveManager(manager);

    // 2. Archive the team
    const archivedTeam = await txArchivalService.archiveTeam(team, archivedManager);

    // 3. Archive the players
    await txArchivalService.archivePlayers(players, archivedTeam);

    // 4. Reset the team for AI management and cleanup player data
    await txTeamResetService.completeTeamReset(team, manager, players);

    logger.info('Successfully archived and reset idle manager', {
      managerId: manager.managerId,
      teamId: team.teamId,
      archivedTeamId: archivedTeam.archivedTeamId,
      newTeamName: team.teamName,
    });
  });
}

export const handler = eventMiddify(main, { injectRepositories: true });
