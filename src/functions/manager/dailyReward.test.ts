import { TransactionType } from '@/storage-interface/teams/index.js';
import createHttpEvent from '@/testing/createHttpEvent.js';
import { ManagerFactory } from '@/testing/factories/managerFactory.js';
import { mockManagerRepository, mockTeamRepository } from '@/testing/mockRepositories.js';
import { buildResponse } from '@/utils/buildResponse.js';
import { subDays } from 'date-fns';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { handler } from './dailyReward.js';

describe('dailyReward handler', () => {
  const mockContext = {} as any;
  let event: any;
  let manager: any;

  beforeEach(() => {
    vi.clearAllMocks();
    manager = ManagerFactory.build({});
    event = createHttpEvent({});
    mockManagerRepository.getManagerById.mockResolvedValue(manager);
    mockManagerRepository.getEntityManager.mockReturnValue({
      assign: vi.fn(),
      flush: vi.fn(),
    });
    mockTeamRepository.updateTeamBalance.mockResolvedValue({});
  });

  it('returns 404 if manager is not found', async () => {
    mockManagerRepository.getManagerById.mockResolvedValue(null);
    const response = await handler(event, mockContext);
    expect(response).toEqual(buildResponse(404, JSON.stringify({ error: 'Manager not found' })));
  });

  it("rewards player for yesterday's login and updates streak", async () => {
    manager.lastActive = subDays(new Date(), 1).getTime();
    manager.loginStreak = 2;
    const response = await handler(event, mockContext);
    expect(response.statusCode).toBe(200);
    expect(mockManagerRepository.getEntityManager().assign).toHaveBeenCalledWith(
      manager,
      expect.objectContaining({
        lastActive: expect.any(BigInt),
        loginStreak: 3,
      })
    );
    expect(mockManagerRepository.getEntityManager().flush).toHaveBeenCalled();
  });

  it('returns rewards object with money rewards scaled by tier', async () => {
    manager.lastActive = subDays(new Date(), 1).getTime();
    manager.loginStreak = 6; // triggers day 7 (money reward)
    manager.team.tier = 2; // tier multiplier is 5

    const response = await handler(event, mockContext);
    expect(response.statusCode).toBe(200);

    const body = JSON.parse(response.body);
    expect(Array.isArray(body.rewards)).toBe(true);

    // Find the day 7 reward (index 6)
    const day7 = body.rewards[6];
    const moneyReward = day7.find((r: any) => r.type === 'money');
    expect(moneyReward.value).toBe(100000 * 5); // 500,000 for tier 2
  });

  it('returns 202 if login is on the same day', async () => {
    manager.lastActive = Date.now();
    const response = await handler(event, mockContext);
    expect(response).toEqual(buildResponse(202, JSON.stringify({})));
  });

  it('resets login streak if last login was more than a day ago', async () => {
    manager.lastActive = subDays(new Date(), 3).getTime();
    manager.loginStreak = 5;
    // Capture original values before handler mutates manager
    const originalScoutTokens = manager.scoutTokens;
    const originalMagicSponges = manager.magicSponges;
    const originalCardAppeals = manager.cardAppeals;
    const originalTrainingBoosts = manager.trainingBoosts;

    const response = await handler(event, mockContext);
    expect(response.statusCode).toBe(200);
    expect(mockManagerRepository.getEntityManager().assign).toHaveBeenCalledWith(
      manager,
      expect.objectContaining({
        lastActive: expect.any(BigInt),
        loginStreak: 1,
        scoutTokens: originalScoutTokens + 1,
        magicSponges: originalMagicSponges,
        cardAppeals: originalCardAppeals,
        trainingBoosts: originalTrainingBoosts,
      })
    );
    expect(mockManagerRepository.getEntityManager().flush).toHaveBeenCalled();
  });

  describe('applies tier multiplier to money rewards', () => {
    it('tier 1', async () => {
      manager.lastActive = subDays(new Date(), 1).getTime();
      manager.loginStreak = 1;
      manager.team.tier = 1;
      const response = await handler(event, mockContext);
      expect(response.statusCode).toBe(200);
      expect(mockTeamRepository.updateTeamBalance).toHaveBeenCalledWith(
        manager.team.teamId,
        manager.gameworldId,
        100000,
        TransactionType.DAILY_REWARD
      );
    });
    it('tier 2', async () => {
      manager.lastActive = subDays(new Date(), 1).getTime();
      manager.loginStreak = 1;
      manager.team.tier = 2;
      const response = await handler(event, mockContext);
      expect(response.statusCode).toBe(200);
      expect(mockTeamRepository.updateTeamBalance).toHaveBeenCalledWith(
        manager.team.teamId,
        manager.gameworldId,
        50000,
        TransactionType.DAILY_REWARD
      );
    });
    it('tier 3', async () => {
      manager.lastActive = subDays(new Date(), 1).getTime();
      manager.loginStreak = 1;
      manager.team.tier = 3;
      const response = await handler(event, mockContext);
      expect(response.statusCode).toBe(200);
      expect(mockTeamRepository.updateTeamBalance).toHaveBeenCalledWith(
        manager.team.teamId,
        manager.gameworldId,
        30000,
        TransactionType.DAILY_REWARD
      );
    });
    it('tier 4', async () => {
      manager.lastActive = subDays(new Date(), 1).getTime();
      manager.loginStreak = 1;
      manager.team.tier = 4;
      const response = await handler(event, mockContext);
      expect(response.statusCode).toBe(200);
      expect(mockTeamRepository.updateTeamBalance).toHaveBeenCalledWith(
        manager.team.teamId,
        manager.gameworldId,
        10000,
        TransactionType.DAILY_REWARD
      );
    });
  });

  describe('rewards for each day', () => {
    const expectedRewards = [
      // day: [money, scoutTokens, magicSponges, cardAppeals, trainingBoosts]
      { day: 1, money: 0, scoutTokens: 1, magicSponges: 0, cardAppeals: 0, trainingBoosts: 0 },
      { day: 2, money: 10000, scoutTokens: 1, magicSponges: 0, cardAppeals: 0, trainingBoosts: 0 },
      { day: 3, money: 0, scoutTokens: 1, magicSponges: 1, cardAppeals: 0, trainingBoosts: 0 },
      { day: 4, money: 0, scoutTokens: 1, magicSponges: 0, cardAppeals: 1, trainingBoosts: 0 },
      { day: 5, money: 0, scoutTokens: 1, magicSponges: 1, cardAppeals: 0, trainingBoosts: 0 },
      { day: 6, money: 0, scoutTokens: 1, magicSponges: 0, cardAppeals: 0, trainingBoosts: 1 },
      { day: 7, money: 100000, scoutTokens: 1, magicSponges: 0, cardAppeals: 0, trainingBoosts: 0 },
      // day 8 wraps to day 1
      { day: 8, money: 0, scoutTokens: 1, magicSponges: 0, cardAppeals: 0, trainingBoosts: 0 },
    ];

    expectedRewards.forEach(
      ({ day, money, scoutTokens, magicSponges, cardAppeals, trainingBoosts }, idx) => {
        it(`gives correct rewards for day ${day}`, async () => {
          manager.lastActive = subDays(new Date(), 1).getTime();
          manager.loginStreak = idx; // day = loginStreak + 1
          manager.scoutTokens = 0;
          manager.magicSponges = 0;
          manager.cardAppeals = 0;
          manager.trainingBoosts = 0;
          manager.team.tier = 4;

          await handler(event, mockContext);

          if (money > 0) {
            expect(mockTeamRepository.updateTeamBalance).toHaveBeenCalledWith(
              manager.team.teamId,
              manager.gameworldId,
              money,
              TransactionType.DAILY_REWARD
            );
          } else {
            expect(mockTeamRepository.updateTeamBalance).not.toHaveBeenCalled();
          }
          expect(manager.scoutTokens).toBe(scoutTokens);
          expect(manager.magicSponges).toBe(magicSponges);
          expect(manager.cardAppeals).toBe(cardAppeals);
          expect(manager.trainingBoosts).toBe(trainingBoosts);
        });
      }
    );
  });
});
