import { Manager } from '@/entities/Manager.js';
import { Repositories } from '@/middleware/database/types.js';
import { sqsMiddify } from '@/middleware/sqs/index.js';
import { SQSEvent } from '@/middleware/sqs/types.js';
import { TransactionService } from '@/services/database/transaction-service.js';
import { getMikroOrmService } from '@/storage-interface/database-initializer.js';
import { NewManagerEvent } from '@/types/generated/new-manager-event.js';
import { logger } from '@/utils/logger.js';

export async function createAndAssignNewManager(
  managerId: string,
  repositories: Repositories,
  transactionService: TransactionService,
  email?: string
) {
  const existingManager = await repositories.managerRepository.getManagerById(managerId);
  if (existingManager) {
    logger.error('Manager already exists', { existingManager });
    return;
  }
  try {
    await transactionService.executeInTransaction(async (txEm) => {
      // Get a team from the available teams table
      logger.debug('Getting team');
      const team = await repositories.teamRepository.getAndDeleteAvailableTeam(txEm);
      if (!team) {
        throw new Error('No available teams');
      }
      logger.debug('Got team', { team });
      const teamId = team.teamId;
      const gameworldId = team.gameworldId;

      // Create a new manager
      const manager = new Manager();
      manager.managerId = managerId;
      manager.team = repositories.teamRepository.createFromPK(teamId);
      manager.email = email;
      manager.gameworldId = gameworldId;
      manager.scoutTokens = 3;
      manager.superScoutTokens = 0;
      manager.createdAt = BigInt(new Date().getTime());
      manager.lastActive = BigInt(new Date().getTime());

      // Save the manager
      logger.debug('Saving manager', { manager });
      await repositories.managerRepository.createManager(manager, txEm);

      // Make sure the team is correctly initialised
      await repositories.teamRepository.reinitialiseTeam(
        teamId,
        gameworldId,
        repositories.playerRepository,
        repositories.transferRepository,
        txEm
      );

      logger.debug('Manager saved');
    });
  } catch (error) {
    logger.error('Error saving user:', { error });
    throw error;
  }
}

export async function main(event: SQSEvent<NewManagerEvent>) {
  const mikroOrmService = await getMikroOrmService();
  const transactionService = new TransactionService(mikroOrmService);
  const batchItemFailures: { itemIdentifier: string }[] = [];
  for (const record of event.Records) {
    try {
      const { managerId, email } = record.body;
      if (!managerId) {
        throw new Error('Manager ID is required');
      }
      // Process each manager creation
      await createAndAssignNewManager(
        managerId,
        event.context.repositories,
        transactionService,
        email
      );
    } catch (error) {
      logger.error('Failed to create manager', { error, body: record.body });
      batchItemFailures.push({ itemIdentifier: record.messageId });
    }
  }
  return { batchItemFailures };
}

export const handler = sqsMiddify<NewManagerEvent>(main);
