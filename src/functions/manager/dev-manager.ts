import { AvailableTeam } from '@/entities/AvailableTeam.js';
import { Manager } from '@/entities/Manager.js';
import { httpMiddify } from '@/middleware/rest/index.js';
import { HttpEvent } from '@/middleware/rest/types.js';
import { TeamRepository } from '@/storage-interface/teams/index.js';
import { buildResponse } from '@/utils/buildResponse.js';
import { logger } from '@/utils/logger.js';
import { v4 as uuidv4 } from 'uuid';

type DevManagerEvent = HttpEvent<void, void, void>;

export async function getAndDelete(teamRepository: TeamRepository): Promise<AvailableTeam | null> {
  const team = await teamRepository.getRandomAvailableTeam();
  if (!team) {
    return null;
  }

  await teamRepository.deleteAvailableTeam(team);
  return team;
}

export const main = async function (event: DevManagerEvent) {
  const { teamRepository, managerRepository } = event.context.repositories;
  if (process.env.STAGE !== 'dev') {
    logger.error('DevManager should only be called in dev stage');
    return Promise.resolve(
      buildResponse(
        500,
        JSON.stringify({
          message: 'DevManager should only be called in dev stage',
        })
      )
    );
  }

  try {
    // get a team from the available teams table
    const team = await getAndDelete(teamRepository);
    if (!team) {
      throw new Error('No available teams');
    }

    const manager = new Manager();
    manager.managerId = process.env.DEBUG_USER_ID || uuidv4();
    manager.createdAt = BigInt(new Date().getTime());
    manager.lastActive = BigInt(new Date().getTime());
    manager.team = teamRepository.createFromPK(team.teamId);
    manager.gameworldId = team.gameworldId;
    manager.scoutTokens = 3;
    manager.superScoutTokens = 0;

    await managerRepository.createManager(manager);

    return buildResponse(200, JSON.stringify({ message: 'Manager created' }));
  } catch (error) {
    logger.error('Error saving user:', { error });
    throw error;
  }
};

export const handler = httpMiddify(main, {});
