function randomItem<T>(array: T[]): T {
  return array[Math.floor(Math.random() * array.length)]!;
}

export function generateBritishPubName(): string {
  const adjectives = [
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON><PERSON>',
    '<PERSON>',
    "<PERSON>'s",
    "<PERSON>'s",
    'Drunken',
    '<PERSON><PERSON><PERSON>',
    'Wobbly',
    '<PERSON>eeky',
    'Rowdy',
    '<PERSON>',
    '<PERSON><PERSON><PERSON>',
    '<PERSON>rumpy',
    '<PERSON>',
    '<PERSON>y',
  ];

  const animals = [
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    'Hound',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>ger',
    'Otter',
    'Weasel',
    '<PERSON><PERSON><PERSON>',
    '<PERSON>',
    'Pigeon',
    'Hedgehog',
    'Toad',
    'Mole',
    'Squirrel',
  ];

  const objects = [
    'Anchor',
    'Crown',
    '<PERSON>',
    'Wheel',
    'Harp',
    '<PERSON>',
    'Key',
    'Ship',
    'Bridge',
    'Oak',
    'Keg',
    '<PERSON><PERSON>',
    'Mug',
    'Tankard',
    'Pint',
    'Lager',
    'Stout',
    'Ale',
    'Hop',
    'Froth',
  ];

  const suffixes = [
    'Arms',
    'Inn',
    'Tavern',
    'Alehouse',
    'House',
    'Pub',
    'Lodge',
    'Hall',
    'Retreat',
    'Haven',
  ];

  const football_suffixes = [
    'Legends',
    'Boot Room',
    'Eleven',
    'United',
    'Rovers',
    'Athletic',
    'Wanderers',
    'FC',
    '',
  ];
  const formats: Array<() => string> = [
    () => `${randomItem(adjectives)} ${randomItem(animals)} ${randomItem(football_suffixes)}`,
    () => `The ${randomItem(animals)} and ${randomItem(objects)} ${randomItem(football_suffixes)}`,
    () => `The ${randomItem(objects)} ${randomItem(football_suffixes)}`,
    () => `The ${randomItem(adjectives)} ${randomItem(animals)} ${randomItem(football_suffixes)}`,
    () => `The ${randomItem(animals)} ${randomItem(suffixes)} ${randomItem(football_suffixes)}`,
    () => `The ${randomItem(adjectives)} ${randomItem(objects)} ${randomItem(football_suffixes)}`,
    () => `${randomItem(adjectives)} ${randomItem(animals)} ${randomItem(football_suffixes)}`,
  ];

  return randomItem(formats)().trim();
}

function generateFictionalTownName(): string {
  const prefixes = [
    'North',
    'South',
    'West',
    'East',
    'Upper',
    'Lower',
    'New',
    'Great',
    'Little',
    'Middle',
  ];

  const roots = [
    'Bran',
    'Wex',
    'Tud',
    'Dray',
    'Fenn',
    'Hals',
    'Dun',
    'Lud',
    'Hox',
    'Cram',
    'Marl',
    'Duns',
    'Nor',
    'Bex',
    'Elms',
    'Rud',
    'Toll',
    'Hind',
    'Cros',
    'Stow',
    'Ash',
    'Bar',
    'Chester',
    'Dale',
    'Eld',
    'Fen',
    'Grim',
    'Holt',
    'Ingle',
    'Jor',
    'Kel',
    'Lox',
    'Mar',
    'Neth',
    'Orms',
    'Pains',
    'Quen',
    'Rother',
    'Sax',
    'Thorn',
    'Uff',
    'Vale',
    'Wim',
    'Yar',
    'Zan',
  ];

  const suffixes = [
    'ford',
    'ley',
    'worth',
    'chester',
    'mere',
    'wick',
    'borough',
    'gate',
    'wich',
    'ham',
    'minster',
    'stead',
    'stoke',
    'field',
    'well',
    'shaw',
    'port',
    'pool',
    'thorpe',
    'holt',
    'bury',
    'bridge',
    'combe',
    'dene',
    'fell',
    'heath',
    'ing',
    'low',
    'mead',
    'stone',
  ];

  const formats = [
    () => `${randomItem(roots)}${randomItem(suffixes)}`,
    () => `${randomItem(prefixes)} ${randomItem(roots)}${randomItem(suffixes)}`,
  ];

  return randomItem(formats)();
}

export function generateFictionalFootballTeam(): string {
  const prefixes = [
    'North',
    'South',
    'West',
    'East',
    'Upper',
    'Lower',
    'New',
    'Great',
    'Little',
    'Middle',
  ];

  const suffixes = [
    'United',
    'Rovers',
    'Town',
    'City',
    'Athletic',
    'Wanderers',
    'Albion',
    'County',
    'Vale',
    'Harriers',
  ];

  const formats = [
    () => `${generateFictionalTownName()} ${randomItem(suffixes)}`,
    () => `${randomItem(prefixes)} ${generateFictionalTownName()} ${randomItem(suffixes)}`,
    () => `${generateFictionalTownName()} FC`,
    () => `${generateFictionalTownName()} & District ${randomItem(suffixes)}`,
  ];

  return randomItem(formats)();
}

export function generateRandomTeamName(): string {
  return Math.random() > 0.5 ? generateBritishPubName() : generateFictionalFootballTeam();
}
