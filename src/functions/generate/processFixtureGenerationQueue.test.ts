import { handler } from '@/functions/generate/processFixtureGenerationQueue.js';
import createSqsEvent from '@/testing/createSqsEvent.js';
import type { SQSBatchResponse } from 'aws-lambda';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';

// Mock services used by the handler
const mockTransactionService = {
  executeInTransaction: vi.fn(),
};
const mockFixtureGenerationService = {
  prepareFixtureGenerationData: vi.fn(),
  validateFixtureData: vi.fn(),
  getFixtureStatistics: vi.fn(() => ({ stats: 'mockStats' })),
};
const mockDatabaseService = {
  executeFixtureGeneration: vi.fn(),
  recordSuccessMetrics: vi.fn(),
  recordFailureMetrics: vi.fn(),
  deleteCompletedFixtureDetails: vi.fn(),
};

vi.mock('@/services/database/transaction-service.js', () => ({
  TransactionService: vi.fn(() => mockTransactionService),
}));
vi.mock('@/services/fixtures/fixture-generation-service.js', () => ({
  FixtureGenerationService: vi.fn(() => mockFixtureGenerationService),
}));
vi.mock('@/services/fixtures/fixture-generation-database-service.js', () => ({
  FixtureGenerationDatabaseService: vi.fn(() => mockDatabaseService),
}));

const defaultBody = {
  gameworldId: 'testGameworldId',
  leagueId: 'testLeagueId',
};

describe('processFixtureGenerationQueue handler', () => {
  let defaultEvent: any;
  const mockContext = {} as any;

  beforeEach(() => {
    defaultEvent = createSqsEvent([{ body: defaultBody }]);
    mockTransactionService.executeInTransaction.mockImplementation(async (fn: any) => {
      await fn({});
    });
    mockFixtureGenerationService.prepareFixtureGenerationData.mockResolvedValue({ fixtures: [] });
    mockFixtureGenerationService.validateFixtureData.mockReturnValue(undefined);
    mockDatabaseService.executeFixtureGeneration.mockResolvedValue(undefined);
    mockDatabaseService.recordSuccessMetrics.mockResolvedValue(undefined);
    mockDatabaseService.deleteCompletedFixtureDetails.mockResolvedValue(undefined);
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  it('should process a valid fixture generation request', async () => {
    const result = (await handler(defaultEvent, mockContext)) as SQSBatchResponse;
    expect(mockTransactionService.executeInTransaction).toHaveBeenCalled();
    expect(mockDatabaseService.executeFixtureGeneration).toHaveBeenCalled();
    expect(mockDatabaseService.recordSuccessMetrics).toHaveBeenCalled();
    expect(mockDatabaseService.deleteCompletedFixtureDetails).toHaveBeenCalled();
    expect(result.batchItemFailures).toEqual([]);
  });

  it('should handle errors during fixture generation', async () => {
    mockDatabaseService.executeFixtureGeneration.mockRejectedValue(new Error('DB error'));
    const result = (await handler(defaultEvent, mockContext)) as SQSBatchResponse;
    expect(result.batchItemFailures).toHaveLength(1);
  });

  it('should handle errors during deleteCompletedFixtureDetails gracefully', async () => {
    mockDatabaseService.deleteCompletedFixtureDetails.mockRejectedValue(new Error('Delete error'));
    const result = (await handler(defaultEvent, mockContext)) as SQSBatchResponse;
    expect(result.batchItemFailures).toEqual([]);
    // Should not throw, just log a warning
  });
});
