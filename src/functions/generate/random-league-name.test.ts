import { describe, expect, test } from 'vitest';
import { generateRandomLeagueName } from './random-league-name.js';

describe('generateRandomLeagueName', () => {
  describe('Child league naming', () => {
    test('should use Division A-I naming for first 9 child leagues', () => {
      const parentName = 'Premier Championship';

      // Test Division A through I
      for (let i = 0; i < 9; i++) {
        const expectedDivision = String.fromCharCode(65 + i); // 65 = 'A'
        const result = generateRandomLeagueName(2, parentName, i);
        expect(result).toBe(`${parentName} Division ${expectedDivision}`);
      }
    });

    test('should use geographic naming for child leagues beyond 9', () => {
      const parentName = 'Premier Championship';
      const result1 = generateRandomLeagueName(2, parentName, 9);
      const result2 = generateRandomLeagueName(2, parentName, 10);

      // We can't predict exactly which geographic name will be chosen due to randomness,
      // but we can verify it doesn't use the Division A-I format
      expect(result1).toContain(parentName);
      expect(result1).not.toContain('Division');
      expect(result2).toContain(parentName);
      expect(result2).not.toContain('Division');
    });
  });

  describe('Tier-based naming', () => {
    test('should return a string for tier 1', () => {
      const result = generateRandomLeagueName(1);
      expect(typeof result).toBe('string');
      expect(result.length).toBeGreaterThan(0);
    });

    test('should return a string for tier 2', () => {
      const result = generateRandomLeagueName(2);
      expect(typeof result).toBe('string');
      expect(result.length).toBeGreaterThan(0);
    });

    test('should return a string for tier 3', () => {
      const result = generateRandomLeagueName(3);
      expect(typeof result).toBe('string');
      expect(result.length).toBeGreaterThan(0);
    });

    test('should return a string for tier 4', () => {
      const result = generateRandomLeagueName(4);
      expect(typeof result).toBe('string');
      expect(result.length).toBeGreaterThan(0);
    });

    test('should handle tiers beyond 4', () => {
      const result = generateRandomLeagueName(5);
      expect(typeof result).toBe('string');
      expect(result.length).toBeGreaterThan(0);
    });
  });

  describe('Function behavior', () => {
    test('should return different names on multiple calls for the same tier', () => {
      // This test might occasionally fail due to random chance, but it's unlikely
      const results = new Set();
      for (let i = 0; i < 10; i++) {
        results.add(generateRandomLeagueName(4));
      }
      // We should get at least a few different names
      expect(results.size).toBeGreaterThan(1);
    });
  });
});
