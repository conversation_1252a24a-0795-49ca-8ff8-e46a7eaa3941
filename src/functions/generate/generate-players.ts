import { Player } from '@/entities/Player.js';
import { generatePlayerWithinRanges } from '@/functions/generate/player.js';
import { sqsMiddify } from '@/middleware/sqs/index.js';
import { SQSEvent } from '@/middleware/sqs/types.js';
import { NotificationManager } from '@/services/notifications/NotificationManager.js';
import { GeneratePlayersEvent } from '@/types/generated/generate-players-event.js';
import { logger } from '@/utils/logger.js';

const notificationManager = NotificationManager.getInstance();

/**
 * Generate players in response to an SQS event. Currently only used for youth players.
 *
 * @param event
 */
export async function main(event: SQSEvent<GeneratePlayersEvent>) {
  const { playerRepository, teamRepository } = event.context.repositories;

  const allPlayers: Player[] = [];
  const batchItemFailures: { itemIdentifier: string }[] = [];
  for (const record of event.Records) {
    const teamPlayers: Player[] = [];
    const createEvent = record.body;
    try {
      logger.debug(
        `Generating ${createEvent.requiredPlayers} players for team ${createEvent.teamId}`
      );
      for (let i = 0; i < createEvent.requiredPlayers; i++) {
        const player = generatePlayerWithinRanges(createEvent, teamRepository);
        allPlayers.push(player);
        teamPlayers.push(player);
      }
      if (createEvent.managerId) {
        await notificationManager.loadManagerPreferences(
          createEvent.managerId,
          event.context.repositories
        );
        await notificationManager.sendYouthPlayerNotification(teamPlayers);
      }
    } catch (err) {
      logger.error(`Failed to process record ${record.messageId}:`, { error: err });
      batchItemFailures.push({ itemIdentifier: record.messageId });
    }
  }

  if (allPlayers.length > 0) {
    await playerRepository.batchCreatePlayers(allPlayers);
  }
  return { batchItemFailures };
}

export const handler = sqsMiddify<GeneratePlayersEvent>(main, {});
