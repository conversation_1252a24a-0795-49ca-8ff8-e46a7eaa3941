import { Player } from '@/entities/Player.js';
import {
  ATTRIBUTE_MAX,
  ATTRIBUTE_MIN,
  calculatePlayerValue,
  generatePlayerAttributes,
  getPlayerTemplates,
} from '@/functions/generate/player-attributes.js';
import { sqsMiddify } from '@/middleware/sqs/index.js';
import { SQSEvent } from '@/middleware/sqs/types.js';
import { TeamRepository } from '@/storage-interface/teams/team-repository.interface.js';
import { BaseAttributeName } from '@/types/attribute-utils.js';
import { GeneratePlayersEvent } from '@/types/generated/generate-players-event.js';
import { TeamCreatedEvent } from '@/types/generated/index.js';
import { logger } from '@/utils/logger.js';
import {
  seededRandom,
  seededRandomIntInRange,
  setAndReturnSeededRandom,
} from '@/utils/seeded-random.js';
import { v4 as uuidv4 } from 'uuid';
import firstNames from './gb-forenames.json' with { type: 'json' };
import surnames from './gb-surnames.json' with { type: 'json' };

export function generatePlayerWithinRanges(
  event: GeneratePlayersEvent,
  teamRepository: TeamRepository
): Player {
  const player = new Player();
  player.playerId = uuidv4();
  player.gameworldId = event.gameworldId;
  player.team = teamRepository.createFromPK(event.teamId);
  player.seed = BigInt(setAndReturnSeededRandom());
  player.age = seededRandomIntInRange(event.minAge ?? 17, event.maxAge ?? 38);
  player.firstName = firstNames[Math.floor(seededRandom() * firstNames.length)]!.name;
  player.surname = surnames[Math.floor(seededRandom() * surnames.length)]!.name;
  player.attributes = generatePlayerAttributes(
    seededRandomIntInRange(event.minSkill ?? 1, event.maxSkill ?? 40),
    event.minPotential ?? 1,
    event.maxPotential ?? 40,
    []
  );
  player.value = Number(calculatePlayerValue(player).toFixed(2));
  player.energy = 100;
  player.lastMatchPlayed = 0n;
  player.suspendedForGames = 0;
  return player;
}

export function generatePlayer(
  teamRepository: TeamRepository,
  gameworldId: string,
  teamId?: string,
  templateSkills?: BaseAttributeName[],
  baseSkill?: number,
  isGoalkeeper: boolean = false,
  seed?: number
): Player {
  // random number between 1 and 10
  const randomSkill = Math.floor(seededRandom() * 10) + 1;
  // three random skills
  let randomSkills: BaseAttributeName[] = [];
  if (!templateSkills) {
    randomSkills = [
      'reflexes',
      'positioning',
      'shotStopping',
      'tackling',
      'marking',
      'heading',
      'finishing',
      'pace',
      'crossing',
      'passing',
      'vision',
      'ballControl',
    ]
      .sort(() => seededRandom() - 0.5)
      .slice(0, 3) as BaseAttributeName[];
  }
  // random age between 17 and 39
  const randomAge = Math.floor(seededRandom() * 23) + 17;

  const player = new Player();
  player.gameworldId = gameworldId;
  player.team = teamId ? teamRepository.createFromPK(teamId) : undefined;
  player.playerId = uuidv4();
  player.firstName = firstNames[Math.floor(seededRandom() * firstNames.length)]!.name;
  player.surname = surnames[Math.floor(seededRandom() * surnames.length)]!.name;
  // Ensure we have a non-undefined array of skills
  const skills: BaseAttributeName[] = templateSkills || randomSkills;

  player.attributes = generatePlayerAttributes(
    baseSkill ?? randomSkill,
    ATTRIBUTE_MIN,
    ATTRIBUTE_MAX,
    skills
  );
  player.attributes.isGoalkeeper = isGoalkeeper;
  player.age = randomAge;
  player.seed = BigInt(seed!);
  player.value = Number(calculatePlayerValue(player).toFixed(2));
  player.energy = 100;
  player.lastMatchPlayed = 0n;
  player.suspendedForGames = 0;
  return player;
}

export interface PlayerTemplate {
  skills: BaseAttributeName[];
  baseSkill: number;
  isGoalkeeper?: boolean;
}

function generatePlayers(teamRepository: TeamRepository, team: TeamCreatedEvent) {
  const players: Player[] = [];
  const playerTemplates = getPlayerTemplates(team.tier);

  // save the seed so we can generate the same players again for testing
  const seed = setAndReturnSeededRandom();

  for (let i = 0; i < team.requiredPlayers; i++) {
    if (playerTemplates.length > i) {
      const template = playerTemplates[i]!;
      players.push(
        generatePlayer(
          teamRepository,
          team.gameworldId,
          team.teamId,
          template.skills,
          template.baseSkill,
          template.isGoalkeeper || false,
          seed
        )
      );
      continue;
    }
    players.push(
      generatePlayer(
        teamRepository,
        team.gameworldId,
        team.teamId,
        undefined,
        undefined,
        false,
        seed
      )
    );
  }

  return players;
}

/**
 * Generate players in response to an SQS event
 * @param event
 */
export async function main(event: SQSEvent<TeamCreatedEvent>) {
  const allPlayers = [];

  const { playerRepository, teamRepository } = event.context.repositories;

  const batchItemFailures = [];
  for (const record of event.Records) {
    try {
      const team = record.body;
      logger.debug(`Generating ${team.requiredPlayers} players for team ${team.teamId}`);
      allPlayers.push(...generatePlayers(teamRepository, team));
    } catch (error) {
      logger.error('Failed to generate players', { error, body: record.body });
      batchItemFailures.push({ itemIdentifier: record.messageId });
    }
  }

  await playerRepository.batchCreatePlayers(allPlayers);
  return { batchItemFailures };
}

export const handler = sqsMiddify<TeamCreatedEvent>(main, {});
