import { TransferListedPlayer } from '@/entities/TransferListedPlayer.ts';
import { handler } from '@/functions/generate/unattached-players.js';
import createSqsEvent from '@/testing/createSqsEvent.js';
import { mockPlayerRepository } from '@/testing/mockRepositories.ts';
import { GenerateUnattachedPlayersEvent } from '@/types/generated/index.js';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';

describe('Generate Unattached Players', () => {
  let defaultEvent: any;
  const context = {} as any;

  beforeEach(() => {
    process.env.TRANSFER_LISTED_PLAYERS_TABLE_NAME = 'transferListedPlayersTable';

    const testBody: GenerateUnattachedPlayersEvent = {
      gameworldId: 'testGameworldId',
      requiredPlayers: 75,
    };
    defaultEvent = createSqsEvent([{ body: testBody }]);

    mockPlayerRepository.batchCreateTransferListedPlayers.mockResolvedValue(undefined);

    // Use fake timers and set the system time
    vi.useFakeTimers();
    vi.setSystemTime(new Date(1740178572294));
  });

  afterEach(() => {
    vi.clearAllMocks();
    vi.useRealTimers();
  });

  it('should generate the correct number of players (75 per league)', async () => {
    await handler(defaultEvent, context);

    expect(mockPlayerRepository.batchCreateTransferListedPlayers).toHaveBeenCalledTimes(1);
    expect(mockPlayerRepository.batchCreateTransferListedPlayers).toHaveBeenCalledWith(
      expect.any(Array)
    );
    const callArgs = mockPlayerRepository.batchCreateTransferListedPlayers.mock.calls[0]![0];
    expect(callArgs).toHaveLength(75);
  });

  it('should handle multiple league tables in the event', async () => {
    const multipleEvent = createSqsEvent([
      {
        body: {
          gameworldId: 'world1',
          requiredPlayers: 75,
        },
      },
      {
        body: {
          gameworldId: 'world2',
          requiredPlayers: 75,
        },
      },
    ]) as any;

    // Parse the JSON bodies since we're mocking the middleware that would normally do this
    multipleEvent.Records[0].body = {
      gameworldId: 'world1',
      requiredPlayers: 75,
    };
    multipleEvent.Records[1].body = {
      gameworldId: 'world2',
      requiredPlayers: 75,
    };

    // Add repository context
    multipleEvent.context = {
      repositories: {
        playerRepository: mockPlayerRepository,
      },
    };

    await handler(multipleEvent, context);

    expect(mockPlayerRepository.batchCreateTransferListedPlayers).toHaveBeenCalledTimes(1);
    const players = mockPlayerRepository.batchCreateTransferListedPlayers.mock
      .calls[0]![0] as TransferListedPlayer[];
    expect(players).toHaveLength(150); // 75 players * 2 leagues

    const world1Players = players.filter((p) => p.gameworldId === 'world1');
    const world2Players = players.filter((p) => p.gameworldId === 'world2');

    expect(world1Players).toHaveLength(75);
    expect(world2Players).toHaveLength(75);
  });

  it('should handle database insertion failures', async () => {
    // Make the repository mock throw an error
    mockPlayerRepository.batchCreateTransferListedPlayers.mockRejectedValue(
      new Error('Database error')
    );

    // The function should propagate the error (middleware would handle logging)
    await expect(handler(defaultEvent, context)).rejects.toThrow('Database error');
  });
});
