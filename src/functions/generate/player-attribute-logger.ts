import { Player } from '../../entities/Player.js';
import { PlayerAttributes } from '../../entities/PlayerAttributes.js';

/**
 * Logs player attributes and value in a readable format for test debugging.
 * @param player The player entity
 * @param value The calculated player value
 */
export function logPlayerAttributesAndValue(player: Player, value: number) {
  const attrs: PlayerAttributes = player.attributes as PlayerAttributes;
  const attributeRows: [string, number, number][] = [
    ['reflexes', attrs.reflexesCurrent, attrs.reflexesPotential],
    ['positioning', attrs.positioningCurrent, attrs.positioningPotential],
    ['shotStopping', attrs.shotStoppingCurrent, attrs.shotStoppingPotential],
    ['tackling', attrs.tacklingCurrent, attrs.tacklingPotential],
    ['marking', attrs.markingCurrent, attrs.markingPotential],
    ['heading', attrs.headingCurrent, attrs.headingPotential],
    ['finishing', attrs.finishingCurrent, attrs.finishingPotential],
    ['pace', attrs.paceCurrent, attrs.pacePotential],
    ['crossing', attrs.crossingCurrent, attrs.crossingPotential],
    ['passing', attrs.passingCurrent, attrs.passingPotential],
    ['vision', attrs.visionCurrent, attrs.visionPotential],
    ['ballControl', attrs.ballControlCurrent, attrs.ballControlPotential],
  ];
  const valueFormatted = `£${value.toLocaleString('en-GB', { maximumFractionDigits: 0 })}`;
  console.log(`Player: ${player.firstName} ${player.surname} (ID: ${player.playerId})`);
  console.log(`Is Goalkeeper: ${attrs.isGoalkeeper}`);
  console.log('Attributes:');
  console.log('---------------------------------------------------');
  console.log('| Attribute     | Current   | Potential |');
  console.log('---------------------------------------------------');
  for (const [attr, current, potential] of attributeRows) {
    console.log(
      `| ${String(attr).padEnd(13)} | ${Number(current).toFixed(2).padEnd(8)} | ${Number(potential).toFixed(2).padEnd(8)} |`
    );
  }
  console.log('---------------------------------------------------');
  console.log(`Stamina: ${attrs.stamina.toFixed(2)}`);
  console.log(`Value: ${valueFormatted}`);
}
