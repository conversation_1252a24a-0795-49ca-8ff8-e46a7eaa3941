import { handler } from '@/functions/generate/player.js';
import DynamoDbService from '@/services/database/dynamo/dynamo-db-service.js';
import createSqsEvent from '@/testing/createSqsEvent.js';
import { mockPlayerRepository } from '@/testing/mockRepositories.js';
import { TeamCreatedEvent } from '@/types/generated/index.js';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';

// Mock seeded random to ensure consistent behavior
vi.mock('@/utils/seeded-random.js', () => ({
  setAndReturnSeededRandom: vi.fn(() => 12345),
  seededRandom: vi.fn(() => 0.5),
  seededRandomIntInRange: vi.fn((min, max) => Math.floor((max - min + 1) * 0.5) + min),
}));

describe('Generate Player', () => {
  let defaultEvent: any;
  const context = {} as any; // Mock context object
  const insertSpy = vi.spyOn(DynamoDbService.prototype, 'batchInsert');

  beforeEach(() => {
    process.env.PLAYERS_TABLE_NAME = 'playersTable';

    const testBody: TeamCreatedEvent = {
      leagueId: 'testLeagueId',
      requiredPlayers: 15,
      teamId: 'testTeamId',
      gameworldId: 'testGameworldId',
      tier: 1,
    };
    defaultEvent = createSqsEvent([{ body: testBody }]);

    mockPlayerRepository.batchCreatePlayers.mockResolvedValue(undefined);

    insertSpy.mockResolvedValue({ successful: [], failed: [] });

    // Use fake timers and set the system time
    vi.useFakeTimers();
    vi.setSystemTime(new Date(1740178572294));
  });

  afterEach(() => {
    vi.clearAllMocks();
    vi.useRealTimers(); // Restore real timers
  });

  it('should generate the correct number of players', async () => {
    await handler(defaultEvent, context);

    // Verify that batchCreatePlayers was called with the correct number of items
    expect(mockPlayerRepository.batchCreatePlayers).toHaveBeenCalledTimes(1);
    expect(mockPlayerRepository.batchCreatePlayers).toHaveBeenCalledWith(expect.any(Array));
    const callArgs = mockPlayerRepository.batchCreatePlayers.mock.calls[0]![0]; // Gets the first argument of the first call
    expect(callArgs).toHaveLength(15);
  });

  it('should be used to debug player attributes', async () => {
    await handler(defaultEvent, context);

    // Add an assertion to avoid ESLint error
    expect(Date.now()).toBe(1740178572294);
  });
});
