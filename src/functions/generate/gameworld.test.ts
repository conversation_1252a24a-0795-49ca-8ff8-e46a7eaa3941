import { League } from '@/entities/League.js';
import { SQS } from '@/services/sqs/sqs.js';
import { mockLeagueRepository, resetAllRepositoryMocks } from '@/testing/mockRepositories.js';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { handler } from './gameworld.js';

// Mock MikroORM entities with proper hoisting
const mockLeagueRules = vi.hoisted(() => {
  return vi.fn().mockImplementation((rules) => {
    const instance = {
      teamCount: 15,
      promotionSpots: 0,
      relegationSpots: 0,
      league: null,
      maximumPrize: 0,
      minimumPrize: 0,
    };

    // If rules is provided, copy its properties (mimicking the real constructor)
    if (rules) {
      if (rules.promotionSpots !== undefined) instance.promotionSpots = rules.promotionSpots;
      if (rules.relegationSpots !== undefined) instance.relegationSpots = rules.relegationSpots;
      if (rules.teamCount !== undefined) instance.teamCount = rules.teamCount;
      if (rules.league !== undefined) instance.league = rules.league;
      if (rules.maximumPrize !== undefined) instance.maximumPrize = rules.maximumPrize;
      if (rules.minimumPrize !== undefined) instance.minimumPrize = rules.minimumPrize;
    }

    return instance;
  });
});

vi.mock('@/entities/LeagueRules.js', () => ({
  LeagueRules: mockLeagueRules,
}));

vi.mock('@/entities/League.js', () => {
  return {
    League: vi.fn().mockImplementation(() => {
      const mockLeague = {
        id: 'mock-league-id',
        gameworld: null,
        name: '',
        tier: 1,
        parentLeague: null,
        leagueRules: null,
        leagueChildren: {
          add: vi.fn(),
        },
      };

      // Allow leagueRules to be assigned
      Object.defineProperty(mockLeague, 'leagueRules', {
        writable: true,
        value: null,
      });

      return mockLeague;
    }),
  };
});

vi.mock('@/entities/Gameworld.js', () => {
  return {
    Gameworld: vi.fn().mockImplementation(() => {
      return {
        id: 'mock-gameworld-id',
        endDate: 0,
      };
    }),
  };
});

describe('Generate Gameworld', () => {
  const context = {} as any;
  const sendSpy = vi.fn().mockResolvedValue(undefined);

  process.env.UNATTACHED_PLAYERS_QUEUE_URL = 'test-unattached-queue-url';
  process.env.TEAM_QUEUE_URL = 'test-team-queue-url';

  beforeEach(() => {
    resetAllRepositoryMocks();

    // Mock SQS constructor and send method
    vi.spyOn(SQS.prototype, 'send').mockImplementation(sendSpy);
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  it.each([
    { tiers: 1, childLeagues: 3, expected: 1 }, // 1
    { tiers: 2, childLeagues: 3, expected: 4 }, // 1 + 3
    { tiers: 3, childLeagues: 3, expected: 13 }, // 1 + 3 + 9
    { tiers: 4, childLeagues: 3, expected: 40 }, // 1 + 3 + 9 + 27
    { tiers: 3, childLeagues: 2, expected: 7 }, // 1 + 2 + 4
    { tiers: 4, childLeagues: 2, expected: 15 }, // 1 + 2 + 4 + 8
  ])(
    'should create $expected leagues for $tiers tiers and $childLeagues child leagues',
    async ({ tiers, childLeagues, expected }) => {
      const event = {
        tiers,
        childLeagues,
        teamsPerLeague: 15,
        httpMethod: 'POST',
      } as any;

      await handler(event, context);

      expect(mockLeagueRepository.batchCreateLeagues).toHaveBeenCalledTimes(1);
      expect(mockLeagueRepository.batchCreateLeagues).toHaveBeenCalledWith(
        expect.arrayContaining([]) && expect.any(Array)
      );
      const callArgs = mockLeagueRepository.batchCreateLeagues.mock.calls[0]![0];
      expect(callArgs).toHaveLength(expected);
    }
  );

  it('should set correct promotion/relegation rules based on tier', async () => {
    const event = {
      tiers: 3,
      childLeagues: 2,
      teamsPerLeague: 15,
    } as any;

    await handler(event, context);

    const callArgs = mockLeagueRepository.batchCreateLeagues.mock.calls[0]![0];

    // Top tier (tier 1) should have no promotion
    const topTier = callArgs.find((league: League) => league.tier === 1);
    expect(topTier?.leagueRules.promotionSpots).toBe(0);
    expect(topTier?.leagueRules.relegationSpots).toBe(2);

    // Middle tier (tier 2) should have both promotion and relegation
    const middleTier = callArgs.find((league: League) => league.tier === 2);
    expect(middleTier?.leagueRules.promotionSpots).toBe(1);
    expect(middleTier?.leagueRules.relegationSpots).toBe(2);

    // Bottom tier (tier 3) should have no relegation
    const bottomTier = callArgs.find((league: League) => league.tier === 3);
    expect(bottomTier?.leagueRules.promotionSpots).toBe(1);
    expect(bottomTier?.leagueRules.relegationSpots).toBe(0);
  });

  it('should send events to SQS for unattached players and league creation', async () => {
    const event = {
      tiers: 2,
      childLeagues: 2,
      teamsPerLeague: 15,
    } as any;

    await handler(event, context);

    // Should create 3 leagues (1 parent + 2 children)
    expect(sendSpy).toHaveBeenCalledTimes(4); // 3 league events + 1 unattached players event

    // Verify unattached players event
    expect(sendSpy).toHaveBeenCalledWith(
      'test-unattached-queue-url',
      expect.stringContaining('requiredPlayers')
    );

    // Verify league creation events
    expect(sendSpy).toHaveBeenCalledWith(
      'test-team-queue-url',
      expect.stringContaining('requiredTeams')
    );
  });

  it('should set availableToManage correctly based on tier', async () => {
    const event = {
      tiers: 4,
      childLeagues: 1,
      teamsPerLeague: 15,
    } as any;

    await handler(event, context);

    // Verify that SQS messages were sent
    expect(sendSpy).toHaveBeenCalled();

    // We can't easily check the content of the messages since they're stringified
    // but we can verify the number of calls
    const sendCalls = sendSpy.mock.calls;
    expect(sendCalls.length).toBeGreaterThan(1);
  });

  it('should set correct prize money based on tier', async () => {
    const event = {
      tiers: 4,
      childLeagues: 2,
      teamsPerLeague: 15,
    } as any;

    await handler(event, context);

    const callArgs = mockLeagueRepository.batchCreateLeagues.mock.calls[0]![0];

    // Tier 1: £3,000,000 max, £1,500,000 min
    const tier1 = callArgs.find((league: League) => league.tier === 1);
    expect(tier1?.leagueRules.maximumPrize).toBe(3000000);
    expect(tier1?.leagueRules.minimumPrize).toBe(1500000);

    // Tier 2: £1,000,000 max, £400,000 min
    const tier2 = callArgs.find((league: League) => league.tier === 2);
    expect(tier2?.leagueRules.maximumPrize).toBe(1000000);
    expect(tier2?.leagueRules.minimumPrize).toBe(400000);

    // Tier 3: £300,000 max, £150,000 min
    const tier3 = callArgs.find((league: League) => league.tier === 3);
    expect(tier3?.leagueRules.maximumPrize).toBe(300000);
    expect(tier3?.leagueRules.minimumPrize).toBe(150000);

    // Tier 4: £100,000 max, £50,000 min
    const tier4 = callArgs.find((league: League) => league.tier === 4);
    expect(tier4?.leagueRules.maximumPrize).toBe(100000);
    expect(tier4?.leagueRules.minimumPrize).toBe(50000);
  });
});
