import { describe, expect, it } from 'vitest';

import { Fixture } from '@/entities/Fixture.js';
import { Team } from '@/entities/Team.js';
import { TeamsFactory } from '@/testing/factories/teamFactory.js';
import { mockLeagueRepository, mockTeamRepository } from '@/testing/mockRepositories.ts';
import { generateFixtures } from './fixtures.js';

describe('generateFixtures', () => {
  function generateMockTeam(id: number): Team {
    return TeamsFactory.build({
      teamId: `team${id}`,
      gameworldId: 'gameworld1',
      tier: 1,
      teamName: `teamName${id}`,
      league: { id: 'league1' } as any,
    });
  }

  it('should generate the correct number of fixtures for 15 teams', () => {
    const teams: Team[] = Array.from({ length: 15 }, (_, i) => generateMockTeam(i + 1));

    const fixtures = generateFixtures(mockTeamRepository, mockLeagueRepository, teams);

    expect(fixtures).toHaveLength(105);
  });

  it.each(Array.from({ length: 15 }, (_, i) => i + 1))(
    'team%i should have exactly 14 games',
    (teamNumber) => {
      const teams: Team[] = Array.from({ length: 15 }, (_, i) => generateMockTeam(i + 1));

      mockTeamRepository.createFromPK.mockImplementation((teamId: string) => {
        return teams.find((team) => team.teamId === teamId)!;
      });

      const fixtures = generateFixtures(mockTeamRepository, mockLeagueRepository, teams);

      const teamFixtures = fixtures.filter(
        (fixture: Fixture) =>
          fixture.homeTeam.teamId === `team${teamNumber}` ||
          fixture.awayTeam.teamId === `team${teamNumber}`
      );

      expect(teamFixtures).toHaveLength(14);
    }
  );

  it('should alternate home and away matches', () => {
    const teams: Team[] = Array.from({ length: 5 }, (_, i) => generateMockTeam(i + 1));

    const fixtures = generateFixtures(mockTeamRepository, mockLeagueRepository, teams);

    const homeMatches = fixtures.filter((fixture: Fixture) => fixture.homeTeam.teamId === 'team1');
    const awayMatches = fixtures.filter((fixture: Fixture) => fixture.awayTeam.teamId === 'team1');

    expect(homeMatches).toHaveLength(2);
    expect(awayMatches).toHaveLength(2);
  });

  it('should throw an error if teams array is empty', () => {
    expect(() => generateFixtures(mockTeamRepository, mockLeagueRepository, [])).toThrow(
      'Need at least 2 teams to generate fixtures'
    );
  });
});
