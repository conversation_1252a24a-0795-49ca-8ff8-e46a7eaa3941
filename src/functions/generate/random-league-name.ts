import { randomItem } from '@/utils/random.js';

/**
 * Generates a random league name based on tier
 * Lower tier numbers represent higher-quality leagues (tier 1 is the top league)
 * @param tier The tier of the league (1 is highest)
 * @param parentName Optional parent league name for child leagues
 * @param childIndex Optional index for child leagues (0, 1, 2, etc.)
 */
export function generateRandomLeagueName(
  tier: number,
  parentName?: string,
  childIndex?: number
): string {
  // If this is a child league and we have a parent name, use a division naming scheme
  if (parentName && childIndex !== undefined) {
    const divisions = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I'];
    const childSuffixes = [
      'North',
      'South',
      'East',
      'West',
      'Central',
      'Regional',
      'Metropolitan',
      'Rural',
    ];

    // For first few children, use simple A, B, C naming
    if (childIndex < divisions.length) {
      return `${parentName} Division ${divisions[childIndex]}`;
    }
    // For additional children, use geographic designations
    else {
      const geoIndex = childIndex - divisions.length;
      return `${parentName} ${childSuffixes[geoIndex % childSuffixes.length]}`;
    }
  }

  // For tier 1 (top league) - prestigious, serious names
  if (tier === 1) {
    const premierNames = [
      'Premier Championship',
      'Elite League',
      'Superstar Division',
      'Champions League',
      'Legends Premier',
      'Golden Boot League',
      'Top Flight Championship',
      'Ultimate Premier',
      'First Class League',
      'Supreme Division',
      'Virtuoso League',
      'Maestro Championship',
      'Pinnacle Division',
      'Apex League',
      'Crown Jewel Championship',
    ];
    return randomItem(premierNames);
  }

  // For tier 2 - professional but not quite top tier
  if (tier === 2) {
    const tier2Names = [
      'Championship Division',
      'First Division',
      'Advanced League',
      'Superior Championship',
      'Pro League',
      'Major Division',
      'Senior Championship',
      'High Performance League',
      'Excellence Division',
      'Rising Stars League',
      'Ascension Championship',
      'Promotion League',
      'Contender Division',
      'Challenger Circuit',
      'Elite Aspirants League',
    ];
    return randomItem(tier2Names);
  }

  // For tier 3 - semi-professional with a touch of humor
  if (tier === 3) {
    // 50% chance of a more serious name, 50% chance of a slightly humorous name
    if (Math.random() < 0.5) {
      const tier3Names = [
        'National League',
        'Regional Championship',
        'Second Division',
        'Standard League',
        'Progressive Division',
        'Competitive League',
        'Development Championship',
        'Contender Division',
        'Aspiring League',
        'Challenger Championship',
      ];
      return randomItem(tier3Names);
    } else {
      const prefixes = [
        'Ambitious',
        'Dedicated',
        'Enthusiastic',
        'Passionate',
        'Determined',
        'Hopeful',
        'Eager',
        'Weekend',
        'After-Work',
        'Local',
      ];

      const suffixes = [
        'Heroes League',
        'Challengers Cup',
        'Contenders Division',
        'Strikers Championship',
        'Football Alliance',
        'Athletic Association',
        'Stars Division',
        'Footballers League',
        'Players Championship',
        'Kickabout League',
      ];

      return `${randomItem(prefixes)} ${randomItem(suffixes)}`;
    }
  }

  // For tier 4 - definitely humorous
  if (tier === 4) {
    // 70% chance of a humorous name, 30% chance of a very humorous name
    if (Math.random() < 0.7) {
      const humorousAdjectives = [
        'Wheezy',
        'Wobbly',
        'Creaky',
        'Rusty',
        'Slow-Motion',
        'Out-of-Breath',
        'Sweaty',
        'Tired',
        'Persistent',
        'Optimistic',
        'Beer-Loving',
        'Post-Pub',
        'Sunday Morning',
        'Enthusiastic',
        'Try-Hard',
      ];

      const prefixes = [
        'Sunday',
        'Amateur',
        'Recreational',
        'Grassroots',
        'Community',
        'Local',
        'Pub',
        'Village',
        'Casual',
        'Weekend Warriors',
        'After-Work',
        'Beer Belly',
        'Dad Bod',
        'Kickabout',
        'Five-a-Side',
      ];

      const suffixes = [
        'League',
        'Division',
        'Championship',
        'Conference',
        'Association',
        'Cup',
        'Trophy',
        'Series',
        'Tournament',
        'Alliance',
      ];

      return `${randomItem(humorousAdjectives)} ${randomItem(prefixes)} ${randomItem(suffixes)}`;
    } else {
      // Very humorous names
      const funnyLeagueConcepts = [
        'The "I Used To Be Good" League',
        'The "One More Beer" Championship',
        'The "My Glory Days Are Behind Me" Division',
        'The "I Swear I Was Scouted Once" Cup',
        'The "My Knee Isn\'t What It Used To Be" Tournament',
        'The "Just Don\'t Tell My Doctor" League',
        'The "I\'ll Start The Diet Tomorrow" Championship',
        'The "This Counts As Exercise" Division',
        'The "At Least We\'re Not Last" Cup',
        'The "We\'re Here For The Post-Match Pints" League',
        'The "Stretching Is For Youngsters" Division',
        'The "Remember When We Could Run?" Cup',
        'The "I\'ll Feel This Tomorrow" League',
        'The "Where\'s The Defibrillator?" Championship',
        'The "Jumpers for Goalposts" Premier League',
      ];
      return randomItem(funnyLeagueConcepts);
    }
  }

  // If we somehow get a tier beyond 4, default to tier 4 behavior
  return generateRandomLeagueName(4);
}
