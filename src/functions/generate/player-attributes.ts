// Constants for the seeded random number generator
import { Player } from '@/entities/Player.js';
import { PlayerAttributes } from '@/entities/PlayerAttributes.js';
import { PlayerTemplate } from '@/functions/generate/player.js';
import { BaseAttributeName } from '@/types/attribute-utils.js';
import { seededRandom } from '@/utils/seeded-random.js';

/**
 * Helper function to set attribute values on a PlayerAttributes instance
 * @param attributes The PlayerAttributes instance
 * @param attrName The base attribute name
 * @param current The current value
 * @param potential The potential value
 * @param isGoalkeeper
 */
function setAttributeValues(
  attributes: PlayerAttributes,
  attrName: BaseAttributeName,
  current: number,
  potential: number,
  isGoalkeeper: boolean = false
): void {
  switch (attrName) {
    case 'reflexes':
      attributes.reflexesCurrent = current;
      attributes.reflexesPotential = isGoalkeeper ? potential : current;
      break;
    case 'positioning':
      attributes.positioningCurrent = current;
      attributes.positioningPotential = isGoalkeeper ? potential : current;
      break;
    case 'shotStopping':
      attributes.shotStoppingCurrent = current;
      attributes.shotStoppingPotential = isGoalkeeper ? potential : current;
      break;
    case 'tackling':
      attributes.tacklingCurrent = current;
      attributes.tacklingPotential = isGoalkeeper ? current : potential;
      break;
    case 'marking':
      attributes.markingCurrent = current;
      attributes.markingPotential = isGoalkeeper ? current : potential;
      break;
    case 'heading':
      attributes.headingCurrent = current;
      attributes.headingPotential = isGoalkeeper ? current : potential;
      break;
    case 'finishing':
      attributes.finishingCurrent = current;
      attributes.finishingPotential = isGoalkeeper ? current : potential;
      break;
    case 'pace':
      attributes.paceCurrent = current;
      attributes.pacePotential = isGoalkeeper ? current : potential;
      break;
    case 'crossing':
      attributes.crossingCurrent = current;
      attributes.crossingPotential = isGoalkeeper ? current : potential;
      break;
    case 'passing':
      attributes.passingCurrent = current;
      attributes.passingPotential = isGoalkeeper ? current : potential;
      break;
    case 'vision':
      attributes.visionCurrent = current;
      attributes.visionPotential = isGoalkeeper ? current : potential;
      break;
    case 'ballControl':
      attributes.ballControlCurrent = current;
      attributes.ballControlPotential = isGoalkeeper ? current : potential;
      break;
  }
}

/**
 * Helper function to get attribute values from a PlayerAttributes instance
 * @param attributes The PlayerAttributes instance
 * @param attrName The base attribute name
 * @returns A tuple of [current, potential] values
 */
function getAttributeValues(
  attributes: PlayerAttributes,
  attrName: BaseAttributeName
): [number, number] {
  switch (attrName) {
    case 'reflexes':
      return [attributes.reflexesCurrent, attributes.reflexesPotential];
    case 'positioning':
      return [attributes.positioningCurrent, attributes.positioningPotential];
    case 'shotStopping':
      return [attributes.shotStoppingCurrent, attributes.shotStoppingPotential];
    case 'tackling':
      return [attributes.tacklingCurrent, attributes.tacklingPotential];
    case 'marking':
      return [attributes.markingCurrent, attributes.markingPotential];
    case 'heading':
      return [attributes.headingCurrent, attributes.headingPotential];
    case 'finishing':
      return [attributes.finishingCurrent, attributes.finishingPotential];
    case 'pace':
      return [attributes.paceCurrent, attributes.pacePotential];
    case 'crossing':
      return [attributes.crossingCurrent, attributes.crossingPotential];
    case 'passing':
      return [attributes.passingCurrent, attributes.passingPotential];
    case 'vision':
      return [attributes.visionCurrent, attributes.visionPotential];
    case 'ballControl':
      return [attributes.ballControlCurrent, attributes.ballControlPotential];
    default:
      return [0, 0]; // Fallback for TypeScript exhaustiveness checking
  }
}

/**
 * seededRandom() returns a pseudo-random number between 0 and 1.
 * Math.pow(seededRandom(), 2) squares that number, biasing the result towards 0 (lower values are more likely).
 * The result is then scaled by 0.75 and shifted up by 0.25, so the final value is between 0.25 and 1.
 * This means most non-focus attributes will get a weight closer to 0.25, but can be as high as 1, introducing some variability in attribute generation.
 */
function getDefaultAttributeWeight() {
  return Math.pow(seededRandom(), 2) * 0.75 + 0.25;
}

export const highBaseSkillAtTier = [28, 24, 18, 12];
export const lowBaseSkillAtTier = [19, 15, 10, 6];

export function getPlayerTemplates(tier: number): PlayerTemplate[] {
  return [
    // goalkeepers (1 decent, 1 poor)
    {
      skills: ['reflexes', 'positioning', 'shotStopping'],
      baseSkill: highBaseSkillAtTier[tier - 1]!,
      isGoalkeeper: true,
    },
    {
      skills: ['reflexes', 'positioning', 'shotStopping'],
      baseSkill: lowBaseSkillAtTier[tier - 1]!,
      isGoalkeeper: true,
    },
    // defenders (1 decent, 2 poor)
    {
      skills: ['tackling', 'marking', 'heading'],
      baseSkill: highBaseSkillAtTier[tier - 1]!,
    },
    {
      skills: ['tackling', 'marking', 'heading'],
      baseSkill: lowBaseSkillAtTier[tier - 1]!,
    },
    {
      skills: ['tackling', 'marking', 'heading'],
      baseSkill: lowBaseSkillAtTier[tier - 1]!,
    },
    // attackers (1 decent, 2 poor)
    {
      skills: ['finishing', 'pace', 'crossing'],
      baseSkill: highBaseSkillAtTier[tier - 1]!,
    },
    {
      skills: ['finishing', 'pace', 'crossing'],
      baseSkill: lowBaseSkillAtTier[tier - 1]!,
    },
    {
      skills: ['finishing', 'pace', 'crossing'],
      baseSkill: lowBaseSkillAtTier[tier - 1]!,
    },
    // midfielders (1 decent, 2 poor)
    {
      skills: ['passing', 'vision', 'ballControl'],
      baseSkill: highBaseSkillAtTier[tier - 1]!,
    },
    {
      skills: ['passing', 'vision', 'ballControl'],
      baseSkill: lowBaseSkillAtTier[tier - 1]!,
    },
    {
      skills: ['passing', 'vision', 'ballControl'],
      baseSkill: lowBaseSkillAtTier[tier - 1]!,
    },
  ];
}

// Constants for skill range adjustments
const SKILL_RANGE_OFFSET = 2; // Defines variation around the base skill level
export const ATTRIBUTE_MIN = 1; // Minimum possible attribute value
export const ATTRIBUTE_MAX = 40; // Maximum possible attribute value
const RANDOM_FACTOR_RANGE = 4; // Determines the spread of the random variation
const RANDOM_FACTOR_OFFSET = 2; // Centers the random variation around 0
const FOCUS_CATEGORY_WEIGHT = 1; // Increased weight for focus categories
const TIER_BASE_VALUES = [3500000, 1000000, 300000, 100000]; // Good values for each tier
const TIER_THRESHOLDS = [19, 15, 10, 6]; // Minimum skill level for each tier

/**
 * Generates a PlayerAttributes entity instance with randomly generated attribute values
 * @param skillLevel The base skill level to use for generating attributes
 * @param minPotential The minimum potential value
 * @param maxPotential The maximum potential value
 * @param focusCategories Categories to emphasize when generating attributes
 * @param isGoalkeeper Whether the player is a goalkeeper
 * @returns A new PlayerAttributes instance
 */
export function generatePlayerAttributes(
  skillLevel: number,
  minPotential: number,
  maxPotential: number,
  focusCategories: BaseAttributeName[],
  isGoalkeeper: boolean = false
): PlayerAttributes {
  // Create a new PlayerAttributes instance
  const attributes = new PlayerAttributes();

  const baseValue = skillLevel - SKILL_RANGE_OFFSET;

  // Assign base weights to all categories
  const categoryWeights: Record<BaseAttributeName, number> = {
    reflexes: 0.1,
    positioning: 0.1,
    shotStopping: 0.1,
    tackling: getDefaultAttributeWeight(),
    marking: getDefaultAttributeWeight(),
    heading: getDefaultAttributeWeight(),
    finishing: getDefaultAttributeWeight(),
    pace: getDefaultAttributeWeight(),
    crossing: getDefaultAttributeWeight(),
    passing: getDefaultAttributeWeight(),
    vision: getDefaultAttributeWeight(),
    ballControl: getDefaultAttributeWeight(),
  };

  // Increase weights for the specified focus categories
  focusCategories.forEach((category) => {
    categoryWeights[category] = FOCUS_CATEGORY_WEIGHT;
  });

  // Function to generate a single attribute's current and potential values
  function generateAttributeValues(name: BaseAttributeName): [number, number] {
    const weight = categoryWeights[name];
    const randomFactor = seededRandom() * RANDOM_FACTOR_RANGE - RANDOM_FACTOR_OFFSET; // Random value between -2 and 2
    const current = Math.min(
      ATTRIBUTE_MAX,
      Math.max(ATTRIBUTE_MIN, Math.round((baseValue + randomFactor) * weight))
    );
    const potential = generatePotential(current, minPotential, maxPotential);
    return [current, potential];
  }

  // Generate all attributes directly on the PlayerAttributes instance
  const baseAttributes: BaseAttributeName[] = [
    'reflexes',
    'positioning',
    'shotStopping',
    'tackling',
    'marking',
    'heading',
    'finishing',
    'pace',
    'crossing',
    'passing',
    'vision',
    'ballControl',
  ];

  // Apply the generated values directly to the PlayerAttributes instance
  baseAttributes.forEach((attr) => {
    const [current, potential] = generateAttributeValues(attr);
    setAttributeValues(attributes, attr, current, potential, isGoalkeeper);
  });

  attributes.stamina = seededRandom() * 0.7 + 0.3; // random value 0.3-1

  return attributes;
}

/**
 * Generate a potential value for an attribute
 * @param current
 * @param minPotential
 * @param maxPotential
 */
function generatePotential(current: number, minPotential: number, maxPotential: number): number {
  const upper = Math.min(maxPotential, ATTRIBUTE_MAX);
  const lower = Math.min(Math.max(current + 1, minPotential), upper);

  if (lower > upper) {
    return Math.min(lower, current); // fallback: can't go higher
  }

  // Exponential bias: higher values are less likely
  const rand = seededRandom(); // 0..1
  const exp = 2; // increase for steeper drop-off
  const biased = 1 - Math.pow(1 - rand, exp); // bias towards lower values

  const potential = Math.round(lower + (upper - lower) * biased);
  return Math.max(lower, Math.min(potential, upper));
}

// Function to calculate a player's market value
export function calculatePlayerValue(player: Player): number {
  if (!player.attributes) {
    return 0;
  }

  const attrs = player.attributes;

  // Calculate effective attribute values (current + small potential bonus)
  // Potential has less significance as it's "unknown" to users
  const allAttributes = [
    attrs.reflexesCurrent + (attrs.reflexesPotential - attrs.reflexesCurrent) * 0.05,
    attrs.positioningCurrent + (attrs.positioningPotential - attrs.positioningCurrent) * 0.05,
    attrs.shotStoppingCurrent + (attrs.shotStoppingPotential - attrs.shotStoppingCurrent) * 0.05,
    attrs.tacklingCurrent + (attrs.tacklingPotential - attrs.tacklingCurrent) * 0.05,
    attrs.markingCurrent + (attrs.markingPotential - attrs.markingCurrent) * 0.05,
    attrs.headingCurrent + (attrs.headingPotential - attrs.headingCurrent) * 0.05,
    attrs.finishingCurrent + (attrs.finishingPotential - attrs.finishingCurrent) * 0.05,
    attrs.paceCurrent + (attrs.pacePotential - attrs.paceCurrent) * 0.05,
    attrs.crossingCurrent + (attrs.crossingPotential - attrs.crossingCurrent) * 0.05,
    attrs.passingCurrent + (attrs.passingPotential - attrs.passingCurrent) * 0.05,
    attrs.visionCurrent + (attrs.visionPotential - attrs.visionCurrent) * 0.05,
    attrs.ballControlCurrent + (attrs.ballControlPotential - attrs.ballControlCurrent) * 0.05,
  ];

  // Sort attributes in descending order to prioritize best skills
  const sortedAttributes = allAttributes.sort((a, b) => b - a);

  // Calculate base value using top 3 attributes (specialization focus)
  const top3Average = (sortedAttributes[0]! + sortedAttributes[1]! + sortedAttributes[2]!) / 3;

  // Calculate secondary attributes contribution (diminishing returns)
  const secondary3Average = (sortedAttributes[3]! + sortedAttributes[4]! + sortedAttributes[5]!) / 3;
  const remaining6Average = sortedAttributes.slice(6).reduce((sum, val) => sum + val, 0) / 6;

  // Weighted combination emphasizing specialization
  const overallRating = (
    top3Average * 0.7 +           // 70% weight on top 3 skills
    secondary3Average * 0.25 +    // 25% weight on next 3 skills
    remaining6Average * 0.05      // 5% weight on remaining skills
  );

  // Tier-based scaling to match income levels
  // Base value calculation with exponential scaling
  const normalizedRating = overallRating / ATTRIBUTE_MAX; // 0-1 scale
  const exponentialRating = Math.pow(normalizedRating, 2.8); // Much higher exponent for dramatic scaling

  // Tier value ranges based on income analysis:
  // Tier 1: £400k - £2.5M (can afford 2 good players per season)
  // Tier 2: £100k - £800k
  // Tier 3: £15k - £200k
  // Tier 4: £3k - £50k
  const maxValue = 2000000; // Further reduced maximum value
  const baseValue = exponentialRating * maxValue;

  // Add some randomness based on attribute distribution (specialization bonus)
  const attributeVariance = calculateAttributeVariance(sortedAttributes);
  const specializationBonus = 1 + (attributeVariance * 0.03); // Further reduced to 3% bonus

  const finalValue = baseValue * specializationBonus;

  // Ensure minimum value
  return Math.max(Math.round(finalValue), 1000);
}

// Helper function to calculate attribute variance (higher = more specialized)
function calculateAttributeVariance(sortedAttributes: number[]): number {
  const mean = sortedAttributes.reduce((sum, val) => sum + val, 0) / sortedAttributes.length;
  const variance = sortedAttributes.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / sortedAttributes.length;
  return Math.min(variance / 100, 1); // Normalize to 0-1 scale
}

export function formatPlayerAttributes(attributes: PlayerAttributes): string {
  const columnWidth = 20;
  const formatAttribute = (name: BaseAttributeName, attr: PlayerAttributes) => {
    const [current, potential] = getAttributeValues(attr, name);
    return `${name.padEnd(12)}${current.toString().padStart(2)}|${potential.toString().padStart(2)}`;
  };

  const goalkeeper = [
    'GOALKEEPING',
    formatAttribute('reflexes', attributes),
    formatAttribute('positioning', attributes),
    formatAttribute('shotStopping', attributes),
  ];

  const defender = [
    'DEFENDING',
    formatAttribute('tackling', attributes),
    formatAttribute('marking', attributes),
    formatAttribute('heading', attributes),
  ];

  const midfielder = [
    'MIDFIELD',
    formatAttribute('passing', attributes),
    formatAttribute('vision', attributes),
    formatAttribute('ballControl', attributes),
  ];

  const attacker = [
    'ATTACKING',
    formatAttribute('finishing', attributes),
    formatAttribute('pace', attributes),
    formatAttribute('crossing', attributes),
  ];

  // Pad columns to align
  const columns = [goalkeeper, defender, midfielder, attacker].map((col) =>
    col.map((line) => line.padEnd(columnWidth))
  );

  // Build output line by line
  let output = '';
  for (let i = 0; i < 4; i++) {
    output += columns.map((col) => col[i]).join(' ') + '\n';
  }

  return output;
}
