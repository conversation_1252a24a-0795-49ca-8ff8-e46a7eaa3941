import { Fixture } from '@/entities/Fixture.js';
import { Gameworld } from '@/entities/Gameworld.js';
import { League } from '@/entities/League.js';
import { LeagueRules } from '@/entities/LeagueRules.js';
import { Manager } from '@/entities/Manager.js';
import { Player } from '@/entities/Player.js';
import { PlayerAttributes } from '@/entities/PlayerAttributes.js';
import { Team } from '@/entities/Team.js';
import { SendMessageBatchCommand, SQSClient } from '@aws-sdk/client-sqs';
import { BatchWriteCommand, DynamoDBDocumentClient, QueryCommand } from '@aws-sdk/lib-dynamodb';
import { MikroORM } from '@mikro-orm/core';
import { PostgreSqlDriver } from '@mikro-orm/postgresql';
import { mockClient } from 'aws-sdk-client-mock';
import { v4 as uuidv4 } from 'uuid';
import { afterAll, beforeAll, beforeEach, describe, expect, it, vi } from 'vitest';

// Import the lambda handlers
import { mockGameworldRepository, mockLeagueRepository } from '@/testing/mockRepositories.js';
import { logger } from '@/utils/logger.js';
import mikroOrmConfig from '../../mikro-orm.config.js';
import { handler as generatePlayersHandler } from '../generate/generate-players.js';
import { handler as processFixtureGenerationHandler } from '../generate/processFixtureGenerationQueue.js';
import { handler as processEndOfSeasonHandler } from '../league/processEndOfSeason.js';
import { handler as checkForSeasonEndHandler } from './checkForSeasonEnd.js';
import { handler as processLeagueMovementHandler } from './processLeagueMovement.js';

// Mock AWS clients
const sqsMock = mockClient(SQSClient);
const dynamoMock = mockClient(DynamoDBDocumentClient);

describe('End of Season Flow E2E Test', () => {
  let orm: MikroORM<PostgreSqlDriver>;
  let gameworld: Gameworld;
  let league: League;
  let teams: Team[];
  let managers: Manager[];
  let players: Player[];

  beforeAll(async () => {
    if (!process.env.DATABASE_URL?.includes('localhost')) {
      throw new Error('Tests aborted: DATABASE_URL must be localhost');
    }

    vi.restoreAllMocks();
    vi.resetModules();
    vi.unmock('../../storage-interface/database-initializer.js');
    await MikroORM.init<PostgreSqlDriver>(mikroOrmConfig)
      .then(async (_orm) => {
        orm = _orm;
        // Create schema
        await _orm.em.getConnection().execute('CREATE EXTENSION IF NOT EXISTS "uuid-ossp";');
        await _orm.schema.refreshDatabase({ dropDb: false });
      })
      .catch((error) => {
        logger.error('Failed to initialize database', { error });
      });
  });

  afterAll(async () => {
    await orm.close();
  });

  beforeEach(async () => {
    // Reset AWS mocks
    sqsMock.reset();
    dynamoMock.reset();

    // Set up environment variables
    process.env.QUEUE_URL = 'https://sqs.test.amazonaws.com/123456789012/end-of-season-queue';
    process.env.LEAGUE_MOVEMENT_QUEUE_URL =
      'https://sqs.test.amazonaws.com/123456789012/league-movement-queue';
    process.env.GENERATE_PLAYERS_QUEUE_URL =
      'https://sqs.test.amazonaws.com/123456789012/generate-players-queue';
    process.env.FIXTURE_GENERATION_QUEUE_URL =
      'https://sqs.test.amazonaws.com/123456789012/fixture-generation-queue';
    process.env.FIXTURE_DETAIL_TABLE_NAME = 'test-fixture-details';

    // Mock SQS responses
    sqsMock.on(SendMessageBatchCommand).resolves({});

    // Mock DynamoDB responses for fixture details cleanup
    dynamoMock.on(QueryCommand).resolves({
      Items: [
        { fixtureId: 'old-fixture-1', gameworldId: 'test-gameworld-1', leagueId: 'test-league-1' },
      ],
    });
    dynamoMock.on(BatchWriteCommand).resolves({});

    // Clear all data
    await clearTestData();

    // Create test data
    await createTestData();

    // Update repositories with actual test data
    mockGameworldRepository.getCompletedSeasons.mockResolvedValue([gameworld]);
    mockLeagueRepository.getLeaguesByGameworld.mockResolvedValue([league]);
    mockLeagueRepository.getLeague.mockResolvedValue({
      ...league,
      teams: { getItems: () => teams },
    });
  });

  async function clearTestData() {
    await orm.em.nativeDelete(Fixture, {});
    await orm.em.nativeDelete(Player, {});
    await orm.em.nativeDelete(Manager, {});
    await orm.em.nativeDelete(Team, {});
    await orm.em.nativeDelete(LeagueRules, {});
    await orm.em.nativeDelete(League, {});
    await orm.em.nativeDelete(Gameworld, {});
  }

  async function createTestData() {
    const em = orm.em.fork();

    // Create gameworld with end date in the past (season completed)
    gameworld = new Gameworld();
    gameworld.id = uuidv4();
    gameworld.endDate = BigInt(Date.now() - 86400000); // 1 day ago (season ended)
    gameworld.highestManageableTier = 3;
    em.persist(gameworld);

    // Create league
    league = new League();
    league.id = uuidv4();
    league.gameworld = gameworld;
    league.name = 'Test League';
    league.tier = 1;
    em.persist(league);

    // Create league rules
    const leagueRules = new LeagueRules();
    leagueRules.league = league;
    leagueRules.promotionSpots = 1;
    leagueRules.relegationSpots = 1;
    leagueRules.teamCount = 4;
    leagueRules.maximumPrize = 100000;
    leagueRules.minimumPrize = 10000;
    em.persist(leagueRules);

    // Create teams with different standings
    teams = [];
    managers = [];
    players = [];
    for (let i = 1; i <= 4; i++) {
      const team = new Team();
      team.teamId = uuidv4();
      team.gameworldId = gameworld.id;
      team.league = league;
      team.tier = 1;
      team.teamName = `Test Team ${i}`;
      team.balance = 1000000;
      team.played = 6; // Full season played
      team.points = 15 - i * 3; // Descending points for league table
      team.goalsFor = 10;
      team.goalsAgainst = 5;
      team.wins = 5 - i;
      team.draws = 1;
      team.losses = i;
      em.persist(team);
      teams.push(team);

      // Create manager for each team
      const manager = new Manager();
      manager.managerId = uuidv4();
      manager.createdAt = BigInt(Date.now() - 86400000);
      manager.lastActive = BigInt(Date.now());
      manager.firstName = `Manager${i}`;
      manager.lastName = `Surname${i}`;
      manager.email = `manager${i}@test.com`;
      manager.team = team;
      manager.gameworldId = gameworld.id;
      manager.wins = 5 - i;
      manager.draws = 1;
      manager.defeats = i;
      manager.goalsScored = 10;
      manager.goalsConceded = 5;
      em.persist(manager);
      managers.push(manager);

      // Create players for each team (including some old players who should retire)
      for (let playerIndex = 1; playerIndex <= 15; playerIndex++) {
        const player = new Player();
        player.playerId = uuidv4();
        player.gameworldId = team.gameworldId;
        player.team = team;

        // Create some players of different ages for retirement testing
        if (playerIndex <= 2) {
          player.age = 39; // Will definitely retire
        } else if (playerIndex <= 4) {
          player.age = 38; // 50% chance to retire
        } else {
          player.age = 20 + (playerIndex % 15); // Normal ages
        }

        player.seed = BigInt(Date.now() + i * 1000 + playerIndex);
        player.firstName = `Player${playerIndex}`;
        player.surname = `Team${i}`;
        player.value = 100000 + playerIndex * 10000;
        player.energy = 50; // Low energy to test energy reset
        player.lastMatchPlayed = BigInt(Date.now() - 604800000); // 1 week ago
        player.suspendedForGames = 0;
        player.retiringAtEndOfSeason = false;
        em.persist(player);

        // Create player attributes
        const attributes = new PlayerAttributes();
        attributes.player = player;
        attributes.isGoalkeeper = playerIndex === 1;

        const baseValue = 15 + (playerIndex % 10);
        attributes.paceCurrent = baseValue;
        attributes.pacePotential = baseValue + 5;
        attributes.finishingCurrent = baseValue;
        attributes.finishingPotential = baseValue + 5;
        attributes.passingCurrent = baseValue;
        attributes.passingPotential = baseValue + 5;
        attributes.tacklingCurrent = baseValue;
        attributes.tacklingPotential = baseValue + 5;
        attributes.reflexesCurrent = playerIndex === 1 ? 20 : 5;
        attributes.reflexesPotential = attributes.reflexesCurrent + 5;
        attributes.positioningCurrent = baseValue;
        attributes.positioningPotential = baseValue + 5;
        attributes.shotStoppingCurrent = playerIndex === 1 ? 20 : 5;
        attributes.shotStoppingPotential = attributes.shotStoppingCurrent + 5;
        attributes.markingCurrent = baseValue;
        attributes.markingPotential = baseValue + 5;
        attributes.headingCurrent = baseValue;
        attributes.headingPotential = baseValue + 5;
        attributes.crossingCurrent = baseValue;
        attributes.crossingPotential = baseValue + 5;
        attributes.visionCurrent = baseValue;
        attributes.visionPotential = baseValue + 5;
        attributes.ballControlCurrent = baseValue;
        attributes.ballControlPotential = baseValue + 5;
        attributes.stamina = 0.8;
        em.persist(attributes);

        players.push(player);
      }
    }

    await em.flush();

    // Create fixtures for the season - each team plays every other team once
    const fixtures = [];
    for (let i = 0; i < teams.length; i++) {
      for (let j = i + 1; j < teams.length; j++) {
        const fixture = new Fixture();
        fixture.fixtureId = uuidv4();
        fixture.gameworldId = gameworld.id;
        fixture.league = league;
        fixture.homeTeam = teams[i]!;
        fixture.awayTeam = teams[j]!;
        fixture.date = BigInt(Date.now() - 604800000); // 1 week ago (played)
        fixture.played = true;

        em.persist(fixture);
        fixtures.push(fixture);
      }
    }

    await em.flush();
  }

  it('should execute the complete end of season flow', async () => {
    // Step 1: Check for Season End (EventBridge trigger)
    console.log('🔍 Step 1: Check for Season End');

    const checkSeasonEndEvent = {} as any;

    // Mock SQS calls to simulate downstream Lambda invocations
    sqsMock.on(SendMessageBatchCommand).callsFake(async (input) => {
      if (input.QueueUrl === process.env.QUEUE_URL) {
        // Simulate End of Season Lambda being triggered
        const endOfSeasonEvent = {
          Records:
            input.Entries?.map((entry: any, index: any) => ({
              messageId: `end-of-season-${index}`,
              body: JSON.parse(entry.MessageBody!),
            })) || [],
        };
        await processEndOfSeasonHandler(endOfSeasonEvent as any, {} as any);
      } else if (input.QueueUrl === process.env.LEAGUE_MOVEMENT_QUEUE_URL) {
        // Simulate League Movement Lambda being triggered
        const leagueMovementEvent = {
          Records:
            input.Entries?.map((entry: any, index: any) => ({
              messageId: `league-movement-${index}`,
              body: JSON.parse(entry.MessageBody!),
            })) || [],
        };
        await processLeagueMovementHandler(leagueMovementEvent as any, {} as any);
      } else if (input.QueueUrl === process.env.GENERATE_PLAYERS_QUEUE_URL) {
        // Simulate Generate Players Lambda being triggered
        const generatePlayersEvent = {
          Records:
            input.Entries?.map((entry: any, index: any) => ({
              messageId: `generate-players-${index}`,
              body: JSON.parse(entry.MessageBody!),
            })) || [],
        };
        await generatePlayersHandler(generatePlayersEvent as any, {} as any);
      } else if (input.QueueUrl === process.env.FIXTURE_GENERATION_QUEUE_URL) {
        // Simulate Fixture Generation Lambda being triggered
        const fixtureGenerationEvent = {
          Records:
            input.Entries?.map((entry: any, index: any) => ({
              messageId: `fixture-generation-${index}`,
              body: JSON.parse(entry.MessageBody!),
            })) || [],
        };
        await processFixtureGenerationHandler(fixtureGenerationEvent as any, {} as any);
      }
      return {};
    });

    let em = orm.em.fork();
    // Get initial state for comparison
    const initialPlayerCount = await em.count(Player, { gameworldId: gameworld.id });
    const initialFixtures = await em.find(Fixture, { gameworldId: gameworld.id });
    expect(initialFixtures.every((f) => f.played)).toBe(true); // all fixtures should be played to start with
    const initialGameworld = await em.findOneOrFail(Gameworld, { id: gameworld.id });

    // Execute the complete flow starting with checkForSeasonEnd
    await checkForSeasonEndHandler(checkSeasonEndEvent, {} as any);

    // Refork to reset any cache
    em = orm.em.fork();
    // Verify final effects of the complete flow
    console.log('✅ Verifying final effects of complete end-of-season flow');

    // Verify player aging and retirement occurred
    const playersAfterFlow = await em.find(Player, { gameworldId: gameworld.id });
    const retiringPlayers = playersAfterFlow.filter((p) => p.retiringAtEndOfSeason);
    expect(retiringPlayers.length).toBeGreaterThan(0);

    // Verify energy reset
    for (const player of playersAfterFlow) {
      expect(player.energy).toBe(100);
    }

    // Verify youth players were created
    const finalPlayerCount = await em.count(Player, { gameworldId: gameworld.id });
    expect(finalPlayerCount).toBeGreaterThan(initialPlayerCount);

    // Verify fixtures were created for new season
    const fixtures = await em.find(Fixture, { gameworldId: gameworld.id });
    expect(fixtures.length).toEqual(initialFixtures.length);
    expect(fixtures.every((f) => f.played)).toBe(false);

    // Verify gameworld end date was updated
    const updatedGameworld = await em.findOneOrFail(Gameworld, { id: gameworld.id });
    expect(updatedGameworld.endDate).toBeGreaterThan(initialGameworld.endDate);

    // Verify team standings were reset
    const updatedTeams = await em.find(Team, { gameworldId: gameworld.id });
    for (const team of updatedTeams) {
      expect(team.played).toBe(0);
      expect(team.points).toBe(0);
      expect(team.wins).toBe(0);
      expect(team.draws).toBe(0);
      expect(team.losses).toBe(0);
    }

    // Verify new season fixtures exist
    const newSeasonFixtures = await em.find(Fixture, {
      gameworldId: gameworld.id,
      played: false,
    });
    expect(newSeasonFixtures.length).toBeGreaterThan(0);

    console.log(`✅ Complete end-of-season flow executed successfully:
    - Players aged and ${retiringPlayers.length} marked for retirement
    - ${finalPlayerCount - initialPlayerCount} youth players created
    - ${fixtures.length} new fixtures generated
    - Team standings reset for new season
    - Gameworld end date updated`);
  });
});
