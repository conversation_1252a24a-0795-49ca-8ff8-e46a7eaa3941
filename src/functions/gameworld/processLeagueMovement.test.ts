import { handler } from '@/functions/gameworld/processLeagueMovement.js';
import { SQSEvent } from '@/middleware/sqs/types.js';
import { TransactionService } from '@/services/database/transaction-service.js';
import { LeagueMovementDatabaseService } from '@/services/gameworld/league-movement-database-service.js';
import { LeagueMovementService } from '@/services/gameworld/league-movement-service.js';
import { getMikroOrmService } from '@/storage-interface/database-initializer.js';
import { LeagueFactory } from '@/testing/factories/leagueFactory.js';
import { TeamsFactory } from '@/testing/factories/teamFactory.js';
import { createMockRepositories } from '@/testing/mockRepositories.js';
import { LeagueMovementEvent } from '@/types/generated/league-movement-event.js';
import { SQSBatchResponse } from 'aws-lambda';
import { beforeEach, describe, expect, it, vi } from 'vitest';

// Mock the services
vi.mock('@/services/database/transaction-service.js');
vi.mock('@/services/gameworld/league-movement-service.js');
vi.mock('@/services/gameworld/league-movement-database-service.js');

describe('processLeagueMovement handler (SQS-based)', () => {
  let mockTransactionService: any;
  let mockLeagueMovementService: any;
  let mockDatabaseService: any;
  let mockMikroOrmService: any;
  let repositories: ReturnType<typeof createMockRepositories>;
  let mockSQSEvent: SQSEvent<LeagueMovementEvent>;

  beforeEach(() => {
    // Setup mocks
    mockTransactionService = {
      executeInTransaction: vi.fn(),
    };

    mockLeagueMovementService = {
      prepareLeagueMovementData: vi.fn(),
    };

    mockDatabaseService = {
      executeLeagueMovement: vi.fn(),
      generateFixturesForAllLeagues: vi.fn(),
    };

    mockMikroOrmService = {
      getEntityManager: vi.fn(),
      close: vi.fn(),
    };

    repositories = createMockRepositories();

    mockSQSEvent = {
      Records: [
        {
          messageId: 'msg-1',
          body: { gameworldId: 'gameworld-1' },
        },
      ],
      context: {
        repositories,
      },
    } as any;

    // Mock constructors
    vi.mocked(TransactionService).mockImplementation(() => mockTransactionService);
    vi.mocked(LeagueMovementService).mockImplementation(() => mockLeagueMovementService);
    vi.mocked(LeagueMovementDatabaseService).mockImplementation(() => mockDatabaseService);
    vi.mocked(getMikroOrmService).mockResolvedValue(mockMikroOrmService);
  });

  it('should successfully process league movement', async () => {
    const movementData = {
      gameworldId: 'gameworld-1',
      leagues: [LeagueFactory.build({ id: 'league-1' }), LeagueFactory.build({ id: 'league-2' })],
      allTeams: [
        TeamsFactory.build({ teamId: 'team-1' }),
        TeamsFactory.build({ teamId: 'team-2' }),
      ],
      movements: [
        {
          teamId: 'team-1',
          fromLeagueId: 'league-1',
          toLeagueId: 'league-2',
        },
      ],
      sortedLeagues: new Map([
        ['league-1', [TeamsFactory.build()]],
        ['league-2', [TeamsFactory.build()]],
      ]),
    };

    mockLeagueMovementService.prepareLeagueMovementData.mockResolvedValue(movementData);
    mockTransactionService.executeInTransaction.mockImplementation(async (callback: any) => {
      return await callback({});
    });

    // Act
    const result = (await handler(mockSQSEvent, {} as any)) as SQSBatchResponse;

    // Assert
    expect(result.batchItemFailures).toHaveLength(0);
    expect(mockLeagueMovementService.prepareLeagueMovementData).toHaveBeenCalledWith('gameworld-1');
    expect(mockTransactionService.executeInTransaction).toHaveBeenCalledOnce();
    expect(mockDatabaseService.executeLeagueMovement).toHaveBeenCalledWith(
      {},
      movementData,
      mockLeagueMovementService
    );
    expect(mockDatabaseService.generateFixturesForAllLeagues).toHaveBeenCalledWith(
      movementData.leagues
    );
  });

  it('should handle league movement preparation failure and add to batch failures', async () => {
    // Arrange
    mockLeagueMovementService.prepareLeagueMovementData.mockRejectedValue(
      new Error('No leagues found')
    );

    // Act
    const result = (await handler(mockSQSEvent, {} as any)) as SQSBatchResponse;

    // Assert
    expect(result.batchItemFailures).toHaveLength(1);
    expect(result.batchItemFailures[0]!.itemIdentifier).toBe('msg-1');
    expect(mockTransactionService.executeInTransaction).not.toHaveBeenCalled();
  });

  it('should handle transaction failure and add to batch failures', async () => {
    const movementData = {
      gameworldId: 'gameworld-1',
      leagues: [],
      allTeams: [],
      movements: [],
      sortedLeagues: new Map(),
    };

    mockLeagueMovementService.prepareLeagueMovementData.mockResolvedValue(movementData);
    mockTransactionService.executeInTransaction.mockRejectedValue(new Error('Transaction failed'));

    // Act
    const result = (await handler(mockSQSEvent, {} as any)) as SQSBatchResponse;

    // Assert
    expect(result.batchItemFailures).toHaveLength(1);
    expect(result.batchItemFailures[0]!.itemIdentifier).toBe('msg-1');
    expect(mockDatabaseService.generateFixturesForAllLeagues).not.toHaveBeenCalled();
  });

  it('should handle post-transaction operation failures gracefully', async () => {
    const movementData = {
      gameworldId: 'gameworld-1',
      leagues: [LeagueFactory.build()],
      allTeams: [TeamsFactory.build()],
      movements: [],
      sortedLeagues: new Map(),
    };

    mockLeagueMovementService.prepareLeagueMovementData.mockResolvedValue(movementData);
    mockTransactionService.executeInTransaction.mockImplementation(async (callback: any) => {
      return await callback({});
    });
    mockDatabaseService.generateFixturesForAllLeagues.mockRejectedValue(
      new Error('Fixture generation failed')
    );

    // Act - should not throw error
    const result = (await handler(mockSQSEvent, {} as any)) as SQSBatchResponse;

    // Assert - should succeed despite post-transaction failure
    expect(result.batchItemFailures).toHaveLength(0);

    // Assert - transaction should have completed successfully
    expect(mockTransactionService.executeInTransaction).toHaveBeenCalledOnce();
    expect(mockDatabaseService.executeLeagueMovement).toHaveBeenCalled();
    expect(mockDatabaseService.generateFixturesForAllLeagues).toHaveBeenCalled();
  });

  it('should initialize all services correctly', async () => {
    const movementData = {
      gameworldId: 'gameworld-1',
      leagues: [],
      allTeams: [],
      movements: [],
      sortedLeagues: new Map(),
    };

    mockLeagueMovementService.prepareLeagueMovementData.mockResolvedValue(movementData);
    mockTransactionService.executeInTransaction.mockImplementation(async (callback: any) => {
      return await callback({});
    });

    // Act
    const result = (await handler(mockSQSEvent, {} as any)) as SQSBatchResponse;

    // Assert - verify all services were initialized with correct parameters
    expect(result.batchItemFailures).toHaveLength(0);
    expect(getMikroOrmService).toHaveBeenCalled();
    expect(TransactionService).toHaveBeenCalledWith(mockMikroOrmService);
    expect(LeagueMovementService).toHaveBeenCalledWith(repositories);
    expect(LeagueMovementDatabaseService).toHaveBeenCalled();
  });

  it('should log appropriate messages for successful processing', async () => {
    const movementData = {
      gameworldId: 'gameworld-1',
      leagues: [LeagueFactory.build(), LeagueFactory.build()],
      allTeams: [TeamsFactory.build(), TeamsFactory.build(), TeamsFactory.build()],
      movements: [{ teamId: 'team-1', fromLeagueId: 'league-1', toLeagueId: 'league-2' }],
      sortedLeagues: new Map([
        ['league-1', [TeamsFactory.build()]],
        ['league-2', [TeamsFactory.build()]],
      ]),
    };

    mockLeagueMovementService.prepareLeagueMovementData.mockResolvedValue(movementData);
    mockTransactionService.executeInTransaction.mockImplementation(async (callback: any) => {
      return await callback({});
    });

    // Act
    const result = (await handler(mockSQSEvent, {} as any)) as SQSBatchResponse;

    // Assert - the function should complete without throwing
    expect(result.batchItemFailures).toHaveLength(0);
    expect(mockLeagueMovementService.prepareLeagueMovementData).toHaveBeenCalled();
    expect(mockTransactionService.executeInTransaction).toHaveBeenCalled();
    expect(mockDatabaseService.generateFixturesForAllLeagues).toHaveBeenCalled();
  });
});
