import { httpMiddify } from '@/middleware/rest/index.js';
import { HttpEvent } from '@/middleware/rest/types.js';
import { SQS } from '@/services/sqs/sqs.js';
import { EndOfSeasonEvent } from '@/types/generated/end-of-season-event.js';
import { buildResponse } from '@/utils/buildResponse.js';
import { logger } from '@/utils/logger.js';
import { tracer } from '@/utils/tracer.js';
import { SendMessageBatchRequestEntry } from '@aws-sdk/client-sqs/dist-types/models/models_0.js';

interface PathParameters {
  gameworldId: string;
}

export type DebugTriggerEndOfSeasonEvent = HttpEvent<void, PathParameters, void>;

const sqs = new SQS({ tracer });

export const main = async function (event: DebugTriggerEndOfSeasonEvent) {
  const { leagueRepository } = event.context.repositories;

  const gameworldId = event.pathParameters.gameworldId;
  logger.debug('Triggering end of season processing', { gameworldId });

  const leagues = await leagueRepository.getLeaguesByGameworld(gameworldId, false);
  if (leagues.length === 0) {
    logger.warn('No leagues found for gameworld', { gameworldId });
    return buildResponse(404, JSON.stringify({ error: 'No leagues found for gameworld' }));
  }

  const sqsPayload: SendMessageBatchRequestEntry[] = leagues.map((league) => ({
    Id: league.id,
    MessageBody: JSON.stringify({
      gameworldId,
      leagueId: league.id,
    } as EndOfSeasonEvent),
  }));

  await sqs.sendBatch(process.env.QUEUE_URL!, sqsPayload);

  const response = buildResponse(
    200,
    JSON.stringify({
      message: 'End of season processing triggered',
      gameworldId,
    })
  );

  return Promise.resolve(response);
};

export const handler = httpMiddify(main, {});
