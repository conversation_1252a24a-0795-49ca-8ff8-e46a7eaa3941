import { httpMiddify } from '@/middleware/rest/index.js';
import { HttpEvent } from '@/middleware/rest/types.js';
import { buildResponse } from '@/utils/buildResponse.js';

export const main = async (event: HttpEvent<void, void, void>) => {
  const { teamRepository } = event.context.repositories;
  const availableTeams = await teamRepository.countAvailableTeams();
  return buildResponse(200, JSON.stringify({ remaining: availableTeams }));
};

export const handler = httpMiddify(main, {});
