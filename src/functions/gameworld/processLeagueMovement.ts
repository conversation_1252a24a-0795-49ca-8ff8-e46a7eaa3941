import { sqsMiddify } from '@/middleware/sqs/index.js';
import { SQSEvent } from '@/middleware/sqs/types.js';
import { TransactionService } from '@/services/database/transaction-service.js';
import { LeagueMovementDatabaseService } from '@/services/gameworld/league-movement-database-service.js';
import { LeagueMovementService } from '@/services/gameworld/league-movement-service.js';
import { getMikroOrmService } from '@/storage-interface/database-initializer.js';
import { LeagueMovementEvent } from '@/types/generated/league-movement-event.js';
import { logger } from '@/utils/logger.js';
import type { SQSBatchResponse } from 'aws-lambda';

/**
 * Process a single league movement operation within a database transaction
 */
async function processLeagueMovementTransaction(
  gameworldId: string,
  transactionService: TransactionService,
  leagueMovementService: LeagueMovementService,
  databaseService: LeagueMovementDatabaseService
): Promise<{ leagues: any[]; sortedLeagues: Map<string, any[]> }> {
  const correlationId = `league-movement-${gameworldId}-${Date.now()}`;

  logger.debug('Processing league movement', {
    gameworldId,
    correlationId,
  });

  // 1. Prepare all data needed for league movement (outside transaction)
  const movementData = await leagueMovementService.prepareLeagueMovementData(gameworldId);

  // 2. Execute all database operations within a transaction
  await transactionService.executeInTransaction(async (em) => {
    try {
      await databaseService.executeLeagueMovement(em, movementData, leagueMovementService);
    } catch (error) {
      logger.error('Failed to execute league movement in transaction', {
        gameworldId,
        correlationId,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw error;
    }
  });

  logger.info('Processed end of season movements and reset standings', {
    gameworldId,
    correlationId,
    totalMovements: movementData.movements.length,
    movements: movementData.movements.map((m) => ({
      teamId: m.teamId,
      from: m.fromLeagueId,
      to: m.toLeagueId,
    })),
  });

  return {
    leagues: movementData.leagues,
    sortedLeagues: movementData.sortedLeagues,
  };
}

export const main = async function (
  event: SQSEvent<LeagueMovementEvent>
): Promise<SQSBatchResponse> {
  const { repositories } = event.context;
  const batchItemFailures: { itemIdentifier: string }[] = [];

  // Initialize services
  const mikroOrmService = await getMikroOrmService();
  const transactionService = new TransactionService(mikroOrmService);
  const leagueMovementService = new LeagueMovementService(repositories);
  const databaseService = new LeagueMovementDatabaseService();

  for (const record of event.Records) {
    try {
      const { gameworldId } = record.body;
      const correlationId = `league-movement-${gameworldId}-${record.messageId}`;

      logger.info('Processing league movement from SQS', {
        gameworldId,
        messageId: record.messageId,
        correlationId,
      });

      // 1. Process league movement within transaction
      const { leagues, sortedLeagues } = await processLeagueMovementTransaction(
        gameworldId,
        transactionService,
        leagueMovementService,
        databaseService
      );

      // 2. Handle post-transaction operations
      try {
        // Generate fixtures for all leagues
        await databaseService.generateFixturesForAllLeagues(leagues);

        logger.info('League movement processing completed successfully', {
          gameworldId,
          messageId: record.messageId,
          correlationId,
          leagueCount: leagues.length,
          totalTeams: Array.from(sortedLeagues.values()).reduce(
            (sum, teams) => sum + teams.length,
            0
          ),
        });
      } catch (error) {
        // Log but don't fail the entire operation for post-transaction errors
        logger.warn('Post-transaction operations failed', {
          gameworldId,
          messageId: record.messageId,
          correlationId,
          error: error instanceof Error ? error.message : 'Unknown error',
        });
      }
    } catch (error) {
      logger.error('League movement processing failed', {
        gameworldId: record.body.gameworldId,
        messageId: record.messageId,
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined,
      });

      // Add to batch failures so the message goes to DLQ for retry
      batchItemFailures.push({ itemIdentifier: record.messageId });
    }
  }

  return { batchItemFailures };
};

export const handler = sqsMiddify<LeagueMovementEvent>(main);
