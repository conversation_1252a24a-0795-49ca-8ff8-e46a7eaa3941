import { httpMiddify } from '@/middleware/rest/index.js';
import { HttpEvent } from '@/middleware/rest/types.js';
import { buildResponse } from '@/utils/buildResponse.js';
import { getUser } from '@/utils/getUser.js';
import { logger } from '@/utils/logger.js';

interface QueryParameters {
  limit?: string;
}

export type GetInboxMessagesEvent = HttpEvent<void, void, QueryParameters>;

/**
 * Lambda function to get all inbox messages for the current user's team
 */
export const main = async function (event: GetInboxMessagesEvent) {
  try {
    const { inboxRepository, managerRepository } = event.context.repositories;

    // Get the current user ID
    const userId = getUser(event);
    if (!userId) {
      return buildResponse(401, JSON.stringify({ error: 'Unauthorized' }));
    }

    // Get the manager to find the team and gameworld information
    const manager = await managerRepository.getManagerById(userId);
    if (!manager) {
      return buildResponse(404, JSON.stringify({ error: 'Manager not found' }));
    }

    if (!manager.team?.teamId || !manager.gameworldId) {
      return buildResponse(404, JSON.stringify({ error: 'Manager team or gameworld not found' }));
    }

    const teamId = manager.team.teamId;
    const gameworldId = manager.gameworldId;

    // Parse limit from query parameters
    const limit = event.queryStringParameters?.limit
      ? parseInt(event.queryStringParameters.limit, 10)
      : undefined;

    // Validate limit if provided
    if (limit !== undefined && (isNaN(limit) || limit < 1 || limit > 100)) {
      return buildResponse(
        400,
        JSON.stringify({
          error: 'Invalid limit parameter. Must be a number between 1 and 100.',
        })
      );
    }

    logger.debug('Getting inbox messages for team', {
      managerId: userId,
      teamId,
      gameworldId,
      limit,
    });

    // Get inbox messages for the team
    const messages = await inboxRepository.getMessagesByGameworldAndTeam(
      gameworldId,
      teamId,
      limit
    );

    // Transform messages for response (parse extra field if it's JSON)
    const transformedMessages = messages.map((message) => {
      let extra;
      try {
        extra = JSON.parse(message.extra);
      } catch {
        // If extra is not valid JSON, keep it as string
        extra = message.extra;
      }

      return {
        id: message.id,
        date: message.date,
        message: message.message,
        extra,
        gameworldId: message.gameworldId,
        teamId: message.teamId,
      };
    });

    logger.debug('Retrieved inbox messages', {
      managerId: userId,
      teamId,
      gameworldId,
      messageCount: transformedMessages.length,
    });

    return buildResponse(
      200,
      JSON.stringify({
        messages: transformedMessages,
        count: transformedMessages.length,
        teamId,
        gameworldId,
      })
    );
  } catch (error) {
    logger.error('Failed to get inbox messages', { error, userId: getUser(event) });
    return buildResponse(500, JSON.stringify({ error: 'Internal server error' }));
  }
};

export const handler = httpMiddify(main, {});
