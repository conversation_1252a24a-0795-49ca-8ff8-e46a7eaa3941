import { AppVersion } from '@/entities/AppVersion.js';
import { httpMiddify } from '@/middleware/rest/index.js';
import { HttpEvent } from '@/middleware/rest/types.js';
import { buildResponse } from '@/utils/buildResponse.js';

interface Body {
  currentVersion: number;
  platform: 'android' | 'ios';
}
export const main = async function (event: HttpEvent<Body, void, void>) {
  const { teamRepository } = event.context.repositories;
  const em = teamRepository.getEntityManager();
  const value = await em.findOne(AppVersion, { platform: event.body.platform });
  if (!value) {
    return buildResponse(
      500,
      JSON.stringify({ error: 'Platform not found', platform: event.body.platform })
    );
  }

  return buildResponse(
    200,
    JSON.stringify({
      latest: value?.latestVersion,
      forceUpdate: value?.forceUpdate,
      versionString: value?.versionString,
    })
  );
};

export const handler = httpMiddify(main, {
  schema: {
    type: 'object',
    properties: {
      body: {
        type: 'object',
        properties: {
          currentVersion: {
            type: 'number',
          },
          platform: {
            type: 'string',
            enum: ['android', 'ios'],
          },
        },
        required: ['currentVersion', 'platform'],
        additionalProperties: false,
      },
    },
    required: ['body'],
  },
});
