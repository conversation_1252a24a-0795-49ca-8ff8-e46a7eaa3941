export const feedbackEventSchema = {
  $schema: 'http://json-schema.org/draft-07/schema#',
  type: 'object',
  properties: {
    body: {
      type: 'object',
      properties: {
        type: {
          type: 'string',
          enum: ['bug', 'feature', 'feedback'],
        },
        title: {
          type: 'string',
          minLength: 3,
        },
        description: {
          type: 'string',
          minLength: 10,
        },
        metadata: {
          type: 'object',
          additionalProperties: true,
        },
      },
      required: ['type', 'title', 'description'],
      additionalProperties: false,
    },
  },
  required: ['body'],
};
