import createHttpEvent from '@/testing/createHttpEvent.js';
import {
  mockManagerRepository,
  mockPlayerRepository,
  resetAllRepositoryMocks,
} from '@/testing/mockRepositories.js';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { handler } from './useRedCardAppeal.js';

describe('Use Red Card Appeal Handler', () => {
  const mockContext = {} as any;

  beforeEach(() => {
    resetAllRepositoryMocks();
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  it('returns 404 when manager or team is not found', async () => {
    const event = createHttpEvent({
      pathParameters: { gameworldId: 'test-gameworld', playerId: 'player-id' },
      headers: { Authorization: 'Bearer valid-token' },
    });

    vi.mocked(mockManagerRepository.getManagerById).mockResolvedValue(null);

    const response = await handler(event, mockContext);
    const body = JSON.parse(response.body);

    expect(response.statusCode).toBe(404);
    expect(body.error).toBe('Manager or team not found');
  });

  it('returns 404 when player is not found', async () => {
    const event = createHttpEvent({
      pathParameters: { gameworldId: 'test-gameworld', playerId: 'player-id' },
      headers: { Authorization: 'Bearer valid-token' },
    });

    vi.mocked(mockManagerRepository.getManagerById).mockResolvedValue({
      team: { teamId: 'team-id' },
      cardAppeals: 1,
    });
    vi.mocked(mockPlayerRepository.getPlayer).mockResolvedValue(null);

    const response = await handler(event, mockContext);
    const body = JSON.parse(response.body);

    expect(response.statusCode).toBe(404);
    expect(body.error).toBe('Player not found');
  });

  it("returns 403 when player does not belong to the user's team", async () => {
    const event = createHttpEvent({
      pathParameters: { gameworldId: 'test-gameworld', playerId: 'player-id' },
      headers: { Authorization: 'Bearer valid-token' },
    });

    vi.mocked(mockManagerRepository.getManagerById).mockResolvedValue({
      team: { teamId: 'team-id' },
      cardAppeals: 1,
    });
    vi.mocked(mockPlayerRepository.getPlayer).mockResolvedValue({
      team: { teamId: 'other-team-id' },
      suspendedForGames: 2,
    });

    const response = await handler(event, mockContext);
    const body = JSON.parse(response.body);

    expect(response.statusCode).toBe(403);
    expect(body.error).toBe('Forbidden');
    expect(body.message).toBe('You can only use a red card appeal on players from your own team');
  });

  it('returns 400 when player is not suspended', async () => {
    const event = createHttpEvent({
      pathParameters: { gameworldId: 'test-gameworld', playerId: 'player-id' },
      headers: { Authorization: 'Bearer valid-token' },
    });

    vi.mocked(mockManagerRepository.getManagerById).mockResolvedValue({
      team: { teamId: 'team-id' },
      cardAppeals: 1,
    });
    vi.mocked(mockPlayerRepository.getPlayer).mockResolvedValue({
      team: { teamId: 'team-id' },
      suspendedForGames: 0,
    });

    const response = await handler(event, mockContext);
    const body = JSON.parse(response.body);

    expect(response.statusCode).toBe(400);
    expect(body.error).toBe('Bad Request');
    expect(body.message).toBe('Player is not suspended or has no suspension games remaining');
  });

  it('returns 400 when manager has no card appeals', async () => {
    const event = createHttpEvent({
      pathParameters: { gameworldId: 'test-gameworld', playerId: 'player-id' },
      headers: { Authorization: 'Bearer valid-token' },
    });

    vi.mocked(mockManagerRepository.getManagerById).mockResolvedValue({
      team: { teamId: 'team-id' },
      cardAppeals: 0,
    });
    vi.mocked(mockPlayerRepository.getPlayer).mockResolvedValue({
      team: { teamId: 'team-id' },
      suspendedForGames: 2,
    });

    const response = await handler(event, mockContext);
    const body = JSON.parse(response.body);

    expect(response.statusCode).toBe(400);
    expect(body.error).toBe('Bad Request');
    expect(body.message).toBe('No card appeals available');
  });

  it('successfully reduces player suspension and decrements manager card appeals', async () => {
    const event = createHttpEvent({
      pathParameters: { gameworldId: 'test-gameworld', playerId: 'player-id' },
      headers: { Authorization: 'Bearer valid-token' },
    });

    const manager = {
      managerId: 'manager-id',
      team: { teamId: 'team-id' },
      cardAppeals: 1,
    };

    const player = {
      team: { teamId: 'team-id' },
      suspendedForGames: 2,
    };

    vi.mocked(mockManagerRepository.getManagerById).mockResolvedValue(manager);
    vi.mocked(mockPlayerRepository.getPlayer).mockResolvedValue(player);
    vi.mocked(mockManagerRepository.updateCardAppealCount).mockResolvedValue({});
    vi.mocked(mockPlayerRepository.updatePlayer).mockResolvedValue({});

    const response = await handler(event, mockContext);
    const body = JSON.parse(response.body);

    expect(response.statusCode).toBe(200);
    expect(body.suspendedForGames).toBe(1); // Reduced from 2 to 1
    expect(mockManagerRepository.updateCardAppealCount).toHaveBeenCalledWith('manager-id', -1);
    expect(mockPlayerRepository.updatePlayer).toHaveBeenCalledWith(player);
  });

  it('successfully reduces player suspension to 0', async () => {
    const event = createHttpEvent({
      pathParameters: { gameworldId: 'test-gameworld', playerId: 'player-id' },
      headers: { Authorization: 'Bearer valid-token' },
    });

    const manager = {
      managerId: 'manager-id',
      team: { teamId: 'team-id' },
      cardAppeals: 1,
    };

    const player = {
      team: { teamId: 'team-id' },
      suspendedForGames: 1,
    };

    vi.mocked(mockManagerRepository.getManagerById).mockResolvedValue(manager);
    vi.mocked(mockPlayerRepository.getPlayer).mockResolvedValue(player);
    vi.mocked(mockManagerRepository.updateCardAppealCount).mockResolvedValue({});
    vi.mocked(mockPlayerRepository.updatePlayer).mockResolvedValue({});

    const response = await handler(event, mockContext);
    const body = JSON.parse(response.body);

    expect(response.statusCode).toBe(200);
    expect(body.suspendedForGames).toBe(0); // Reduced from 1 to 0
    expect(mockManagerRepository.updateCardAppealCount).toHaveBeenCalledWith('manager-id', -1);
    expect(mockPlayerRepository.updatePlayer).toHaveBeenCalledWith(player);
  });
});
