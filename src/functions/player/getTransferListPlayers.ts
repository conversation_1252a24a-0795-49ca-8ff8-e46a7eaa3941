import { httpMiddify } from '@/middleware/rest/index.js';
import { HttpEvent } from '@/middleware/rest/types.js';
import {
  BidHistoryResponse,
  TransferListPathParameters as PathParameters,
  TransferListQueryParameters as QueryParameters,
  TransferListedPlayerResponse,
  TransferListResponse,
} from '@/model/transfer-list.js';
import { extractCurrentAttributes } from '@/utils/attributeUtils.js';
import { buildResponse } from '@/utils/buildResponse.js';
import { jsonStringifySafe } from '@/utils/misc.js';

export const main = async function (event: HttpEvent<void, PathParameters, QueryParameters>) {
  const { transferRepository } = event.context.repositories;

  const limit = event.queryStringParameters?.limit
    ? parseInt(event.queryStringParameters.limit, 10)
    : 25;

  const gameworldId = event.pathParameters.gameworldId;

  // Extract optional sorting parameters
  const sortBy = event.queryStringParameters?.sortBy;
  const sortDirection = event.queryStringParameters?.sortDirection;

  // Extract optional price filter parameters
  const minPrice = event.queryStringParameters?.minPrice
    ? parseInt(event.queryStringParameters.minPrice, 10)
    : undefined;
  const maxPrice = event.queryStringParameters?.maxPrice
    ? parseInt(event.queryStringParameters.maxPrice, 10)
    : undefined;

  // Query the transfer listed players with sorting and filtering
  const result = await transferRepository.getTransferListedPlayers(
    gameworldId,
    limit,
    event.queryStringParameters?.lastEvaluatedKey,
    sortBy,
    sortDirection,
    minPrice,
    maxPrice
  );

  if (!result || !result.players || result.players.length === 0) {
    return buildResponse(
      200,
      JSON.stringify({
        players: [],
        lastEvaluatedKey: undefined,
      })
    );
  }

  // Transform players to include only current attributes
  const transformedPlayers: TransferListedPlayerResponse[] = result.players.map((transferList) => {
    // Transform bid history to the response format
    const bidHistory: BidHistoryResponse[] = transferList.bidHistory.getItems().map((bid) => ({
      teamId: bid.team.teamId,
      teamName: bid.team.teamName,
      maximumBid: bid.maximumBid,
      bidTime: Number(bid.bidTime),
    }));

    return {
      gameworldId: transferList.gameworldId,
      teamId: transferList.player.team?.teamId || '',
      leagueId: '',
      playerId: transferList.player.playerId,
      firstName: transferList.player.firstName,
      surname: transferList.player.surname,
      attributes: extractCurrentAttributes(transferList.player.attributes),
      age: transferList.player.age,
      value: transferList.player.value,
      auctionStartPrice: transferList.auctionStartPrice,
      auctionCurrentPrice: transferList.auctionCurrentPrice,
      auctionEndTime: Number(transferList.auctionEndTime),
      bidHistory: bidHistory,
      isTransferListed: true,
    };
  });

  const response: TransferListResponse = {
    players: transformedPlayers,
    lastEvaluatedKey: result.lastEvaluatedKey,
  };

  return buildResponse(200, jsonStringifySafe(response));
};

export const handler = httpMiddify(main, {});
