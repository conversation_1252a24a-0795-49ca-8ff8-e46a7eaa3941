import { Fixture } from '@/entities/Fixture.js';
import { PlayerMatchHistory } from '@/entities/PlayerMatchHistory.js';
import { PlayerOverallStats } from '@/entities/PlayerOverallStats.js';
import { Team } from '@/entities/Team.js';
import { handler } from '@/functions/player/getPlayerStats.js';
import createHttpEvent from '@/testing/createHttpEvent.js';
import { PlayerFactory } from '@/testing/factories/playerFactory.js';
import { mockPlayerRepository } from '@/testing/mockRepositories.js';
import { Collection } from '@mikro-orm/core';
import { Context } from 'aws-lambda';
import { beforeEach, describe, expect, it, vi } from 'vitest';

const mockContext = {
  repositories: {
    playerRepository: mockPlayerRepository,
  },
} as unknown as Context;

// Simple factory functions for testing
const createTeam = (overrides: Partial<Team> = {}): Team => {
  const team = new Team();
  team.teamId = overrides.teamId ?? 'team-123';
  team.teamName = overrides.teamName ?? 'Test Team';
  return team;
};

const createFixture = (overrides: Partial<Fixture> = {}): Fixture => {
  const fixture = new Fixture();
  fixture.fixtureId = overrides.fixtureId ?? 'fixture-123';
  fixture.homeTeam =
    overrides.homeTeam ?? createTeam({ teamId: 'home-team', teamName: 'Home Team' });
  fixture.awayTeam =
    overrides.awayTeam ?? createTeam({ teamId: 'away-team', teamName: 'Away Team' });
  fixture.score = overrides.score ?? [2, 1];
  return fixture;
};

const createPlayerMatchHistory = (
  overrides: Partial<PlayerMatchHistory> = {}
): PlayerMatchHistory => {
  const history = new PlayerMatchHistory();
  history.fixtureId = overrides.fixtureId ?? 'fixture-123';
  history.fixture = overrides.fixture ?? createFixture({ fixtureId: history.fixtureId });
  history.yellowCards = overrides.yellowCards ?? 0;
  history.redCards = overrides.redCards ?? 0;
  history.passesCompleted = overrides.passesCompleted ?? 0;
  history.passesAttempted = overrides.passesAttempted ?? 0;
  history.successfulBallCarries = overrides.successfulBallCarries ?? 0;
  history.ballCarriesAttempted = overrides.ballCarriesAttempted ?? 0;
  history.shots = overrides.shots ?? 0;
  history.shotsOnTarget = overrides.shotsOnTarget ?? 0;
  history.goals = overrides.goals ?? 0;
  history.saves = overrides.saves ?? 0;
  history.tackles = overrides.tackles ?? 0;
  history.fouls = overrides.fouls ?? 0;
  return history;
};

const createPlayerOverallStats = (
  overrides: Partial<PlayerOverallStats> = {}
): PlayerOverallStats => {
  const stats = new PlayerOverallStats();
  stats.yellowCards = overrides.yellowCards ?? 0;
  stats.redCards = overrides.redCards ?? 0;
  stats.passesCompleted = overrides.passesCompleted ?? 0;
  stats.passesAttempted = overrides.passesAttempted ?? 0;
  stats.successfulBallCarries = overrides.successfulBallCarries ?? 0;
  stats.ballCarriesAttempted = overrides.ballCarriesAttempted ?? 0;
  stats.shots = overrides.shots ?? 0;
  stats.shotsOnTarget = overrides.shotsOnTarget ?? 0;
  stats.goals = overrides.goals ?? 0;
  stats.saves = overrides.saves ?? 0;
  stats.tackles = overrides.tackles ?? 0;
  stats.fouls = overrides.fouls ?? 0;
  return stats;
};

describe('getPlayerStats', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('returns player stats successfully', async () => {
    const overallStats = createPlayerOverallStats({
      yellowCards: 2,
      redCards: 0,
      passesCompleted: 150,
      passesAttempted: 180,
      successfulBallCarries: 45,
      ballCarriesAttempted: 60,
      shots: 25,
      shotsOnTarget: 15,
      goals: 8,
      saves: 0,
      tackles: 30,
      fouls: 12,
    });

    const fixture1 = createFixture({
      fixtureId: 'fixture-1',
      homeTeam: createTeam({ teamId: 'home-1', teamName: 'Home Team 1' }),
      awayTeam: createTeam({ teamId: 'away-1', teamName: 'Away Team 1' }),
      score: [2, 1],
    });

    const fixture2 = createFixture({
      fixtureId: 'fixture-2',
      homeTeam: createTeam({ teamId: 'home-2', teamName: 'Home Team 2' }),
      awayTeam: createTeam({ teamId: 'away-2', teamName: 'Away Team 2' }),
      score: [1, 3],
    });

    const matchHistory1 = createPlayerMatchHistory({
      fixtureId: 'fixture-1',
      fixture: fixture1,
      yellowCards: 1,
      redCards: 0,
      passesCompleted: 25,
      passesAttempted: 30,
      goals: 1,
    });

    const matchHistory2 = createPlayerMatchHistory({
      fixtureId: 'fixture-2',
      fixture: fixture2,
      yellowCards: 0,
      redCards: 0,
      passesCompleted: 20,
      passesAttempted: 25,
      goals: 0,
    });

    const player = PlayerFactory.build({
      playerId: 'player-123',
      gameworldId: 'gameworld-123',
      firstName: 'John',
      surname: 'Doe',
      age: 25,
      value: 1000000,
      energy: 85,
      lastMatchPlayed: 1234567890n,
      injuredUntil: undefined,
      suspendedForGames: 0,
      isTransferListed: false,
      retiringAtEndOfSeason: false,
      overallStats,
    });

    // Manually set up the match history collection
    player.matchHistory = new Collection(player, [matchHistory1, matchHistory2]);

    mockPlayerRepository.getPlayerWithStats.mockResolvedValue(player);

    const event = createHttpEvent({
      pathParameters: {
        gameworldId: 'gameworld-123',
        playerId: 'player-123',
      },
    });
    event.context = mockContext;

    const response = await handler(event, mockContext);

    expect(response.statusCode).toBe(200);
    const body = JSON.parse(response.body);

    expect(body).toEqual({
      playerId: 'player-123',
      gameworldId: 'gameworld-123',
      firstName: 'John',
      surname: 'Doe',
      age: 25,
      value: 1000000,
      energy: 85,
      lastMatchPlayed: 1234567890,
      injuredUntil: undefined,
      suspendedForGames: 0,
      isTransferListed: false,
      retiringAtEndOfSeason: false,
      overallStats: {
        yellowCards: 2,
        redCards: 0,
        passesCompleted: 150,
        passesAttempted: 180,
        successfulBallCarries: 45,
        ballCarriesAttempted: 60,
        shots: 25,
        shotsOnTarget: 15,
        goals: 8,
        saves: 0,
        tackles: 30,
        fouls: 12,
      },
      matchHistory: [
        {
          fixtureId: 'fixture-1',
          homeTeamName: 'Home Team 1',
          awayTeamName: 'Away Team 1',
          homeTeamScore: 2,
          awayTeamScore: 1,
          yellowCards: 1,
          redCards: 0,
          passesCompleted: 25,
          passesAttempted: 30,
          successfulBallCarries: 0,
          ballCarriesAttempted: 0,
          shots: 0,
          shotsOnTarget: 0,
          goals: 1,
          saves: 0,
          tackles: 0,
          fouls: 0,
        },
        {
          fixtureId: 'fixture-2',
          homeTeamName: 'Home Team 2',
          awayTeamName: 'Away Team 2',
          homeTeamScore: 1,
          awayTeamScore: 3,
          yellowCards: 0,
          redCards: 0,
          passesCompleted: 20,
          passesAttempted: 25,
          successfulBallCarries: 0,
          ballCarriesAttempted: 0,
          shots: 0,
          shotsOnTarget: 0,
          goals: 0,
          saves: 0,
          tackles: 0,
          fouls: 0,
        },
      ],
    });

    expect(mockPlayerRepository.getPlayerWithStats).toHaveBeenCalledWith(
      'gameworld-123',
      'player-123'
    );
  });

  it('returns 404 when player not found', async () => {
    mockPlayerRepository.getPlayerWithStats.mockResolvedValue(null);

    const event = createHttpEvent({
      pathParameters: {
        gameworldId: 'gameworld-123',
        playerId: 'nonexistent-player',
      },
    });
    event.context = mockContext;

    const response = await handler(event, mockContext);

    expect(response.statusCode).toBe(404);
    expect(JSON.parse(response.body)).toEqual({
      error: 'Player not found',
    });
  });

  it('handles missing path parameters', async () => {
    const event = createHttpEvent({
      pathParameters: {
        gameworldId: 'gameworld-123',
        // missing playerId
      },
    });
    event.context = mockContext;

    const response = await handler(event, mockContext);

    expect(response.statusCode).toBe(422);
    expect(mockPlayerRepository.getPlayerWithStats).not.toHaveBeenCalled();
  });
});
