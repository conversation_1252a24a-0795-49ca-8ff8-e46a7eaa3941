import { logger } from '@/utils/logger.js';
import { Request } from '@middy/core';

/**
 * Ensures validation errors are verbosely logged
 */
export const customSQSErrorHandle = () => {
  const onError = (request: Request): void => {
    if (request.error) {
      if (request.error.cause) {
        logger.error(request.error.message, { cause: request.error.cause });
      } else {
        logger.error(request.error.message);
      }
    }
  };

  return {
    onError,
  };
};
