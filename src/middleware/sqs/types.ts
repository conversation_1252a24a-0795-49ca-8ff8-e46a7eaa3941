import { Repositories } from '@/middleware/database/types.js';
import { <PERSON><PERSON><PERSON><PERSON>, EventWithRepositories } from '@/middleware/event/types.js';
import { MiddlewareObj as MiddyMiddlewareObj } from '@middy/core';
import type {
  Context,
  SQSBatchResponse,
  SQSMessageAttributes,
  SQSRecordAttributes,
} from 'aws-lambda';

export interface SQSRecord<T> {
  messageId: string;
  receiptHandle: string;
  body: T;
  attributes: SQSRecordAttributes;
  messageAttributes: SQSMessageAttributes;
  md5OfBody: string;
  eventSource: string;
  eventSourceARN: string;
  awsRegion: string;
}

export interface SQSEvent<T> extends EventWithRepositories {
  Records: SQSRecord<T>[];
  context: Context & {
    repositories: Repositories;
  };
}

export type SQSMiddlewareObj<T> = MiddyMiddlewareObj<SQSEvent<T>, SQSBatchResponse | void>;

export interface SQSMiddifyOptions {
  schema?: object;
  injectRepositories?: boolean;
}

export type SQSHandler<T = unknown> = EventHandler<SQSEvent<T>, SQSBatchResponse | void>;
