import { eventMiddify } from '@/middleware/event/index.js';
import { EventHandler } from '@/middleware/event/types.js';
import { customSQSErrorHandle } from '@/middleware/sqs/customSqsErrorHandler.js';
import { sqsRecordValidator } from '@/middleware/sqs/sqsRecordValidator.js';
import { SQSEvent, SQSMiddifyOptions } from '@/middleware/sqs/types.js';
import { logger } from '@/utils/logger.js';
import type { SQSBatchResponse } from 'aws-lambda';

/**
 *
 * @param handler - SQS Lambda handler
 * @param middleware
 * @returns
 */
export const sqsMiddify = <T>(
  handler: EventHandler<SQSEvent<T>, SQSBatchResponse | void>,
  { schema, injectRepositories }: SQSMiddifyOptions = {}
) => {
  const wrapped = eventMiddify(handler, { injectRepositories: injectRepositories ?? true });

  if (schema) {
    logger.warn(
      "Schema specified: all records will be pre-processed and validated, this may be expensive if you're working with large batch sizes!"
    );
    wrapped.use(sqsRecordValidator(schema));
  }

  wrapped.use(customSQSErrorHandle());

  return wrapped;
};
