/*
import { HttpEvent } from '@/middleware/rest/types.js';
import { logger } from '@/utils/logger.js';
import { CognitoJwtVerifier } from 'aws-jwt-verify';
import { MiddlewareObj } from '@middy/core';

const USER_POOL_ID = process.env.COGNITO_USER_POOL_ID!;
const CLIENT_ID = process.env.COGNITO_CLIENT_ID!;
const IDENTITY_POOL_ID = process.env.COGNITO_IDENTITY_POOL_ID!;

// Create verifiers for both user pool and identity pool tokens
const userPoolVerifier = CognitoJwtVerifier.create({
  userPoolId: USER_POOL_ID,
  tokenUse: 'access',
  clientId: CLIENT_ID,
});

const identityPoolVerifier = CognitoJwtVerifier.create({
  userPoolId: USER_POOL_ID,
  tokenUse: 'id',
  clientId: CLIENT_ID,
});

interface AuthContext {
  userId: string;
  isGuest: boolean;
  identityId?: string;
  deviceId?: string;
}

/!**
 * Middleware that handles both User Pool JWT tokens and Identity Pool tokens
 * - User Pool tokens: Authenticated users
 * - Identity Pool tokens: Guest users
 *!/
export const hybridAuthMiddleware = (): MiddlewareObj<HttpEvent<any, any, any>> => {
  return {
    before: async (request) => {
      const event = request.event;
      
      // Check for Authorization header
      const authHeader = event.headers?.Authorization || event.headers?.authorization;
      
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        // Check for guest user headers as fallback
        const guestUserId = event.headers?.['X-Guest-User-Id'] || event.headers?.['x-guest-user-id'];
        const deviceId = event.headers?.['X-Device-Id'] || event.headers?.['x-device-id'];
        
        if (guestUserId) {
          event.requestContext.auth = {
            userId: guestUserId,
            isGuest: true,
            deviceId,
          } as AuthContext;
          return;
        }
        
        throw new Error('Authorization required');
      }
      
      const token = authHeader.replace('Bearer ', '');
      
      try {
        // First, try to verify as User Pool token (authenticated user)
        const userPoolPayload = await userPoolVerifier.verify(token);
        
        event.requestContext.auth = {
          userId: userPoolPayload.sub,
          isGuest: false,
        } as AuthContext;
        
        logger.debug('Authenticated user verified', { userId: userPoolPayload.sub });
        return;
        
      } catch (userPoolError) {
        logger.debug('Not a valid User Pool token, trying Identity Pool', { error: userPoolError.message });
        
        try {
          // Try to decode as Identity Pool token
          const identityPayload = await verifyIdentityPoolToken(token);
          
          // For guest users, we'll use the identity ID as the user ID
          const identityId = identityPayload.identityId;
          const deviceId = event.headers?.['X-Device-Id'] || event.headers?.['x-device-id'];
          
          event.requestContext.auth = {
            userId: identityId,
            isGuest: true,
            identityId,
            deviceId,
          } as AuthContext;
          
          logger.debug('Guest user verified', { identityId, deviceId });
          return;
          
        } catch (identityPoolError) {
          logger.error('Token verification failed for both User Pool and Identity Pool', {
            userPoolError: userPoolError.message,
            identityPoolError: identityPoolError.message,
          });
          
          throw new Error('Invalid token');
        }
      }
    },
  };
};

/!**
 * Verify Identity Pool token
 * Note: This is a simplified version. In production, you might want to
 * validate the token more thoroughly or use AWS SDK to verify the identity.
 *!/
async function verifyIdentityPoolToken(token: string): Promise<{ identityId: string }> {
  try {
    // Decode the JWT token (without verification for now)
    const parts = token.split('.');
    if (parts.length !== 3) {
      throw new Error('Invalid JWT format');
    }
    
    const payload = JSON.parse(Buffer.from(parts[1], 'base64').toString());
    
    // Check if it's an identity pool token
    if (!payload.identityId || !payload.aud || payload.aud !== IDENTITY_POOL_ID) {
      throw new Error('Not a valid identity pool token');
    }
    
    return {
      identityId: payload.identityId,
    };
    
  } catch (error) {
    logger.error('Failed to verify identity pool token', { error });
    throw new Error('Invalid identity pool token');
  }
}

/!**
 * Helper function to get auth context from event
 *!/
export function getAuthContext(event: HttpEvent<any, any, any>): AuthContext {
  const auth = event.requestContext?.auth as AuthContext;
  
  if (!auth) {
    throw new Error('Auth context not found');
  }
  
  return auth;
}

/!**
 * Helper function to get user ID (works for both authenticated and guest users)
 *!/
export function getUserId(event: HttpEvent<any, any, any>): string {
  const auth = getAuthContext(event);
  return auth.userId;
}

/!**
 * Helper function to check if user is a guest
 *!/
export function isGuestUser(event: HttpEvent<any, any, any>): boolean {
  const auth = getAuthContext(event);
  return auth.isGuest;
}

/!**
 * Helper function to get device ID (for guest users)
 *!/
export function getDeviceId(event: HttpEvent<any, any, any>): string | undefined {
  const auth = getAuthContext(event);
  return auth.deviceId;
}
*/
