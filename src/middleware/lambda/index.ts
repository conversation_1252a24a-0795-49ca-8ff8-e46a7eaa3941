import { logger } from '@/utils/logger.js';
import { tracer } from '@/utils/tracer.js';
import {
  InvokeCommand,
  InvokeCommandInput,
  InvokeCommandOutput,
  LambdaClient,
  LambdaClientConfig,
} from '@aws-sdk/client-lambda';
import type { APIGatewayProxyResult } from 'aws-lambda';

export type PayloadResult<T> = { errorMessage: string; errorType: string } | T;

export interface LambdaServiceOptions {
  configuration?: LambdaClientConfig;
  tracer?: typeof tracer;
}

export class Lambda {
  private readonly client: LambdaClient;
  constructor(protected readonly _options: LambdaServiceOptions) {
    const lambdaOptions = {
      ..._options.configuration,
    };

    this.client = new LambdaClient(lambdaOptions);

    if (_options.tracer) {
      this.client = _options.tracer.captureAWSv3Client(this.client);
    }
  }

  private async invoke(
    payload: string | undefined,
    arn: string,
    additionalCmdInput: Partial<InvokeCommandInput> = {}
  ): Promise<InvokeCommandOutput> {
    const cmdInput: InvokeCommandInput = {
      FunctionName: arn,
      Payload: payload && Buffer.from(payload, 'utf8'),
      LogType: 'Tail',
      ...additionalCmdInput,
    };

    const cmd = new InvokeCommand(cmdInput);
    const result = await this.client.send(cmd);
    if (result.LogResult) {
      logger.debug(Buffer.from(result.LogResult, 'base64').toString());
    }

    // lambda invoke will always return a 200 status code if the lambda was invoked successfully
    if (!result.StatusCode || result.StatusCode < 200 || result.StatusCode >= 300) {
      logger.error('Failed to invoke lambda', { result });
      throw new Error(`${result.StatusCode} - Failed to invoke lambda: ${arn}`);
    }

    return result;
  }

  private marshallOutput<T extends object = object>(output: InvokeCommandOutput): T {
    if (!output.Payload) {
      logger.error('No payload returned from Lambda', { result: output });
      throw new Error('No payload returned from Lambda');
    }

    const resultObject: PayloadResult<T> = JSON.parse(
      Buffer.from(output.Payload).toString()
    ) as PayloadResult<T>;
    logger.debug('Result from lambda', { resultObject });

    if (typeof resultObject === 'object' && 'errorMessage' in resultObject) {
      logger.error('Error message found in lambda response', {
        errorMessage: resultObject.errorMessage,
      });
      throw new Error(resultObject.errorMessage);
    }

    return resultObject;
  }

  /**
   * Invokes a Lambda with an Event invocation type.
   *
   * @param {(string | undefined)} payload
   * @param {string} arn
   * @param {Partial<InvokeCommandInput>} [additionalCmdInput={}]
   * @return {*}  {Promise<void>}
   * @memberof Lambda
   */
  public async eventInvoke(
    payload: string | undefined,
    arn: string,
    additionalCmdInput: Partial<InvokeCommandInput> = {}
  ): Promise<void> {
    try {
      await this.invoke(payload, arn, {
        ...additionalCmdInput,
        InvocationType: 'Event',
      });
    } catch (e) {
      logger.error('Failed to invoke lambda', { error: e, payload, arn, additionalCmdInput });
      throw e;
    }
  }

  /**
   * Invokes a Lambda with a RequestResponse invocation type and returns the result as an APIGatewayProxyResult.
   * If you wish to invoke a http lambda and don't care about the response, use eventInvoke instead.
   *
   * @param {(string)} payload
   * @param {string} arn
   * @param {Partial<InvokeCommandInput>} [additionalCmdInput={}]
   * @return {*}  {Promise<APIGatewayProxyResult>}
   * @memberof Lambda
   */
  public async httpInvoke(
    payload: string,
    arn: string,
    additionalCmdInput: Partial<InvokeCommandInput> = {}
  ): Promise<APIGatewayProxyResult> {
    if (additionalCmdInput.InvocationType === 'Event') {
      logger.warn('Cannot invoke a Lambda with an Event invocation type using httpInvoke');
      throw new Error('Cannot invoke a Lambda with an Event invocation type using httpInvoke');
    }

    try {
      const output = await this.invoke(payload, arn, {
        ...additionalCmdInput,
        InvocationType: 'RequestResponse',
      });

      return this.marshallOutput<APIGatewayProxyResult>(output);
    } catch (e) {
      logger.error('Failed to invoke lambda', { error: e, payload, arn, additionalCmdInput });
      throw e;
    }
  }

  /**
   * Invokes a Lambda with a RequestResponse invocation type.
   *
   * @template T
   * @param {(string)} payload
   * @param {string} arn
   * @param {Partial<InvokeCommandInput>} [additionalCmdInput={}]
   * @return {*}  {Promise<T>}
   * @memberof Lambda
   */
  public async invokeWithResponse<T extends object>(
    payload: string,
    arn: string,
    additionalCmdInput: Partial<InvokeCommandInput> = {}
  ): Promise<T> {
    if (additionalCmdInput.InvocationType === 'Event') {
      logger.warn('Cannot invoke a Lambda with an Event invocation type using invokeWithResponse');
      throw new Error(
        'Cannot invoke a Lambda with an Event invocation type using invokeWithResponse'
      );
    }
    try {
      const output = await this.invoke(payload, arn, {
        ...additionalCmdInput,
        InvocationType: 'RequestResponse',
      });

      return this.marshallOutput<T>(output);
    } catch (e) {
      logger.error('Failed to invoke lambda', { error: e, payload, arn, additionalCmdInput });
      throw e;
    }
  }
}
