import httpJsonBodyParser from '@middy/http-json-body-parser';

export const conditionalJsonBodyParser = () => {
  const jsonBodyParser = httpJsonBodyParser();

  return {
    before: async (handler: any) => {
      const method = handler.event.httpMethod;

      // Skip parsing for GET/DELETE/HEAD/OPTIONS methods
      const methodsWithoutBody = ['GET', 'DELETE', 'HEAD', 'OPTIONS'];
      if (methodsWithoutBody.includes(method)) {
        return;
      }

      // Parse the JSON body for other methods
      await jsonBodyParser.before!(handler);
    },
  };
};
