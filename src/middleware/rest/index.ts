import * as middyModule from '@middy/core';
import * as validatorModule from '@middy/validator';
import { Ajv } from 'ajv';
import * as ajvFormats from 'ajv-formats';

import { customErrorHandler } from '@/middleware/rest/customErrorHandler.js';
import { logger } from '@/utils/logger.js';
import { conditionalJsonBodyParser } from './conditionalBodyParser.js';
import { defaultHttpMiddlewares } from './middlewares.js';
import { HttpHandler, HttpMiddifyOptions } from './types.js';

const formats = ajvFormats.default;

/**
 * @param handler - Event Lambda handler
 * @param options - Middleware options
 * @returns
 */
export const httpMiddify = <TBody, TPathParameters, TQueryStringParameters>(
  handler: HttpHandler<TBody, TPathParameters, TQueryStringParameters>,
  {
    schema,
    validatorOptions,
    validatorCallback,
    injectRepositories,
    addHeaders,
  }: HttpMiddifyOptions
) => {
  // Compatibility: middy v6 ESM default export vs CJS interop
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const middy: any = (middyModule as any).default || (middyModule as any).middy || middyModule;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const validator: any = (validatorModule as any).default || validatorModule;

  const wrapped = middy(handler);

  if (schema) {
    logger.debug('Schema specified. Adding json body parser and validator middlewares');

    const ajv = new Ajv({
      strict: true,
      coerceTypes: 'array',
      allErrors: true,
      useDefaults: 'empty',
      messages: true,
      ...validatorOptions,
    });
    // @ts-ignore
    formats(ajv);

    // can use this to call ajv.addSchema(someReferencedSchema) if needed
    if (validatorCallback) {
      validatorCallback(ajv);
    }

    wrapped.use(conditionalJsonBodyParser()).use(
      validator({
        eventSchema: ajv.compile(schema),
      })
    );
  }

  wrapped.use(
    defaultHttpMiddlewares(schema, {
      injectRepositories: injectRepositories ?? true,
      addHeaders: addHeaders ?? true,
    })
  );
  wrapped.use(customErrorHandler());

  return wrapped;
};
