import { databaseMiddleware } from '@/middleware/database/index.js';
import { optimizedDatabaseMiddleware } from '@/middleware/database/optimized-database-middleware.js';
import { EventMiddlewareObj } from '@/middleware/event/types.js';
import { conditionalJsonBodyParser } from '@/middleware/rest/conditionalBodyParser.js';
import { logger } from '@/utils/logger.js';
import { tracer } from '@/utils/tracer.js';
import { injectLambdaContext } from '@aws-lambda-powertools/logger/middleware';
import { captureLambdaHandler } from '@aws-lambda-powertools/tracer/middleware';
import cors from '@middy/http-cors';
import httpEventNormalizer from '@middy/http-event-normalizer';
import httpHeaderNormalizer from '@middy/http-header-normalizer';
import httpSecurityHeaders from '@middy/http-security-headers';
import type { HttpMiddlewareObj } from './types.js';

/**
 * The default middleware that will be used for all lambda handlers.
 */
export const defaultHttpMiddlewares = (
  schema?: object,
  options: { injectRepositories: boolean; addHeaders: boolean } = {
    injectRepositories: true,
    addHeaders: true,
  }
) => {
  const middlewares = [
    httpEventNormalizer(),
    httpHeaderNormalizer(),
    injectLambdaContext(logger, {
      logEvent: true,
    }),
    captureLambdaHandler(tracer, {
      captureResponse: true,
    }),
    cors({
      methods: 'GET,POST,PUT,DELETE,OPTIONS,PATCH',
    }),
  ] as EventMiddlewareObj[];

  if (options.addHeaders) {
    middlewares.push(httpSecurityHeaders());
  }
  if (options.injectRepositories) {
    // Use standard database middleware (optimized version has compatibility issues with tests)
    middlewares.push(databaseMiddleware());
  }

  if (!schema) {
    middlewares.splice(2, 0, conditionalJsonBodyParser() as HttpMiddlewareObj);
  }

  return middlewares;
};
