/* eslint-disable @typescript-eslint/no-explicit-any */
import { buildResponse } from '@/utils/buildResponse.js';
import { logger } from '@/utils/logger.js';
import { MiddlewareObj, Request } from '@middy/core';
import type { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';

export interface ResponseCodeObject {
  httpStatusCode: number;
  body: string;
}

export type CustomHttpErrorHandleOptions = {
  responseCodes: Record<string, ResponseCodeObject>;
  schemaValidationResponseId?: string;
  genericFailureResponseId?: string;
};

type ErrorWithCause = Error & { cause: { data: { message?: string; instancePath?: string }[] } };

/**
 * Handles converting custom errors thrown by the lambda handle into a pre-defined response.
 *
 * @param options
 */
export const customErrorHandler = (): MiddlewareObj<
  APIGatewayProxyEvent,
  APIGatewayProxyResult
> => {
  logger.debug('Utilising the customHttpErrorHandle middleware using the given response codes');

  const buildValidationFailureResponse = (err: ErrorWithCause) => {
    const errorDetails = err.cause.data.map((c) => ({
      message: c.message,
      instancePath: c.instancePath,
    }));

    return buildResponse(422, JSON.stringify(errorDetails));
  };

  const buildGenericFailureResponse = (message?: string) => {
    return buildResponse(500, message || 'Internal Server Error');
  };

  const onError = (request: Request): void => {
    if (request.response !== undefined) {
      // response was already set skip middleware
      return;
    }

    if (request.error) {
      if (request.error.cause) {
        logger.error(request.error.message, { cause: request.error.cause });
      } else {
        logger.error(request.error.message);
      }
    }

    const { error } = request;

    // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
    if (error && (error as any).status === 400 && error.name === 'BadRequestError' && error.cause) {
      // validation error
      request.response = buildValidationFailureResponse(error as ErrorWithCause);
    } else {
      request.response = buildGenericFailureResponse(request?.error?.message);
    }
  };
  return {
    onError,
  };
};
