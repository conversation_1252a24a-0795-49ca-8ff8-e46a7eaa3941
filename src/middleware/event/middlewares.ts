import { databaseMiddleware } from '@/middleware/database/index.js';
import { optimizedDatabaseMiddleware } from '@/middleware/database/optimized-database-middleware.js';
import { EventMiddlewareObj } from '@/middleware/event/types.js';
import { logger } from '@/utils/logger.js';
import { tracer } from '@/utils/tracer.js';
import { injectLambdaContext } from '@aws-lambda-powertools/logger/middleware';
import { captureLambdaHandler } from '@aws-lambda-powertools/tracer/middleware';
import eventNormalizer from '@middy/event-normalizer';

export const defaultEventMiddlewares = function (options: { injectRepositories: boolean }) {
  const middlewares = [
    eventNormalizer(),
    injectLambdaContext(logger, {
      logEvent: true,
    }),
    captureLambdaHandler(tracer, {
      captureResponse: true,
    }),
  ] as EventMiddlewareObj[];
  if (options.injectRepositories) {
    // Use standard database middleware (optimized version has compatibility issues with tests)
    middlewares.push(databaseMiddleware());
  }
  return middlewares;
};
