import { defaultEventMiddlewares } from '@/middleware/event/middlewares.js';
import { EventHandler } from '@/middleware/event/types.js';
import * as middyModule from '@middy/core';
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const middy: any = (middyModule as any).default || (middyModule as any).middy || middyModule;

/**
 * @param handler - Event Lambda handler
 * @param options - Not used currently
 * @returns
 */
export const eventMiddify = <TEvent, TResult>(
  handler: EventHandler<TEvent, TResult>,
  options?: { injectRepositories: boolean }
) => {
  const wrapped = middy(handler);

  wrapped.use(defaultEventMiddlewares(options || { injectRepositories: true }));

  return wrapped;
};
