import { Repositories } from '@/middleware/database/types.js';
import type { MiddlewareObj as MiddyMiddlewareObj } from '@middy/core';
import type { Handler as AWSHandler, Context } from 'aws-lambda';

// Define a type for events with context that includes repositories
export interface EventWithRepositories {
  context: Context & {
    repositories: Repositories;
  };
}

// Generic event handler type that ensures context.repositories is available
export type EventHandler<TEvent = any, TResult = any> = AWSHandler<
  TEvent & EventWithRepositories,
  TResult
>;
export type EventMiddlewareObj<TEvent = any, TResult = any> = MiddyMiddlewareObj<TEvent, TResult>;
