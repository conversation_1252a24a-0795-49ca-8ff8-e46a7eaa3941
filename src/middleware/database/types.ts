import { IFixtureRepository } from '@/storage-interface/fixtures/fixture-repository.interface.js';
import { GameworldRepository } from '@/storage-interface/gameworld/index.js';
import { InboxRepository } from '@/storage-interface/inbox/index.js';
import { LeagueRepository } from '@/storage-interface/leagues/league-repository.interface.js';
import { ManagerRepository } from '@/storage-interface/managers/manager-repository.interface.js';
import { PlayerRepository } from '@/storage-interface/players/index.js';
import { PurchaseRepository } from '@/storage-interface/purchases/index.js';
import { ScoutingRequestRepository } from '@/storage-interface/scouting-requests/index.js';
import { ScoutingRepository } from '@/storage-interface/scouting/index.js';
import { TeamRepository } from '@/storage-interface/teams/team-repository.interface.js';
import { TeamTrainingSlotRepository } from '@/storage-interface/training/training-repository.interface.js';
import { TransferRepository } from '@/storage-interface/transfers/index.js';

/**
 * Repositories that are injected into the Lambda context
 */
export interface Repositories {
  leagueRepository: LeagueRepository;
  teamRepository: TeamRepository;
  fixtureRepository: IFixtureRepository;
  playerRepository: PlayerRepository;
  managerRepository: ManagerRepository;
  purchaseRepository: PurchaseRepository;
  scoutingRepository: ScoutingRepository;
  scoutingRequestRepository: ScoutingRequestRepository;
  gameworldRepository: GameworldRepository;
  transferRepository: TransferRepository;
  inboxRepository: InboxRepository;
  trainingRepository: TeamTrainingSlotRepository;
}

/**
 * Environment variables used in the database middleware
 */
declare global {
  namespace NodeJS {
    interface ProcessEnv {
      DATABASE_TYPE?: string;
      LEAGUES_TABLE_NAME?: string;
      TEAMS_TABLE_NAME?: string;
      FIXTURES_TABLE_NAME?: string;
      MANAGERS_TABLE_NAME?: string;
    }
  }
}
