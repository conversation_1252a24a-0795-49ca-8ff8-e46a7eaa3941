import { Repositories } from '@/middleware/database/types.js';
import { EventWithRepositories } from '@/middleware/event/types.js';
import {
  getFixtureRepository,
  getGameworldRepository,
  getInboxRepository,
  getLeagueRepository,
  getManagerRepository,
  getMikroOrmService,
  getPlayerRepository,
  getPurchaseRepository,
  getScoutingRepository,
  getScoutingRequestRepository,
  getTeamRepository,
  getTrainingRepository,
  getTransferRepository,
  initializeDatabase,
} from '@/storage-interface/database-initializer.js';
import { logger } from '@/utils/logger.js';
import { Request } from '@middy/core';

import { performance } from 'perf_hooks';

// We'll use the EventWithRepositories interface from event/types.ts

/**
 * Middleware that initializes the database connection and injects repository instances
 * into the event context.
 */
export const databaseMiddleware = () => {
  return {
    before: (request: Request<EventWithRepositories>): Promise<void> => {
      logger.debug('Initializing database connection');

      const startTotal = performance.now();
      let startRepoCreate: number, endRepoCreate: number, endInject: number;

      return initializeDatabase()
        .then(() => {
          startRepoCreate = performance.now();
          // Create repositories and inject them into the context
          return Promise.all([
            getLeagueRepository(),
            getTeamRepository(),
            getFixtureRepository(),
            getPlayerRepository(),
            getManagerRepository(),
            getPurchaseRepository(),
            getScoutingRepository(),
            getScoutingRequestRepository(),
            getGameworldRepository(),
            getTransferRepository(),
            getInboxRepository(),
            getTrainingRepository(),
            getMikroOrmService(),
          ]);
        })
        .then(
          ([
            leagueRepository,
            teamRepository,
            fixtureRepository,
            playerRepository,
            managerRepository,
            purchaseRepository,
            scoutingRepository,
            scoutingRequestRepository,
            gameworldRepository,
            transferRepository,
            inboxRepository,
            trainingRepository,
          ]) => {
            endRepoCreate = performance.now();
            const repositories: Repositories = {
              leagueRepository,
              teamRepository,
              fixtureRepository,
              playerRepository,
              managerRepository,
              purchaseRepository,
              scoutingRepository,
              scoutingRequestRepository,
              gameworldRepository,
              transferRepository,
              inboxRepository,
              trainingRepository,
            };

            // Ensure context exists
            if (!request.event.context) {
              // eslint-disable-next-line @typescript-eslint/ban-ts-comment
              // @ts-ignore
              request.event.context = {};
            }

            // Add repositories to context
            request.event.context.repositories = repositories;
            endInject = performance.now();

            logger.debug(`Database connection initialized and repositories injected`);
            logger.info(
              `Timing: DB init: ${(startRepoCreate - startTotal).toFixed(2)}ms, Repo create: ${(endRepoCreate - startRepoCreate).toFixed(2)}ms, Inject: ${(endInject - endRepoCreate).toFixed(2)}ms, Total: ${(endInject - startTotal).toFixed(2)}ms`
            );
          }
        )
        .catch((error) => {
          logger.error('Failed to initialize database connection', { error });
          // Create a more helpful error message
          const enhancedError = new Error(
            `Database initialization failed: ${error instanceof Error ? error.message : 'Unknown error'}`
          );
          if (error instanceof Error && error.stack) {
            enhancedError.stack = error.stack;
          }
          throw enhancedError;
        });
    },
    after: async (): Promise<void> => {
      // close the database connection if needed
      const mikroOrmService = await getMikroOrmService();
      if (mikroOrmService) {
        await mikroOrmService.close();
      }
    },
    onError: async (request: Request<EventWithRepositories>): Promise<void> => {
      // Optionally close the database connection on error
      const mikroOrmService = await getMikroOrmService();
      if (mikroOrmService) {
        await mikroOrmService.close();
      }
    },
  };
};
