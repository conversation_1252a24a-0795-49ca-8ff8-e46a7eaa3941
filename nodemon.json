{"watch": ["src/monolith", "src/functions", "src/entities", "src/utils", "src/services", "src/middleware", "src/storage-interface"], "ext": "ts,js,json", "ignore": ["dist/*", "coverage/*", "layer/*", "temp/*", "node_modules/*", "scripts/*", "reports/*"], "delay": 150, "exec": "node --experimental-specifier-resolution=node --loader ./src/testing/loader.js ./src/monolith/server.ts", "signal": "SIGINT", "env": {"TS_NODE_TRANSPILE_ONLY": "1", "NODE_ENV": "development"}}