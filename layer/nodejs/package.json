{"name": "jfg-lambda-layer", "version": "1.0.0", "dependencies": {"@mikro-orm/core": "^6.4.16", "@mikro-orm/postgresql": "^6.4.16", "@mikro-orm/migrations": "^6.4.16", "@mikro-orm/reflection": "^6.4.16", "reflect-metadata": "^0.2.2", "pg": "^8.16.3", "pg-query-stream": "^4.8.1", "@aws-sdk/client-dynamodb": "^3.873.0", "@aws-sdk/client-ses": "^3.744.0", "@aws-sdk/client-sns": "^3.744.0", "@aws-sdk/client-sqs": "^3.875.0", "@aws-sdk/lib-dynamodb": "^3.873.0", "@aws-lambda-powertools/logger": "^2.24.1", "@aws-lambda-powertools/tracer": "^2.24.1", "aws-jwt-verify": "^5.0.0", "axios": "^1.9.0", "date-fns": "^4.1.0", "expo-server-sdk": "^3.15.0", "google-auth-library": "^9.15.1", "obscenity": "^0.4.3", "seedrandom": "^3.0.5", "uuid": "^11.1.0"}}