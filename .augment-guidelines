This project is a backend microservice API for a massively multiplayer football management game.
The infrastructure is deployed using Pulumi. All deployment code is in the infrastructure/ folder


### While Coding

- ** (SHOULD NOT)** Introduce classes when small testable functions suffice.
- ** (SHOULD)** Prefer simple, composable, testable functions.
- ** (MUST)** Use `import type { … }` for type-only imports.
- ** (SHOULD NOT)** Add comments only for complex logic; rely on self‑explanatory code.
- ** (SHOULD)** Default to `interface`; use `type` only when required.
- ** (SHOULD NOT)** Extract a new function unless it will be reused elsewhere, is the only way to unit-test otherwise untestable logic, or drastically improves readability of an opaque block.

---

### Testing

- **(MUST)** Colocate unit tests in `*.test.ts` in same directory as source file.
- **(SHOULD)** Prefer integration tests over heavy mocking.
- **(SHOULD)** Unit-test complex algorithms thoroughly.
- **(SHOULD)** Test the entire structure in one assertion if possible
  ```ts
  expect(result).toBe([value]) // Good

  expect(result).toHaveLength(1); // Bad
  expect(result[0]).toBe(value); // Bad
  ```
- **(MUST)** Use factories in src/testing/factories for creating mocked entities.
- **(MUST)** Assume that any calls to handler functions will run middleware that will inject repositories into the event context.
- **(MUST)** Reference the mocks in src/testing/mockRepositories.ts when mocking repositories.

---

### Database

- **(MUST)** Use mikro-orm for database access unless data is complex and non relational where DynamoDB would be a better option.
- **(MUST)** Use repositories for database access.
- **(MUST)** Use entities for database models.
- **(MUST)** Not generate migrations manually. Use `npm run migration:generate` instead
- **(MUST)** Use factories in src/testing/factories for creating mocked entities.
- **(MUST)** Use mock repositories in src/testing/mockRepositories.ts for testing.

---

### 6 — Tooling Gates

- **(MUST)** `npm run prettier:fix` passes.
- **(MUST)** `npm run typecheck` passes.

---

## Writing Functions Best Practices

When evaluating whether a function you implemented is good or not, use this checklist:

1. Can you read the function and HONESTLY easily follow what it's doing? If yes, then stop here.
2. Does the function have very high cyclomatic complexity? (number of independent paths, or, in a lot of cases, number of nesting if if-else as a proxy). If it does, then it's probably sketchy.
3. Are there any common data structures and algorithms that would make this function much easier to follow and more robust? Parsers, trees, stacks / queues, etc.
4. Are there any unused parameters in the function?
5. Are there any unnecessary type casts that can be moved to function arguments?
6. Is the function easily testable without mocking core features (e.g. sql queries, redis, etc.)? If not, can this function be tested as part of an integration test?
7. Does it have any hidden untested dependencies or any values that can be factored out into the arguments instead? Only care about non-trivial dependencies that can actually change or affect the function.
8. Brainstorm 3 better function names and see if the current name is the best, consistent with rest of codebase.

IMPORTANT: you SHOULD NOT refactor out a separate function unless there is a compelling need, such as:
  - the refactored function is used in more than one place
  - the refactored function is easily unit testable while the original function is not AND you can't test it any other way
  - the original function is extremely hard to follow and you resort to putting comments everywhere just to explain it

## Writing Tests Best Practices

When evaluating whether a test you've implemented is good or not, use this checklist:

1. SHOULD parameterize inputs; never embed unexplained literals such as 42 or "foo" directly in the test.
2. SHOULD NOT add a test unless it can fail for a real defect. Trivial asserts (e.g., expect(2).toBe(2)) are forbidden.
3. SHOULD ensure the test description states exactly what the final expect verifies. If the wording and assert don’t align, rename or rewrite.
4. SHOULD compare results to independent, pre-computed expectations or to properties of the domain, never to the function’s output re-used as the oracle.
5. SHOULD follow the same lint, type-safety, and style rules as prod code (prettier, ESLint, strict types).
6. Unit tests for a function should be grouped under `describe(functionName, () => ...`.
7. Use `expect.any(...)` when testing for parameters that can be anything (e.g. variable ids).
8. ALWAYS use strong assertions over weaker ones e.g. `expect(x).toEqual(1)` instead of `expect(x).toBeGreaterThanOrEqual(1)`.
9. SHOULD test edge cases, realistic input, unexpected input, and value boundaries.
10. SHOULD NOT test conditions that are caught by the type checker.
11. Use vitest

## Code Organization

- `infrastructure/` - Pulumi AWS deploy logic
  - `infrastructure/resources/*.ts` - Resource definitions to deploy
- `scripts` - Build tools
- `src/entities/` - Mikro-orm entities
- `src/functions/` - Lambda functions
- `src/middleware/` - Lambda middy middleware
- `src/services/` - Business logic services
- `src/storage-interface/` - Repositories for accessing data
- `src/testing/` - Testing utilities
- `src/types/` - Generated types from OpenAPI schema
- `src/utils/` - Utility functions