{"$schema": "https://json.schemastore.org/tsconfig", "extends": "@tsconfig/node-lts/tsconfig.json", "exclude": ["node_modules", "dist", "coverage"], "compilerOptions": {"allowJs": true, "allowImportingTsExtensions": true, "noEmit": true, "noImplicitAny": true, "noUncheckedIndexedAccess": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "resolveJsonModule": true, "sourceMap": true, "moduleResolution": "NodeNext", "module": "NodeNext", "target": "ES2022", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "skipLibCheck": true, "downlevelIteration": true, "paths": {"@/*": ["./src/*"]}, "typeRoots": ["./node_modules/@types"]}, "include": ["src", "infrastructure"], "ts-node": {"esm": true, "transpileOnly": true, "experimentalSpecifiers": true}}